# Lucy Api
Lucy Api is a RESTful Spring Boot server that exposes functionality from lucy-javaee7 application.

## Getting Started

### Prerequisites

### Build

```bash
gradlew -Pprofile=<profile> clean build docker
```
The project property *profile* is used to select the proper jboss-ejb-client-<profile>.properties.
If the property is not specified the file jboss-ejb-client.properties is selected.

## Authors

* **tobym** - *Business requrements* - [Support Item Barcodes for Packing Items](https://peterpal.atlassian.net/browse/FFSW-1341)
* **stoyanp** - *Initial work*

## License

This project is property of Peter Pal Library Supplier Ltd.
