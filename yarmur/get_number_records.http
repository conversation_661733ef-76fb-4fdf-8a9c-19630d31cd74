# curl 'https://murrindinditest.spydus.com/spydus/api/Marc/GetNumRecords?Filename=73321d32-b7e0-4215-87e9-154f3cddfd78.mrx&_=1718085269157'
#  -H 'sec-ch-ua: "Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"'
#  -H 'X-XSRF-TOKEN: CfDJ8Kk7U8lZV5hHllgnmhE_U0j0El4lLv_S3DtDIkWtStnb4fMsZLAz1e76-6nFKvPfncpfmxcU3Z6XTZqt9EK-JHh5-1Ehc5WgPd0sCb72-kQngE1qO8gPlgLrwZOMJdUmAKcsgybrUZrS0ix8dKAnxXU'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/125.0.0.0 Safari/537.36'
#  -H 'Accept: */*'
#  -H 'Referer: https://murrindinditest.spydus.com/spydus'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua-platform: "Windows"'
GET https://murrindinditest.spydus.com/spydus/api/Marc/GetNumRecords?Filename=deca3869-e5ae-4b8c-b382-f04aee176008.mrx&
    _=1718085269157
sec-ch-ua: "Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"
X-XSRF-TOKEN: CfDJ8Kk7U8lZV5hHllgnmhE_U0gO-eaRi5lSeJWFHv4Z24BCXbz6B3Coz4fEeJYnHpAM3pjz38nDupW_jehYlP9v15Mi7nYc0nk9W_EkPar_HqagFiEq-6d4meKfUxI525UU5dEfBNP06tGWdx7FoI2_tsQ
sec-ch-ua-mobile: ?0
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36
Accept: */*
Referer: https://murrindinditest.spydus.com/spydus
X-Requested-With: XMLHttpRequest
sec-ch-ua-platform: "Windows"

###

