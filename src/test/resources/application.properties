file.export.dir=./batch-export

spring.datasource.platform=h2
spring.datasource.url=jdbc:h2:mem:lucydb;IGNORECASE=TRUE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.jpa.hibernate.ddl-auto = none
spring.jpa.hibernate.dialect = org.hibernate.dialect.H2Dialect

cataloguing.datasource.platform=h2
cataloguing.datasource.url=jdbc:h2:mem:cataloguingdb;IGNORECASE=TRUE
cataloguing.datasource.username=sa
cataloguing.datasource.password=
cataloguing.datasource.driverClassName=org.h2.Driver
cataloguing.jpa.hibernate.ddl-auto = none
cataloguing.jpa.hibernate.dialect = org.hibernate.dialect.H2Dialect

# Keycloak properties
keycloak.realm = ppls-dev
keycloak.resource = lucy-api
keycloak.auth-server-url = http://keycloak.k8s-test.peterpal.local/auth
keycloak.ssl-required = external
keycloak.bearer-only = true
keycloak.cors = true
keycloak.credentials.secret = d9d31185-7021-486c-a732-59da0c544061
ppls.keycloak.client-id=lucy-ui
# Switch on/off security
security.enabled=true