file.export.dir=./batch-export

spring.datasource.platform=h2
spring.datasource.url=jdbc:h2:mem:lucydb;IGNORECASE=TRUE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.jpa.hibernate.ddl-auto = none
spring.jpa.hibernate.dialect = org.hibernate.dialect.H2Dialect


cataloguing.datasource.platform=sqlserver
cataloguing.datasource.url=************************************************
cataloguing.datasource.username=lucy
cataloguing.datasource.password=compact
cataloguing.datasource.hikari.connection-test-query=SELECT 1
cataloguing.datasource.driverClassName=net.sourceforge.jtds.jdbc.Driver

cataloguing.jpa.hibernate.ddl-auto = none
cataloguing.jpa.hibernate.dialect = org.hibernate.dialect.SQLServerDialect
