package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.batch.control.BatchService;
import au.com.peterpal.lucyapi.batch.dto.SearchParams;
import au.com.peterpal.lucyapi.batch.model.BatchInfo;
import au.com.peterpal.lucyapi.batch.model.ExportInfo;
import au.com.peterpal.lucyapi.core.service.spydus.SpydusClient;
import au.com.peterpal.lucyapi.model.BibRecordType;
import au.com.peterpal.lucyapi.persistence.cataloguing.BatchJdbcTemplateRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.LMSConfigurationRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.SeparateBatchByHoldingRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.BatchScheduleType;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.LMSConfiguration;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.LMSType;
import lucy.cataloguing.codes.BibRecordBatchStatus;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class LMSServiceTest {
    @MockBean
    private TitleOrderService titleOrderService;

    @MockBean
    private SpydusClient spydusClient;

    @MockBean
    private LMSConfigurationRepository lmsConfigurationRepository;

    @MockBean
    private BatchService batchService;

    @MockBean
    private SeparateBatchByHoldingRepository separateBatchByHoldingRepository;

    @MockBean
    private BatchJdbcTemplateRepository batchJdbcTemplateRepository;

    @MockBean
    private JiraWebhookService jiraWebhookService;

    private LMSService lmsService;
    @MockBean
    private EmailMarcRecordsService emailMarcRecordsService;


    @Before
    public void setup() {
        String fileExportDir = "";
        String endOfDayTime = "16:30";
        String statusTypeSuccess = "5, 6 ,7";
        lmsService = new LMSService(titleOrderService,
                spydusClient,
                lmsConfigurationRepository,
                batchService,
                separateBatchByHoldingRepository,
                batchJdbcTemplateRepository,
                jiraWebhookService,
                emailMarcRecordsService,
                fileExportDir,
                endOfDayTime,
                statusTypeSuccess);
    }

    @Test
    public void testUploadBibRecordsToSpydus() {
        LMSConfiguration lmsConfiguration = new LMSConfiguration();
        String customerCode = "BCC";
        lmsConfiguration.setCustomerCode(customerCode);
        lmsConfiguration.setBatchSchedule(BatchScheduleType.EVERY_QUARTER_HOUR);

        List<BatchInfo> batchInfos = new ArrayList<>();
        BatchInfo batchInfo = BatchInfo.builder()
            .id(123456)
            .bibType(BibRecordType.ONORDER)
            .customerCode(customerCode)
            .status(BibRecordBatchStatus.OPEN)
            .description("On order batch for Brisbane")
            .build();
        batchInfos.add(batchInfo);
        List<Integer> batchIdList = Collections.singletonList(123456);
        String filename = "PPLS_211797_HUTT_20230809.mrc";
        ExportInfo exportInfo = ExportInfo.of(123, filename);
        String sessionId = "df02565f-627f-4e4b-8ade-2d6da1b36688";

        when(lmsConfigurationRepository.findAllByLmsTypeAndAutomaticallyCloseBatchesIsTrue(LMSType.SPYDUS))
            .thenReturn(Collections.singletonList(lmsConfiguration));
        when(batchService.find(any(SearchParams.class))).thenReturn(batchInfos);

        when(batchService.export(batchIdList)).thenReturn(Collections.singletonList(exportInfo));
        when(spydusClient.uploadBatchWithRetry(any(), any()))
            .thenReturn("{\"isMrxFile\":true,\"SessionId\":\"df02565f-627f-4e4b-8ade-2d6da1b36688\"}");
        when(spydusClient.getNumberRecordsWithRetry(customerCode, sessionId)).thenReturn(1);
        // Act
        lmsService.uploadBibRecordsToSpydus();

        // Assert
        verify(batchService, times(2)).update(batchInfos);
        verify(batchService, times(1)).export(batchIdList);
        verify(spydusClient, times(1)).uploadBatchWithRetry(any(), any());
        verify(spydusClient, times(1)).getNumberRecordsWithRetry(customerCode, sessionId);
        verify(spydusClient, times(1)).getBatchMarcsBriefWithRetry(customerCode, sessionId, filename);
        verify(spydusClient, times(1)).processBatchWithRetry(customerCode, sessionId, 1, 1);

    }

    @Test(expected = BusinessException.class)
    public void shouldThrowExceptionWhenSendBibRecordsToEmailWithLmsConfigurationNotFoundByCustomerCode() {
        String customerCode = "BCC";
        List<Integer> batchIdList = Collections.singletonList(123456);

        when(lmsConfigurationRepository.findByCustomerCodeAndLmsType(any(), any())).thenReturn(Optional.empty());

        lmsService.sendBibRecordsToEmail(customerCode, batchIdList);
    }

    @Test
    public void testSendBibRecordsToEmailWithBatchIdListIsEmptyAndNotFoundAnyCloseBatchInfo() {
        LMSConfiguration lmsConfiguration = new LMSConfiguration();
        String customerCode = "BCC";
        lmsConfiguration.setCustomerCode(customerCode);

        List<BatchInfo> batchInfos = new ArrayList<>();
        List<Integer> batchIdList = new ArrayList<>();

        when(lmsConfigurationRepository.findByCustomerCodeAndLmsType(any(), any())).thenReturn(Optional.of(lmsConfiguration));
        when(batchService.find(any())).thenReturn(batchInfos);

        lmsService.sendBibRecordsToEmail(customerCode, batchIdList);

        verify(batchService, times(2)).find(any());
        verify(emailMarcRecordsService, times(0)).sendEmail(any(), any(), any());
        verify(batchService, times(0)).export(any());
        verify(batchService, times(0)).update(any());
    }

    @Test
    public void testSendBibRecordsToEmailWithBatchIdListIsNEmptyAndClosedBatchInfosIsNotEmpty() {
        LMSConfiguration lmsConfiguration = new LMSConfiguration();
        String customerCode = "BCC";
        lmsConfiguration.setCustomerCode(customerCode);

        List<BatchInfo> batchInfos = Lists.newArrayList(BatchInfo.builder()
                .id(123456)
                .bibType(BibRecordType.ONORDER)
                .customerCode(customerCode)
                .status(BibRecordBatchStatus.CLOSED)
                .description("On order batch for Brisbane")
                .build());
        String filename = "PPLS_211797_HUTT_20230809.mrc";
        ExportInfo exportInfo = ExportInfo.of(123, filename);

        when(lmsConfigurationRepository.findByCustomerCodeAndLmsType(any(), any())).thenReturn(Optional.of(lmsConfiguration));
        when(batchService.find(any())).thenReturn(batchInfos);
        when(batchService.export(any())).thenReturn(Collections.singletonList(exportInfo));

        lmsService.sendBibRecordsToEmail(customerCode, new ArrayList<>());

        verify(batchService, times(2)).find(any());
        verify(emailMarcRecordsService, times(1)).sendEmail(any(), any(), any());
        verify(batchService, times(1)).export(any());
        verify(batchService, times(2)).update(any());
    }

    @Test
    public void testSendBibRecordsToEmailWithBatchIdListIsNotEmpty() {
        LMSConfiguration lmsConfiguration = new LMSConfiguration();
        String customerCode = "BCC";
        lmsConfiguration.setCustomerCode(customerCode);

        BatchInfo batchInfo = BatchInfo.builder()
                .id(123456)
                .bibType(BibRecordType.ONORDER)
                .customerCode(customerCode)
                .status(BibRecordBatchStatus.CLOSED)
                .description("On order batch for Brisbane")
                .build();
        List<Integer> batchIdList = Collections.singletonList(123456);
        String filename = "PPLS_211797_HUTT_20230809.mrc";
        ExportInfo exportInfo = ExportInfo.of(123, filename);

        when(lmsConfigurationRepository.findByCustomerCodeAndLmsType(any(), any())).thenReturn(Optional.of(lmsConfiguration));
        when(batchService.getBatch(any())).thenReturn(batchInfo);
        when(batchService.export(any())).thenReturn(Collections.singletonList(exportInfo));

        lmsService.sendBibRecordsToEmail(customerCode, batchIdList);

        verify(batchService, times(1)).getBatch(any());
        verify(emailMarcRecordsService, times(1)).sendEmail(any(), any(), any());
        verify(batchService, times(1)).export(any());
        verify(batchService, times(1)).update(any());
    }

}
