package au.com.peterpal.lucyapi.core.service;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.core.IsNull.notNullValue;

import au.com.peterpal.lucyapi.model.BarcodeInfo;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lucy.catalogue.codes.CurrencyCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.Acquisition;
import lucy.cataloguing.entity.Copy;
import lucy.cataloguing.entity.Receipt;
import lucy.common.NotFoundException;
import lucy.fulfillment.beans.RemoteFulfillment;
import lucy.fulfillment.entity.Customer;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class BarcodeServiceTest {

  @TestConfiguration
  static class BarcodeServiceTestConfiguration {

    @MockBean
    public RemoteCataloguing cataloguing;

    @MockBean
    public RemoteFulfillment fulfillment;

    @Bean
    public BarcodeService barcodeService() {
      return new BarcodeService(cataloguing, fulfillment);
    }
  }

  @Autowired
  private RemoteCataloguing cataloguing;

  @Autowired
  private RemoteFulfillment fulfillment;

  @Autowired
  private BarcodeService barcodeService;

  @Before
  public void setUp() throws Exception {
    Acquisition acquisition = getAcquisition();

    Receipt receipt = getReceipt("324567");
    receipt.setAcquisition(acquisition);

    Customer customer = new Customer();
    customer.setName("Townsville");

    Mockito.doReturn(receipt).when(cataloguing).findReceipt("324567");
    Mockito.doReturn(acquisition).when(cataloguing).getAcquisition(1);
    Mockito.doReturn(customer).when(fulfillment).getCustomer(Mockito.any(OrganisationIdentifier.class));
  }

  @Test
  public void barcodeServiceNotNullTest() {
    assertThat(barcodeService, is(notNullValue()));
  }

  @Test
  public void whenValidWorkOrderNumber_thenBarcodeInfoList() throws NotFoundException {
    List<BarcodeInfo> info = barcodeService.getBarcodeInfo("324567");

    assertThat(info, is(notNullValue()));
    assertThat(info.size(), is(2));
    assertThat(info.get(0).getBarcode(), is("123456783389"));
    assertThat(info.get(0).getBranchCode(), is("KINGS CROSS"));
    assertThat(info.get(0).getBranchName(), is("Townsville"));
    assertThat(info.get(1).getBarcode(), is("771234567003"));
    assertThat(info.get(1).getBranchCode(), is("WATERLOO"));
    assertThat(info.get(1).getBranchName(), is("Townsville"));
  }

  private Receipt getReceipt(String workOrder)
      throws NoSuchFieldException, IllegalAccessException {
    Receipt receipt = new Receipt();
    Class<?> clazz = receipt.getClass();

    setField(receipt, clazz, "pk", 1);
    setField(receipt, clazz, "netSalePriceCurrency", CurrencyCode.AUD);

    receipt.setOrderAssignmentId(workOrder);
    receipt.setQtyReceived(2);
    receipt.setNetSalePriceAmount(new BigDecimal(36.17));
    return receipt;
  }

  private Acquisition getAcquisition()
      throws NoSuchFieldException, IllegalAccessException {
    Acquisition acquisition = new Acquisition();
    Class<?> clazz = acquisition.getClass();

    setField(acquisition, clazz, "pk", 1);
    setField(acquisition, clazz, "copies", getCopies());

    return acquisition;
  }

  private void setField(Object obj, Class<?> clazz, String name, Object value)
      throws NoSuchFieldException, IllegalAccessException {
    Field fldPk = clazz.getDeclaredField(name);
    fldPk.setAccessible(true);
    fldPk.set(obj, value);
    fldPk.setAccessible(false);
  }

  private List<Copy> getCopies() throws NoSuchFieldException, IllegalAccessException {
    List<Copy> copies = new ArrayList<>();

    Copy copy = new Copy();
    Class<?> clazz = copy.getClass();
    setField(copy, clazz, "pk", 1);
    copy.setBarCodeNumber("123456783389");
    copy.setBranchCode("KINGS CROSS");
    copies.add(copy);

    copy = new Copy();
    clazz = copy.getClass();
    setField(copy, clazz, "pk", 2);
    copy.setBarCodeNumber("771234567003");
    copy.setBranchCode("WATERLOO");
    copies.add(copy);
    return copies;
  }
}
