package au.com.peterpal.lucyapi.core.service;

import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EC_MORE_THAN_ONE_TO;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.PN_ERROR_CODE;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalToIgnoringCase;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.junit.Assert.fail;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.TestData;
import au.com.peterpal.lucyapi.model.SearchCriteria;
import au.com.peterpal.lucyapi.model.TitleOrderInfo;
import au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository;
import au.com.peterpal.lucyapi.persistence.lucy.TitleOrderActionInfoRepository;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import javax.sql.DataSource;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.fulfillment.beans.RemoteFulfillment;
import lucy.fulfillment.codes.CataloguingAction;
import lucy.fulfillment.entity.OpenTitleOrder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class TitleOrderServiceTest {

  private TitleOrderService titleOrderService;
  private TitleOrderService titleOrderServiceSpy;

  @MockBean
  private RemoteCataloguing cataloguing;

  @MockBean
  private RemoteFulfillment fulfilment;

  @MockBean
  private TitleOrderActionInfoRepository repo;

  private LucyHelperRepository helperRepo;
  private LucyHelperRepository spyed;

  @Mock
  private SimpleJdbcCall simpleJdbcCall;

  private List<OpenTitleOrder> orders;

  @Before
  public void setUp() {
    DataSource dataSource = Mockito.mock(DataSource.class);
    helperRepo = new LucyHelperRepository(dataSource);
    spyed = Mockito.spy(helperRepo);
    titleOrderService = new TitleOrderService(cataloguing, new TestFulfilment(), repo,
        null, spyed);
    titleOrderServiceSpy = Mockito.spy(titleOrderService);
    orders = Arrays.asList(TestData.getOpenTitleOrders());
  }

  @Test
  public void testReadingOpenTitleOrders() {
    assertThat(orders, is(notNullValue()));
    assertThat(orders.size(), is(14));
  }

  @Test
  public void testFilterBySourceCodeProfile() {

    SearchCriteria searchCriteria = SearchCriteria.builder()
        .source("PROFILE")
        .build();
    List<TitleOrderInfo> filteredTitleOrders =
        titleOrderService.getTitleOrdersByState("BCC", searchCriteria, LocalDate.now(), LocalDate.now());

    assertThat(filteredTitleOrders, is(notNullValue()));
    assertThat(filteredTitleOrders.size(), is(4));
  }

  @Test
  public void testFilterByFundCodeF2() {

    SearchCriteria searchCriteria = SearchCriteria.builder()
        .fundCode("F2")
        .build();
    List<TitleOrderInfo> filteredTitleOrders =
        titleOrderService.getTitleOrdersByState("BCC", searchCriteria, LocalDate.now(), LocalDate.now());

    assertThat(filteredTitleOrders, is(notNullValue()));
    assertThat(filteredTitleOrders.size(), is(2));
  }

  @Test
  public void testFilterBySourceCodeEmail() {

    SearchCriteria searchCriteria = SearchCriteria.builder()
        .source("EMAIL")
        .build();
    List<TitleOrderInfo> filteredTitleOrders =
        titleOrderService.getTitleOrdersByState("BCC", searchCriteria, LocalDate.now(), LocalDate.now());

    assertThat(filteredTitleOrders, is(notNullValue()));
    assertThat(filteredTitleOrders.size(), is(3));
  }

  @Test
  public void testFilterBySourceCodeAndFund() {

    SearchCriteria searchCriteria = SearchCriteria.builder()
        .source("PROFILE")
        .fundCode("F2")
        .build();
    List<TitleOrderInfo> filteredTitleOrders =
        titleOrderService.getTitleOrdersByState("BCC", searchCriteria, LocalDate.now(), LocalDate.now());

    assertThat(filteredTitleOrders, is(notNullValue()));
    assertThat(filteredTitleOrders.size(), is(2));
  }

  @Test
  public void testFilterByMediaPaperback() {

    SearchCriteria searchCriteria = SearchCriteria.builder()
        .media("PAPERBACK")
        .build();

    List<TitleOrderInfo> filteredTitleOrders =
        titleOrderService.getTitleOrdersByState("BCC", searchCriteria, LocalDate.now(), LocalDate.now());

    assertThat(filteredTitleOrders, is(notNullValue()));
    assertThat(filteredTitleOrders.size(), is(7));
  }

  @Test
  public void testProcessTOs() {
    Map<String, Object> out = new Hashtable<>();
    out.put(PN_ERROR_CODE, 0);
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    Mockito.doReturn(out).when(simpleJdbcCall).execute(Mockito.any(MapSqlParameterSource.class));
    titleOrderService.process(Arrays.asList("TO123456"));
    Mockito.verify(simpleJdbcCall, Mockito.times(1)).execute(Mockito.any(MapSqlParameterSource.class));
  }

  @Test
  public void testProcessTOsWithNulls() {
    Map<String, Object> out = new Hashtable<>();
    out.put(PN_ERROR_CODE, 0);
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    Mockito.doReturn(out).when(simpleJdbcCall).execute(Mockito.any(MapSqlParameterSource.class));
    titleOrderService.process(Arrays.asList("TO123456", null, "TO875623"));
    Mockito.verify(simpleJdbcCall, Mockito.times(2)).execute(Mockito.any(MapSqlParameterSource.class));
  }

  @Test
  public void testProcessTOsWithSpaces() {
    Map<String, Object> out = new Hashtable<>();
    out.put(PN_ERROR_CODE, 0);
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    Mockito.doReturn(out).when(simpleJdbcCall).execute(Mockito.any(MapSqlParameterSource.class));
    titleOrderService.process(Arrays.asList("TO123456", "TO 347891", "TO875623", null, "TO1092378"));
    Mockito.verify(simpleJdbcCall, Mockito.times(3)).execute(Mockito.any(MapSqlParameterSource.class));
  }

  @Test
  public void testProcessTOsWithRetry() {
    Map<String, Object> out = new Hashtable<>();
    out.put(PN_ERROR_CODE, 0);
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    Mockito.doReturn(out).when(simpleJdbcCall).execute(Mockito.any(MapSqlParameterSource.class));
    Mockito.doThrow(new BusinessException("Test")).when(spyed).processTitleOrder("TO123456");
    titleOrderServiceSpy.setAddToPurchaseOrderRetry(3);
    titleOrderServiceSpy.process(Arrays.asList("TO654321", "TO123456"));
    Mockito.verify(titleOrderServiceSpy, Mockito.times(3)).processList(Mockito.anyList());
  }

  @Test
  public void testProcessWithNullTOs() {
    try {
      titleOrderService.process(null);
      fail("Expected IllegalStateException exception has not been thrown");
    } catch (IllegalArgumentException ex) {
      assertThat(ex.getMessage(), equalToIgnoringCase("list of title numbers must not be null"));
    }
  }

  public static class TestFulfilment extends BaseRemoteFulfilment {

    @Override
    public List<OpenTitleOrder> getTitleOrders(OrganisationIdentifier organisationIdentifier,
        CataloguingAction cataloguingAction, Date date, Date date1) {
      return Arrays.asList(TestData.getOpenTitleOrders());
    }
  }

}
