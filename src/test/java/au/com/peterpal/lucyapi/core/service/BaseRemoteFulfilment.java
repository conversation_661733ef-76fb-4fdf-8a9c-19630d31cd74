package au.com.peterpal.lucyapi.core.service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lucy.catalogue.codes.CurrencyCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.catalogue.entity.Price;
import lucy.catalogue.entity.ProductIdentifier;
import lucy.common.NotFoundException;
import lucy.fulfillment.beans.RemoteFulfillment;
import lucy.fulfillment.codes.CataloguingAction;
import lucy.fulfillment.entity.BasketItem;
import lucy.fulfillment.entity.Branch;
import lucy.fulfillment.entity.ClosedTitleOrder;
import lucy.fulfillment.entity.Customer;
import lucy.fulfillment.entity.CustomerUser;
import lucy.fulfillment.entity.CustomerUserGroup;
import lucy.fulfillment.entity.CustomerUserOrganisation;
import lucy.fulfillment.entity.ExchangeRate;
import lucy.fulfillment.entity.Fund;
import lucy.fulfillment.entity.ItemReceiptLine;
import lucy.fulfillment.entity.OpenTitleOrder;
import lucy.fulfillment.entity.TitleOrder;
import lucy.fulfillment.entity.TitleOrderSearchCriteria;
import lucy.fulfillment.entity.TransactionIdentifier;

public class BaseRemoteFulfilment implements RemoteFulfillment {

  @Override
  public void addCustomer(Customer customer) {

  }

  @Override
  public Customer getCustomer(OrganisationIdentifier organisationIdentifier)
          throws NotFoundException {
    return null;
  }

  @Override
  public void updateCustomer(Customer customer) {

  }

  @Override
  public void removeCustomer(OrganisationIdentifier organisationIdentifier) {

  }

  @Override
  public CustomerUser getCustomerUser(Integer integer) {
    return null;
  }

  @Override
  public CustomerUser getCustomerUser(String s) {
    return null;
  }

  @Override
  public CustomerUserGroup getCustomerUserGroup(Integer integer) {
    return null;
  }

  @Override
  public CustomerUserOrganisation getCustomerUserOrganisation(Integer integer) {
    return null;
  }

  @Override
  public void addBranch(Branch branch) {

  }

  @Override
  public Branch getBranch(OrganisationIdentifier organisationIdentifier)
          throws NotFoundException {
    return null;
  }

  @Override
  public void updateBranch(Branch branch) {

  }

  @Override
  public void removeBranch(OrganisationIdentifier organisationIdentifier) {

  }

  @Override
  public void addFund(Fund fund) {

  }

  @Override
  public Fund getFund(OrganisationIdentifier organisationIdentifier, String s)
          throws NotFoundException {
    return null;
  }

  @Override
  public void updateFund(Fund fund) {

  }

  @Override
  public void removeFund(OrganisationIdentifier organisationIdentifier, String s) {

  }

  @Override
  public List<Fund> getFunds(OrganisationIdentifier organisationIdentifier) {
    return null;
  }

  @Override
  public String getNextTitleOrderNum() {
    return null;
  }

  @Override
  public OpenTitleOrder createTitleOrder(OpenTitleOrder openTitleOrder) {
    return null;
  }

  @Override
  public OpenTitleOrder addTitleOrder(OpenTitleOrder openTitleOrder) {
    return null;
  }

  @Override
  public OpenTitleOrder getTitleOrder(TransactionIdentifier transactionIdentifier)
          throws NotFoundException {
    return null;
  }

  @Override
  public ClosedTitleOrder getClosedTitleOrder(TransactionIdentifier transactionIdentifier)
          throws NotFoundException {
    return null;
  }

  @Override
  public void updateTitleOrder(OpenTitleOrder openTitleOrder) {

  }

  @Override
  public void updateTitleOrder(ClosedTitleOrder closedTitleOrder) {

  }

  @Override
  public void removeTitleOrder(TransactionIdentifier transactionIdentifier) {

  }

  @Override
  public OpenTitleOrder getTitleOrder(String s) throws NotFoundException {
    return null;
  }

  @Override
  public ClosedTitleOrder getClosedTitleOrder(String s) throws NotFoundException {
    return null;
  }

  @Override
  public BasketItem getBasketItem(int i) {
    return null;
  }

  @Override
  public void updateBasketItem(BasketItem basketItem) {

  }

  @Override
  public void removeBasketItem(int i) {

  }

  @Override
  public List<OpenTitleOrder> getTitleOrders(OrganisationIdentifier organisationIdentifier,
                                             Date date, Date date1) {
    return null;
  }

  @Override
  public List<OpenTitleOrder> getTitleOrders(OrganisationIdentifier organisationIdentifier,
                                             CataloguingAction cataloguingAction, Date date, Date date1) {
    return null;
  }

  @Override
  public List<OpenTitleOrder> findTitleOrders(OrganisationIdentifier organisationIdentifier,
                                              ProductIdentifier productIdentifier) {
    return null;
  }

  @Override
  public Map<ProductIdentifier, List<OpenTitleOrder>> findTitleOrders(
          OrganisationIdentifier organisationIdentifier, List<ProductIdentifier> list) {
    return null;
  }

  @Override
  public List<ClosedTitleOrder> findClosedTitleOrders(OrganisationIdentifier organisationIdentifier, ProductIdentifier productIdentifier) {
    return Collections.emptyList();
  }

  @Override
  public Map<ProductIdentifier, List<ClosedTitleOrder>> findClosedTitleOrders(OrganisationIdentifier organisationIdentifier, List<ProductIdentifier> list) {
    return Collections.emptyMap();
  }

  @Override
  public long findTitleOrdersCount(TitleOrderSearchCriteria titleOrderSearchCriteria,
                                   Integer integer, Integer integer1) {
    return 0;
  }

  @Override
  public List<TitleOrder> findTitleOrders(TitleOrderSearchCriteria titleOrderSearchCriteria,
                                          Integer integer, Integer integer1) {
    return null;
  }

  @Override
  public List<BasketItem> getBasketItems(String s) {
    return null;
  }

  @Override
  public List<ItemReceiptLine> getNewProductReceipts(Date date, Date date1) {
    return null;
  }

  @Override
  public List<ItemReceiptLine> getNewProductReceipts(
          OrganisationIdentifier organisationIdentifier, Date date, Date date1) {
    return null;
  }

  @Override
  public List<OpenTitleOrder> getReceivedOrders(ProductIdentifier productIdentifier) {
    return null;
  }

  @Override
  public List<OpenTitleOrder> getReceivedOrders(ProductIdentifier productIdentifier,
                                                OrganisationIdentifier organisationIdentifier) {
    return null;
  }

  @Override
  public void addNewCoreSelections(Date date) {

  }

  @Override
  public void updateBestSellers() throws NotFoundException {

  }

  @Override
  public ExchangeRate getCurrencyExchangeRate(CurrencyCode currencyCode) {
    return null;
  }

  @Override
  public Price getEstimatedRetailPrice(Price price, Boolean aBoolean) {
    return null;
  }

  @Override
  public Price getEstimatedSalePrice(Price price, BigDecimal bigDecimal, Boolean aBoolean,
                                     CurrencyCode currencyCode) {
    return null;
  }
}
