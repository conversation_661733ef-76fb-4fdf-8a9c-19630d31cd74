package au.com.peterpal.lucyapi.core.service.retrieve;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import au.com.peterpal.libero.LiberoSearch;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.marc4j.marc.Record;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;

public class LiberoSearchTest {
  private static final String testServerAddress = "https://eurobodalla.libero.com.au/libero";
  private static final String testDbCode = "EBD";
  private LiberoSearch liberoSearch;

  @Before
  public void setUp() throws Exception {
    Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
    marshaller.setContextPath("au.com.libero.ws.v20161006.catalogue");

    this.liberoSearch = new LiberoSearch("https://eurobodalla.libero.com.au/libero", "EBD");
  }

  @Test
  public void keywordSearch() {

    List<Record> results = this.liberoSearch.search("organic farming australia", "ku");

    assertThat(results, is(notNullValue()));
  }

  @Test
  public void isbnSearch() {
    List<Record> results = this.liberoSearch.search("9780992471323", "i");
    Assert.assertThat(results.size(), is(1));
  }

}
