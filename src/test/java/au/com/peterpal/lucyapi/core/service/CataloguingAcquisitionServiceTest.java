package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.persistence.cataloguing.AcquisitionJdbcTemplateRepository;
import au.com.peterpal.lucyapi.persistence.cataloguing.CopyRepository;
import au.com.peterpal.lucyapi.persistence.cataloguing.model.Copy;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class CataloguingAcquisitionServiceTest {

    private AcquisitionJdbcTemplateRepository acquisitionJdbcTemplateRepository;

    private CopyRepository copyRepository;

    private WorkOrderService workOrderService;

    private CataloguingAcquisitionService cataloguingAcquisitionService;

    @Before
    public void setUp() throws Exception {
        acquisitionJdbcTemplateRepository = mock(AcquisitionJdbcTemplateRepository.class);
        copyRepository = mock(CopyRepository.class);
        workOrderService = mock(WorkOrderService.class);

        cataloguingAcquisitionService = new CataloguingAcquisitionService(acquisitionJdbcTemplateRepository,
                copyRepository, workOrderService);
    }

    @Test
    public void shouldDoNothingWhenRemoveAcquisitionsWithSelectAcquisitionsResultIsEmpty() {
        when(acquisitionJdbcTemplateRepository.findAcquisitionPkByWorkOrderNumber(anyString()))
                .thenReturn(new ArrayList<>());
        cataloguingAcquisitionService.removeAcquisitions("123456");
        verify(acquisitionJdbcTemplateRepository, times(1))
                .findAcquisitionPkByWorkOrderNumber(anyString());
        verify(copyRepository, times(0)).findAllByAcquisitionPkIn(anyList());
        verify(acquisitionJdbcTemplateRepository, times(0)).removeAcquisitions(anyList());
        verify(workOrderService, times(0)).removeBarcodes(anyString(), anyList());
    }

    @Test
    public void shouldNotRemoveBarcodesWhenRemoveAcquisitionsWithBarcodesIsEmpty() {
        List<Integer> acquisitionPks = new ArrayList<>(Arrays.asList(111, 222, 333));
        when(acquisitionJdbcTemplateRepository.findAcquisitionPkByWorkOrderNumber(anyString())).thenReturn(acquisitionPks);
        when(copyRepository.findAllByAcquisitionPkIn(anyList())).thenReturn(new ArrayList<>());
        cataloguingAcquisitionService.removeAcquisitions("123456");
        verify(acquisitionJdbcTemplateRepository, times(1))
                .findAcquisitionPkByWorkOrderNumber(anyString());
        verify(copyRepository, times(1)).findAllByAcquisitionPkIn(anyList());
        verify(acquisitionJdbcTemplateRepository, times(1)).removeAcquisitions(anyList());
        verify(workOrderService, times(0)).removeBarcodes(anyString(), anyList());
    }

    @Test
    public void shouldRemoveBarcodesWhenRemoveAcquisitionsWithBarcodesIsNotEmpty() {
        List<Integer> acquisitionPks = new ArrayList<>(Arrays.asList(111, 222, 333));
        when(acquisitionJdbcTemplateRepository.findAcquisitionPkByWorkOrderNumber(anyString())).thenReturn(acquisitionPks);
        when(copyRepository.findAllByAcquisitionPkIn(anyList())).thenReturn(mockListCopyWithBarCodes());
        cataloguingAcquisitionService.removeAcquisitions("123456");
        verify(acquisitionJdbcTemplateRepository, times(1))
                .findAcquisitionPkByWorkOrderNumber(anyString());
        verify(copyRepository, times(1)).findAllByAcquisitionPkIn(anyList());
        verify(acquisitionJdbcTemplateRepository, times(1)).removeAcquisitions(anyList());
        verify(workOrderService, times(1)).removeBarcodes(anyString(), anyList());
    }

    private List<Copy> mockListCopyWithBarCodes() {
        List<Copy> copies = new ArrayList<>();
        Copy copy1 = new Copy();
        copy1.setBarcodeNumber("123");
        Copy copy2 = new Copy();
        copy2.setBarcodeNumber("456");
        copies.add(copy1);
        copies.add(copy2);
        return copies;
    }

}