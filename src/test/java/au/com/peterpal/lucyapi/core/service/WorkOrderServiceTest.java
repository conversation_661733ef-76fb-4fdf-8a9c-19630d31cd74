package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.TestData;
import au.com.peterpal.lucyapi.model.*;
import lucy.cataloguing.beans.RemoteCataloguing;
import org.junit.Before;
import org.junit.Test;
import org.springframework.messaging.MessageChannel;

import java.util.Arrays;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class WorkOrderServiceTest {
    private WorkOrderService workOrderService;
    private RemoteCataloguing remoteCataloguing;
    private MessageChannel workOrderCompleteTaskChannel;
    private SubtaskInfo[] defaultValueSubtask;
    private TaskInfo task;
    private MessageChannel removeBarcodeChannel;
    private WorkOrderSlipService workOrderSlipService;
    private String printer;

    @Before
    public void setUp() {
        remoteCataloguing = mock(RemoteCataloguing.class);
        workOrderCompleteTaskChannel = mock(MessageChannel.class);
        removeBarcodeChannel = mock(MessageChannel.class);
        workOrderSlipService = mock(WorkOrderSlipService.class);

        workOrderService = new WorkOrderService(remoteCataloguing,
            workOrderCompleteTaskChannel,
            removeBarcodeChannel, workOrderSlipService);
        task = TaskInfo.builder()
            .taskId("123")
            .status(TaskStatus.IN_PROGRESS)
            .serviceType(ServiceItemType.SELECT_MULTIPLE)
            .type(TaskType.FULL)
            .taskNumber("T12345")
            .quantity(5)
            .serviceName("Service X")
            .workOrderId("W9876")
            .nextWorkOrderOnDone(true)
            .workOrderProfileType("Profile A")
            .build();
        defaultValueSubtask = TestData.getSubtaskInfo();
        printer = "printer";
    }

    @Test(expected = BusinessException.class)
    public void shouldThrowBusinessExceptionWhenCompleteAccessioningTaskSelectOneNotFoundAnyServiceComponentDefaultVal() {
        task.setServiceType(ServiceItemType.SELECT_ONE);
        workOrderService.completeAccessioningTask(task, new HashMap<>(), "Tyler", printer);
    }

    @Test
    public void shouldPublishMessageWhenCompleteAccessioningTaskSelectMultipleFoundServiceComponentDefaultVal() {
        task.setSubtasks(Arrays.asList(defaultValueSubtask));
        workOrderService.completeAccessioningTask(task, new HashMap<>(), "Tyler", printer);
        verify(workOrderCompleteTaskChannel, times(1)).send(any());
    }

    @Test
    public void shouldPublishMessageWhenCompleteAccessioningTaskSelectOneFoundServiceComponentDefaultVal() {
        task.setServiceType(ServiceItemType.SELECT_ONE);
        task.setSubtasks(Arrays.asList(defaultValueSubtask));
        workOrderService.completeAccessioningTask(task, new HashMap<>(), "Tyler", printer);
        verify(workOrderCompleteTaskChannel, times(1)).send(any());

    }
}
