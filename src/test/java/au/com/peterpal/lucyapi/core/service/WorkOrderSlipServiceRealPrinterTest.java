package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.model.WorkOrderSlipMessage;
import java.time.LocalDate;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.client.RestTemplate;

@SpringBootTest
public class WorkOrderSlipServiceRealPrinterTest {

  private final RestTemplate restTemplate = new RestTemplate();

  @Test
  public void testPrinting() {

    WorkOrderSlipService workOrderSlipService = new WorkOrderSlipService(restTemplate);

    WorkOrderSlipMessage workOrderSlipMessage = WorkOrderSlipMessage.builder()
        .workOrderNumber("5001223")
        .titleOrderNumber("TO5714715")
        .title("The Picture of Dorian Gray")
        .author("Oscar Wilde")
        .isbnOrdered("3210987654321")
        .isbnReceived("1234567890123")
        .customerCode("Townsville")
        .customerReference("Requests 6/12/24 - 13/12/24")
        .partSupply(true)
        .receivedTotal(Integer.valueOf("23"))
        .orderedTotal(Integer.valueOf("30"))
        .build();

    String workOrderSlip = workOrderSlipService.printWorkOrderSlip(
        "workorderslip/workordersliptemplateTest.xml", workOrderSlipMessage, LocalDate.of(2025, 1, 13));

    // uncomment when you want to send the test to the actual printer
//    workOrderSlipService.print("receipt-printer-01.peterpal.local", "0001", workOrderSlip);
  }
}
