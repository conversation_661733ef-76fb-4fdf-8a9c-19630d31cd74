package au.com.peterpal.lucyapi.core.service;

import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;

import au.com.peterpal.lucyapi.model.WorkOrderSlipMessage;
import java.io.IOException;
import java.time.LocalDate;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;
import org.xmlunit.assertj3.XmlAssert;

public class WorkOrderSlipServiceTest {

  @Test
  public void test() throws IOException {

    String expectedXMLOutput =
        WorkOrderSlipService.readXMLTemplate("workorderslip/workordersliptemplateTest.xml", false);

    expectedXMLOutput = makeXMLWellFormed(expectedXMLOutput);

    RestTemplate restTemplate = mock(RestTemplate.class);

    WorkOrderSlipService workOrderSlipService = new WorkOrderSlipService(restTemplate);

    WorkOrderSlipMessage workOrderSlipMessage =
        WorkOrderSlipMessage.builder()
            .workOrderNumber("5001223")
            .titleOrderNumber("TO5714715")
            .titleOrderDate(LocalDate.of(2025, 7, 31))
            .title("The Picture of Dorian Gray")
            .author("Oscar Wilde")
            .isbnOrdered("3210987654321")
            .isbnReceived("1234567890123")
            .customerCode("Townsville")
            .customerReference("Requests 6/12/24 - 13/12/24")
            .partSupply(true)
            .receivedTotal(Integer.valueOf("23"))
            .orderedTotal(Integer.valueOf("30"))
            .fundCode("ANF")
            .deliveryInstruction("Instruction")
            .build();

    String workOrderSlip =
        workOrderSlipService.printWorkOrderSlip(
            "workorderslip/workordersliptemplate.xml",
            workOrderSlipMessage,
            LocalDate.of(2025, 1, 6));

    workOrderSlip = "<?xml version=\"1.0\" standalone=\"no\"?>" + makeXMLWellFormed(workOrderSlip);
    expectedXMLOutput = "<?xml version=\"1.0\" standalone=\"no\"?>" + expectedXMLOutput;
    XmlAssert.assertThat(workOrderSlip).and(expectedXMLOutput).areIdentical();
  }

  @Test
  public void testExcludeIsbnOrdered() throws IOException {

    String expectedXMLOutput =
        WorkOrderSlipService.readXMLTemplate("workorderslip/workordersliptemplateTest.xml", true);

    expectedXMLOutput = makeXMLWellFormed(expectedXMLOutput);

    RestTemplate restTemplate = mock(RestTemplate.class);

    WorkOrderSlipService workOrderSlipService = new WorkOrderSlipService(restTemplate);

    WorkOrderSlipMessage workOrderSlipMessage =
        WorkOrderSlipMessage.builder()
            .workOrderNumber("5001223")
            .titleOrderNumber("TO5714715")
            .titleOrderDate(LocalDate.of(2025, 7, 31))
            .title("The Picture of Dorian Gray")
            .author("Oscar Wilde")
            .isbnOrdered("1234567890123")
            .isbnReceived("1234567890123")
            .customerCode("Townsville")
            .customerReference("Requests 6/12/24 - 13/12/24")
            .partSupply(true)
            .receivedTotal(Integer.valueOf("23"))
            .orderedTotal(Integer.valueOf("30"))
            .fundCode("ANF")
            .deliveryInstruction("Instruction")
            .build();

    String workOrderSlip =
        workOrderSlipService.printWorkOrderSlip(
            "workorderslip/workordersliptemplate.xml",
            workOrderSlipMessage,
            LocalDate.of(2025, 1, 6));

    workOrderSlip = "<?xml version=\"1.0\" standalone=\"no\"?>" + makeXMLWellFormed(workOrderSlip);
    expectedXMLOutput = "<?xml version=\"1.0\" standalone=\"no\"?>" + expectedXMLOutput;
    XmlAssert.assertThat(workOrderSlip).and(expectedXMLOutput).areIdentical();

    assertThat(workOrderSlip, not(containsString("ISBN Ordered")));
  }

  public String makeXMLWellFormed(String xml) {
    return "<epos-print xmlns=\"http://www.epson-pos.com/schemas/2011/03/epos-print\">"
        + xml
        + "</epos-print>";
  }
}
