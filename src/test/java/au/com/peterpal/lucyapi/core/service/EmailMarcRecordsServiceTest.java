package au.com.peterpal.lucyapi.core.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

import au.com.peterpal.lucyapi.model.email.EmailMessage;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class EmailMarcRecordsServiceTest {

    private EmailMarcRecordsService emailMarcRecordsService;

    @MockBean
    private MessageChannel marcRecordsEmailChannel;

    @Before
    public void setUp() throws Exception {
        emailMarcRecordsService = new EmailMarcRecordsService(marcRecordsEmailChannel);
    }

    @Test
    public void sendEmail() {
        ArgumentCaptor<Message<EmailMessage>> emailMessageArgumentCaptor = ArgumentCaptor.forClass(Message.class);
        String customerCode = "BCC";
        String email = "<EMAIL>";
        Map<Integer, File> marcFiles = new HashMap<>();
        marcFiles.put(1, new File("src/test/resources/PPLS_227205.mrc"));

        when(marcRecordsEmailChannel.send(emailMessageArgumentCaptor.capture())).thenReturn(true);
        emailMarcRecordsService.sendEmail(customerCode, email, marcFiles);

        assertEquals(emailMessageArgumentCaptor.getValue().getPayload().getTo().get(0).getEmailAddress(), email);
        assertEquals(emailMessageArgumentCaptor.getValue().getPayload().getBcc().size(), 0);
        assertEquals(emailMessageArgumentCaptor.getValue().getPayload().getCc().size(), 0);
        assertNotNull(emailMessageArgumentCaptor.getValue().getPayload().getContent().getHtmlTemplate());
    }
}
