package au.com.peterpal.lucyapi.utils;

import static au.com.peterpal.lucyapi.utils.Partition.ofSize;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

import java.util.Arrays;
import java.util.List;
import org.junit.Test;

public class PartitionTest {

  @Test
  public void testPartitionListOfString() {
    final List<String> tos = Arrays.asList("TO000166", "TO036666", "TO088163", "TO1563434", "TO2233398", "TO2248020", "TO2365595");

    Partition<String> partitionOfSize3 = ofSize(tos, 3);
    Partition<String> partitionOfSize2 = ofSize(tos, 2);

    assertThat(partitionOfSize3, is(notNullValue()));
    assertThat(partitionOfSize3.size(), is(3));
    assertThat(partitionOfSize2, is(notNullValue()));
    assertThat(partitionOfSize2.size(), is(4));
  }
}
