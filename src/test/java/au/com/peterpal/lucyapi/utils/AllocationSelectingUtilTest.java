package au.com.peterpal.lucyapi.utils;


import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class AllocationSelectingUtilTest {
    @Test
    public void shouldReplaceNearLatestByLatestWhenSelectAllocationToProcess() {
        Map<String, Integer> mapOfAllocationIdAndQuantity = new HashMap<>();
        mapOfAllocationIdAndQuantity.put("1", 1);
        mapOfAllocationIdAndQuantity.put("2", 2);
        mapOfAllocationIdAndQuantity.put("3", 1);
        mapOfAllocationIdAndQuantity.put("4", 1);
        mapOfAllocationIdAndQuantity.put("5", 2);

        int workOrderQuantity = 6;

        List<String> expectedResult = new ArrayList<>();
        expectedResult.add("1");
        expectedResult.add("2");
        expectedResult.add("3");
        expectedResult.add("5");

        List<String> result = AllocationSelectingUtil.selectBranchCodeToProcess(mapOfAllocationIdAndQuantity, workOrderQuantity);

        assertEquals(expectedResult, result);
    }

    @Test
    public void shouldReplaceThirdByLatestWhenSelectAllocationToProcess() {
        Map<String, Integer> mapOfAllocationIdAndQuantity = new HashMap<>();
        mapOfAllocationIdAndQuantity.put("1", 1);
        mapOfAllocationIdAndQuantity.put("2", 1);
        mapOfAllocationIdAndQuantity.put("3", 1);
        mapOfAllocationIdAndQuantity.put("4", 2);
        mapOfAllocationIdAndQuantity.put("5", 4);
        mapOfAllocationIdAndQuantity.put("6", 2);

        int workOrderQuantity = 6;

        List<String> expectedResult = new ArrayList<>();
        expectedResult.add("1");
        expectedResult.add("2");
        expectedResult.add("4");
        expectedResult.add("6");

        List<String> result = AllocationSelectingUtil.selectBranchCodeToProcess(mapOfAllocationIdAndQuantity, workOrderQuantity);

        assertEquals(expectedResult, result);
    }

}
