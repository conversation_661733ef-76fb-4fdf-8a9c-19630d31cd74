package au.com.peterpal.lucyapi.utils;

import org.junit.Test;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

public class DateFormatUtilTest {

    private final List<String> formats = Arrays.asList("d/M/yyyy", "yyyy-M-d");

    @Test
    public void test_d_M_yyyy_withoutLeadingZero() {
        String input = "1/2/2025"; // 1 Feb 2025
        LocalDate expected = LocalDate.of(2025, 2, 1);
        LocalDate actual = DateFormatUtil.parseDate(input, formats);
        assertEquals(expected, actual);
    }

    @Test
    public void test_d_M_yyyy_withLeadingZero() {
        String input = "01/02/2025";
        LocalDate expected = LocalDate.of(2025, 2, 1);
        LocalDate actual = DateFormatUtil.parseDate(input, formats);
        assertEquals(expected, actual);
    }

    @Test
    public void test_yyyy_M_d_withoutLeadingZero() {
        String input = "2025-2-1";
        LocalDate expected = LocalDate.of(2025, 2, 1);
        LocalDate actual = DateFormatUtil.parseDate(input, formats);
        assertEquals(expected, actual);
    }

    @Test
    public void test_yyyy_M_d_withLeadingZero() {
        String input = "2025-02-01";
        LocalDate expected = LocalDate.of(2025, 2, 1);
        LocalDate actual = DateFormatUtil.parseDate(input, formats);
        assertEquals(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void test_invalidFormat_shouldThrow() {
        String input = "02-01-2025"; // not matching any format
        DateFormatUtil.parseDate(input, formats);
    }

    @Test(expected = IllegalArgumentException.class)
    public void test_emptyString_shouldThrow() {
        String input = "";
        DateFormatUtil.parseDate(input, formats);
    }

    @Test(expected = NullPointerException.class)
    public void test_nullInput_shouldThrow() {
        String input = null;
        DateFormatUtil.parseDate(input, formats);
    }

    @Test(expected = IllegalArgumentException.class)
    public void test_emptyFormats_shouldThrow() {
        String input = "1/2/2025";
        DateFormatUtil.parseDate(input, Collections.emptyList());
    }
}
