package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.lucyapi.SpringSecurityTestConfig;
import au.com.peterpal.lucyapi.config.SecurityConfig;
import au.com.peterpal.lucyapi.core.service.TitleOrderService;
import au.com.peterpal.lucyapi.core.service.retrieve.RetrieveService;
import au.com.peterpal.lucyapi.model.CataloguingState;
import au.com.peterpal.lucyapi.model.SearchCriteria;
import au.com.peterpal.lucyapi.model.TitleOrderInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import lucy.fulfillment.codes.CataloguingAction;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.mockito.BDDMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;

import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ContextConfiguration
@RunWith(SpringRunner.class)
@WebMvcTest(TitleOrderController.class)
@WithUserDetails("dev")
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class TitleOrderControllerTest {

  private static final String BASE_PATH = "/api/orders/";

  @Autowired private MockMvc mvc;

  @MockBean private TitleOrderService titleOrderService;

  @MockBean private RetrieveService retrieveService;

  @MockBean private MessageChannel addToPurchaseOrderChannel;

  private List<TitleOrderInfo> titleOrders;
  private String customer = "BCC";
  private ObjectMapper mapper;

  @Before
  public void setUp() {
    titleOrders =
        Lists.newArrayList(
            TitleOrderInfo.builder()
                .state(CataloguingState.NEW)
                .customerCode(customer)
                .nextAction(CataloguingAction.RETRIEVE)
                .orderDate(LocalDate.now())
                .title("Title")
                .build(),
            TitleOrderInfo.builder()
                .state(CataloguingState.RETRIEVED)
                .customerCode(customer)
                .nextAction(CataloguingAction.RETRIEVE)
                .orderDate(LocalDate.now())
                .title("Title")
                .build());
  }

  @Test
  public void searchByStatusAllParams() throws Exception {
    List<String> cataloguingStates = Lists.newArrayList("New");
    LocalDate dateFrom = LocalDate.of(2018, 3, 1);
    LocalDate dateTo = LocalDate.of(2018, 5, 21);
    SearchCriteria searchCriteria =
        SearchCriteria.builder().cataloguingStates(cataloguingStates).build();

    BDDMockito.given(
            titleOrderService.getTitleOrdersByState(customer, searchCriteria, dateFrom, dateTo))
        .willReturn(titleOrders);

    mvc.perform(
            get(BASE_PATH + "searchByStatus")
                .param("customer", customer)
                .param("cataloguingStates", cataloguingStates.get(0))
                .param("dateFrom", dateFrom.toString())
                .param("dateTo", dateTo.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void searchByStatusCustomerOnly() throws Exception {
    BDDMockito.given(titleOrderService.getTitleOrdersByState(customer, null, null, null))
        .willReturn(titleOrders);

    mvc.perform(
            get(BASE_PATH + "searchByStatus")
                .param("customer", customer)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void searchByStatusDateRangeOnly() throws Exception {
    LocalDate dateFrom = LocalDate.of(2018, 3, 1);
    LocalDate dateTo = LocalDate.of(2018, 5, 21);

    BDDMockito.given(titleOrderService.getTitleOrdersByState(customer, null, dateFrom, dateTo))
        .willReturn(titleOrders);

    mvc.perform(
            get(BASE_PATH + "searchByStatus")
                .param("customer", customer)
                .param("dateFrom", dateFrom.toString())
                .param("dateTo", dateTo.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void searchByStatusCataloguingStatesOnly() throws Exception {
    List<String> cataloguingStates =
        Lists.newArrayList(CataloguingState.NEW.getCode(), CataloguingState.RETRIEVED.getCode());
    SearchCriteria searchCriteria =
        SearchCriteria.builder().cataloguingStates(cataloguingStates).build();

    BDDMockito.given(titleOrderService.getTitleOrdersByState(customer, searchCriteria, null, null))
        .willReturn(titleOrders);

    mvc.perform(
            get(BASE_PATH + "searchByStatus")
                .param("customer", customer)
                .param("cataloguingStates", cataloguingStates.get(0))
                .param("cataloguingStates", cataloguingStates.get(1))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void searchByStatusWithNoParams() throws Exception {
    mvc.perform(
            get(BASE_PATH + "searchByStatus")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void addTitleOrders() throws Exception {
    mapper = new ObjectMapper();
    List<String> titleOrder = Collections.singletonList("TO12345");
    String str = mapper.writeValueAsString(titleOrder);
    mvc.perform(
            post(BASE_PATH + "/add-to-purchase-order")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
                .content(str))
        .andDo(MockMvcResultHandlers.print())
        .andExpect(status().isOk());

    verify(addToPurchaseOrderChannel)
        .send(argThat((GenericMessage<List<String>> message) -> message.getPayload().equals(titleOrder)));
  }
}
