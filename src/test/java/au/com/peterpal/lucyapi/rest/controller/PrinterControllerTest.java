package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.lucyapi.SpringSecurityTestConfig;
import au.com.peterpal.lucyapi.config.SecurityConfig;
import au.com.peterpal.lucyapi.core.service.PrinterService;
import au.com.peterpal.lucyapi.model.CreatePrinterRequest;
import au.com.peterpal.lucyapi.model.PrinterResponse;
import au.com.peterpal.lucyapi.model.UpdatePrinterRequest;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.Printer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ContextConfiguration
@RunWith(SpringRunner.class)
@WebMvcTest(PrinterController.class)
@WithUserDetails("dev")
@Import({
    SecurityConfig.class,
    SpringSecurityTestConfig.class,
    CustomKeycloakSpringBootConfigResolver.class,
    KeycloakSpringBootProperties.class
})
public class PrinterControllerTest {
  private static final String BASE_PATH = "/api/printer";
  private ObjectMapper mapper;
  @Autowired private MockMvc mvc;
  @MockBean private PrinterService printerService;

  private Printer printer;
  private PrinterResponse response;
  private UUID printerId;
  private String host;
  private String label;

  @Before
  public void setUp() {
    mapper = new ObjectMapper();
    printerId = UUID.randomUUID();
    host = "griffiths.peterpal.local";
    label = "packing-printer-1";

    printer =
        Printer.builder()
            .id(printerId)
            .host(host)
            .label(label)
            .build();
    response =
        PrinterResponse.builder()
            .id(printerId)
            .host(host)
            .label(label)
            .build();

    given(printerService.create(any(Printer.class))).willReturn(printer);
    given(printerService.findById(eq(printerId))).willReturn(printer);
    given(printerService.findAll()).willReturn(Lists.newArrayList(printer));
  }

  @Test
  public void createPrinter() throws Exception {
    CreatePrinterRequest request = CreatePrinterRequest.of(host, label);

    mvc.perform(
            post(BASE_PATH)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isCreated())
        .andExpect(content().string(mapper.writeValueAsString(response)));

    given(printerService.create(eq(printer))).willReturn(printer);
  }

  @Test
  public void updatePrinter() throws Exception {
    String labelUpdated = "packing-printer-2";
    String nameUpdated = "hp-f0b0";
    UpdatePrinterRequest request =
        UpdatePrinterRequest.builder()
            .id(printerId)
            .host(host)
            .label(labelUpdated)
            .build();

    Printer printerUpdated = printer.withLabel(labelUpdated);
    PrinterResponse responseUpdated = response.withLabel(labelUpdated);

    given(printerService.update(eq(printerUpdated))).willReturn(printerUpdated);

    mvc.perform(
            put(BASE_PATH)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isOk())
        .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
        .andExpect(content().string(mapper.writeValueAsString(responseUpdated)));

    verify(printerService).findById(eq(printerId));
    verify(printerService).update(eq(printerUpdated));
  }

  @Test
  public void updateNotExistingPrinterShouldThrowException() throws Exception {
    UpdatePrinterRequest request =
        UpdatePrinterRequest.builder()
            .id(printerId)
            .label(label)
            .build();

    given(printerService.findById(eq(printerId))).willThrow(ResourceNotFoundException.class);

    mvc.perform(
            put(BASE_PATH)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isNotFound());

    verify(printerService).findById(eq(printerId));
    verify(printerService, never()).update(any(Printer.class));
  }

  @Test
  public void deletePrinter() throws Exception {

    mvc.perform(
            delete(String.format("%s/%s", BASE_PATH, printerId.toString()))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());

    verify(printerService).findById(eq(printerId));
    verify(printerService).delete(any(Printer.class));
  }

  @Test
  public void deleteNotExistingPrinterShouldThrowException() throws Exception {

    given(printerService.findById(eq(printerId))).willThrow(ResourceNotFoundException.class);

    mvc.perform(
            delete(String.format("%s/%s", BASE_PATH, printerId.toString()))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound());

    verify(printerService).findById(eq(printerId));
    verify(printerService, never()).delete(any(Printer.class));
  }

  @Test
  public void findById() throws Exception {

    mvc.perform(
            get(String.format("%s/%s", BASE_PATH, printerId.toString()))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(mapper.writeValueAsString(response)));

    verify(printerService).findById(eq(printerId));
  }

  @Test
  public void findByNotExistingPrinterShouldThrowException() throws Exception {

    given(printerService.findById(eq(printerId))).willThrow(ResourceNotFoundException.class);

    mvc.perform(
            get(String.format("%s/%s", BASE_PATH, printerId.toString()))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound());

    verify(printerService).findById(eq(printerId));
  }

  @Test
  public void findAllPrinters() throws Exception {

    mvc.perform(
            get(String.format("%s/%s", BASE_PATH, "find-all"))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(mapper.writeValueAsString(Lists.newArrayList(response))));

    verify(printerService).findAll();
  }

  @Test
  public void findAllPrintersWithoutResult() throws Exception {

    given(printerService.findAll()).willReturn(Lists.newArrayList());

    mvc.perform(
            get(String.format("%s/%s", BASE_PATH, "find-all"))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(mapper.writeValueAsString(Lists.newArrayList())));

    verify(printerService).findAll();
  }
}
