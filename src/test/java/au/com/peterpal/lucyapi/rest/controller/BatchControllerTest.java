package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.lucyapi.SpringSecurityTestConfig;
import au.com.peterpal.lucyapi.batch.boundary.BatchController;
import au.com.peterpal.lucyapi.batch.control.BatchService;
import au.com.peterpal.lucyapi.batch.control.FileService;
import au.com.peterpal.lucyapi.batch.dto.BatchRequest;
import au.com.peterpal.lucyapi.batch.model.BatchInfo;
import au.com.peterpal.lucyapi.config.SecurityConfig;
import au.com.peterpal.lucyapi.core.service.LMSService;
import au.com.peterpal.lucyapi.model.BibRecordType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lucy.cataloguing.codes.BibRecordBatchStatus;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.hamcrest.CoreMatchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ContextConfiguration
@RunWith(SpringRunner.class)
@WebMvcTest(BatchController.class)
@WithUserDetails("dev")
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class BatchControllerTest {

  private static final String BASE_PATH = "/api/";

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper mapper;

  @MockBean private BatchService batchService;

  @MockBean private FileService fileService;

  @MockBean private LMSService lmsService;

  private BatchRequest batchRequest;

  @Before
  public void setUp() {
    batchRequest =
        BatchRequest.builder()
            .customer("BCC")
            .bibType(BibRecordType.ONORDER)
            .description("On order batch for Brisbane")
            .build();
  }

  @Test
  public void createBatch() throws Exception {
    when(batchService.createBatch(any(BatchInfo.class), any(String.class)))
        .thenAnswer(
            info -> {
              return BatchInfo.builder()
                  .id(new Integer(123456))
                  .bibType(BibRecordType.ONORDER)
                  .customerCode("BCC")
                  .status(BibRecordBatchStatus.OPEN)
                  .description("On order batch for Brisbane")
                  .build();
            });

    MvcResult result =
        mockMvc
            .perform(
                post(String.format("%sbatches", BASE_PATH))
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON)
                    .content(mapper.writeValueAsString(batchRequest)))
            .andExpect(status().isOk())
            .andDo(print())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id", is(123456)))
            .andExpect(jsonPath("$.bibType", is("ONORDER")))
            .andExpect(jsonPath("$.status", is("OPEN")))
            .andExpect(jsonPath("$.customerCode", is("BCC")))
            .andExpect(jsonPath("$.description", is("On order batch for Brisbane")))
            .andReturn();
  }
}
