package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.lucyapi.SpringSecurityTestConfig;
import au.com.peterpal.lucyapi.config.SecurityConfig;
import au.com.peterpal.lucyapi.core.service.BarcodeService;
import au.com.peterpal.lucyapi.core.service.WorkOrderService;
import au.com.peterpal.lucyapi.model.BarcodeInfo;
import lucy.common.NotFoundException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ContextConfiguration
@RunWith(SpringRunner.class)
@WebMvcTest(CataloguingAdminController.class)
@WithUserDetails("dev")
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class CataloguingAdminControllerTest {

  private static final String BASE_PATH = "/api/admin/barcodes/";

  @Autowired private MockMvc mockMvc;

  @MockBean private BarcodeService service;

  @MockBean private WorkOrderService workOrderService;

  @Autowired private CataloguingAdminController controller;

  @Test
  public void contextLoads() {
    assertThat(controller, notNullValue());
  }

  @Test
  public void findByWorkOrderNumber() throws Exception {
    when(service.getBarcodeInfo(any(String.class)))
        .thenAnswer(
            i -> {
              List<BarcodeInfo> barcodes = new ArrayList<>();
              barcodes.add(
                  BarcodeInfo.builder()
                      .barcode("123-456-789")
                      .branchCode("ABCD")
                      .branchName("Branch Name")
                      .build());
              barcodes.add(
                  BarcodeInfo.builder()
                      .barcode("789-456-123")
                      .branchCode("DCBA")
                      .branchName("Another Branch Name")
                      .build());
              return barcodes;
            });

    mockMvc
        .perform(
            get(String.format("%sfind-by-work-order-number/123456", BASE_PATH))
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$").isArray());
  }

  @Test
  public void findByWorkOrderNumberNotFound() throws Exception {
    final String errorMessage = "Could not find receipt for work order number 123456";
    when(service.getBarcodeInfo(any(String.class))).thenThrow(new NotFoundException(errorMessage));

    mockMvc
        .perform(
            get(String.format("%sfind-by-work-order-number/123456", BASE_PATH))
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
        .andDo(print())
        .andExpect(jsonPath("$.errors", hasItem(errorMessage)))
        .andExpect(status().is4xxClientError());
  }
}
