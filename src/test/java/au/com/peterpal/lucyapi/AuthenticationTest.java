package au.com.peterpal.lucyapi;

import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.lucyapi.batch.boundary.BatchController;
import au.com.peterpal.lucyapi.batch.control.BatchService;
import au.com.peterpal.lucyapi.config.SecurityConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@EnableSpringDataWebSupport
@WebMvcTest(BatchController.class)
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class AuthenticationTest {

  private static final String BASE_PATH = "/api/admin/batches";

  @Autowired private MockMvc mvc;
  @Autowired private ObjectMapper mapper;
  @MockBean private BatchController batchController;
  @MockBean private BatchService service;

  @Test
  public void unauthorizedAccess() throws Exception {
    setEnvironment().andExpect(status().isUnauthorized());
  }

  @Test
  @WithUserDetails
  public void forbiddenAccess() throws Exception {
    setEnvironment().andExpect(status().isForbidden());
  }

  private ResultActions setEnvironment() throws Exception {
    return mvc.perform(
        post(BASE_PATH)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(mapper.writeValueAsString("{}")));
  }
}
