package au.com.peterpal.lucyapi.model;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.springframework.util.Assert.notNull;

import java.util.Arrays;
import java.util.Optional;
import java.util.List;
import lucy.fulfillment.codes.CataloguingAction;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class BarchartInfoTest {

  @Test
  public void actionToStateConversionTest() {

    List<TitleOrderActionInfo> list = Arrays.asList(new TitleOrderActionInfo[] {
        TitleOrderActionInfo.of(CataloguingAction.RETRIEVE, 10),
        TitleOrderActionInfo.of(CataloguingAction.MATCH, 2),
        TitleOrderActionInfo.of(CataloguingAction.OO_NEW, 5),
        TitleOrderActionInfo.of(CataloguingAction.OO_ADD_ITEM, 3),
        TitleOrderActionInfo.of(CataloguingAction.OO_ADD_ISBN, 1),
        TitleOrderActionInfo.of(CataloguingAction.CHECK, 3),
        TitleOrderActionInfo.of(CataloguingAction.OO_MANUAL, 7),
        TitleOrderActionInfo.of(CataloguingAction.ACCESSION, 4),
        TitleOrderActionInfo.of(CataloguingAction.CATALOGUE, 11),
        TitleOrderActionInfo.of(CataloguingAction.ORIGINAL_CATALOGUE, 12),
        TitleOrderActionInfo.of(CataloguingAction.LOAD, 8),
        TitleOrderActionInfo.of(CataloguingAction.COMPLETE, 2),
    });

    List<BarchartInfo> out = BarchartInfo.from(list);

    assertThat(out, is(notNullValue()));
    assertThat(out.size(), is(7));
    assertThat(get(out, CataloguingState.NEW), is(notNullValue()));
    assertThat(get(out, CataloguingState.NEW).getCount(), is(10));
    assertThat(get(out, CataloguingState.CHECK), is(notNullValue()));
    assertThat(get(out, CataloguingState.CHECK).getCount(), is(10));
    assertThat(get(out, CataloguingState.EXPORTED), is(notNullValue()));
    assertThat(get(out, CataloguingState.EXPORTED).getCount(), is(35));
    assertThat(get(out, CataloguingState.OOR), is(notNullValue()));
    assertThat(get(out, CataloguingState.OOR).getCount(), is(5));
    assertThat(get(out, CataloguingState.RETRIEVED), is(notNullValue()));
    assertThat(get(out, CataloguingState.RETRIEVED).getCount(), is(2));
    assertThat(get(out, CataloguingState.ISBN), is(notNullValue()));
    assertThat(get(out, CataloguingState.ISBN).getCount(), is(1));
    assertThat(get(out, CataloguingState.ITEM), is(notNullValue()));
    assertThat(get(out, CataloguingState.ITEM).getCount(), is(3));
  }

  @Test
  public void filterActionsTest() {

    List<TitleOrderActionInfo> list = Arrays.asList(new TitleOrderActionInfo[] {
        TitleOrderActionInfo.of(CataloguingAction.RETRIEVE, 10),
        TitleOrderActionInfo.of(CataloguingAction.NONE, 2),
        TitleOrderActionInfo.of(CataloguingAction.COMPLETE, 5)
    });

    List<BarchartInfo> out = BarchartInfo.from(list);

    assertThat(out, is(notNullValue()));
    assertThat(out.size(), is(1));
  }

  private BarchartInfo get(List<BarchartInfo> list, CataloguingState state) {
    notNull(state, "Cataloguing state must not be null");

    return Optional.ofNullable(list)
        .map(l -> l.stream()
            .filter(info -> state.equals(info.getState()))
            .findFirst()
            .orElse(null)
        )
        .orElse(null);
  }
}
