package au.com.peterpal.lucyapi.persistence;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository;
import java.util.Hashtable;
import java.util.Map;
import javax.sql.DataSource;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.test.context.junit4.SpringRunner;

import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EC_ADD_PO_ERROR;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EC_MORE_THAN_ONE_TO;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EC_NULL_SUPPLIER_CODE;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EC_UPDATE_TO_ERROR;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EM_ADD_PO_ERROR;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EM_MORE_THAN_ONE_TO;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EM_NULL_SUPPLIER_CODE;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.EM_UPDATE_TO_ERROR;
import static au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository.PN_ERROR_CODE;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalToIgnoringCase;
import static org.junit.Assert.fail;

@RunWith(SpringRunner.class)
public class LucyHelperRepositoryTest {

  private LucyHelperRepository repo;

  @Mock
  private SimpleJdbcCall simpleJdbcCall;

  @Before
  public void setUp() {
    DataSource dataSource = Mockito.mock(DataSource.class);
    repo = new LucyHelperRepository(dataSource);
  }

  @Test
  public void testProcessNullTO() {
    LucyHelperRepository spyed = Mockito.spy(repo);
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    repo.processTitleOrder(null);
    Mockito.verify(spyed, Mockito.times(0)).newSimpleJdbcCall();
  }

  @Test
  public void testProcessTOWithSpace() {
    LucyHelperRepository spyed = Mockito.spy(repo);
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    repo.processTitleOrder("TO 1234");
    Mockito.verify(spyed, Mockito.times(0)).newSimpleJdbcCall();
  }

  @Test
  public void testProcessTOMoreThanOneTOSelected() {
    LucyHelperRepository spyed = Mockito.spy(repo);
    Map<String, Object> out = new Hashtable<>();
    out.put(PN_ERROR_CODE, EC_MORE_THAN_ONE_TO);
    Mockito.doReturn(out).when(simpleJdbcCall).execute(Mockito.any(MapSqlParameterSource.class));
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    try {
      spyed.processTitleOrder("TO12345");
      fail("Expected BusinessException has not been thrown");
    } catch (BusinessException ex) {
      String str = String.format(EM_MORE_THAN_ONE_TO, EC_MORE_THAN_ONE_TO, "TO12345");
      assertThat(ex.getMessage(), equalToIgnoringCase(str));
    }
  }

  @Test
  public void testProcessTONullSupplierCode() {
    LucyHelperRepository spyed = Mockito.spy(repo);
    Map<String, Object> out = new Hashtable<>();
    out.put(PN_ERROR_CODE, EC_NULL_SUPPLIER_CODE);
    Mockito.doReturn(out).when(simpleJdbcCall).execute(Mockito.any(MapSqlParameterSource.class));
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    try {
      spyed.processTitleOrder("TO12345");
      fail("Expected BusinessException has not been thrown");
    } catch (BusinessException ex) {
      String str = String.format(EM_NULL_SUPPLIER_CODE, EC_NULL_SUPPLIER_CODE, "TO12345");
      assertThat(ex.getMessage(), equalToIgnoringCase(str));
    }
  }

  @Test
  public void testProcessTOAddPOError() {
    LucyHelperRepository spyed = Mockito.spy(repo);
    Map<String, Object> out = new Hashtable<>();
    out.put(PN_ERROR_CODE, EC_ADD_PO_ERROR);
    Mockito.doReturn(out).when(simpleJdbcCall).execute(Mockito.any(MapSqlParameterSource.class));
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    try {
      spyed.processTitleOrder("TO12345");
      fail("Expected BusinessException has not been thrown");
    } catch (BusinessException ex) {
      String str = String.format(EM_ADD_PO_ERROR, EC_ADD_PO_ERROR, "TO12345");
      assertThat(ex.getMessage(), equalToIgnoringCase(str));
    }
  }

  @Test
  public void testProcessTOUpdateTOError() {
    LucyHelperRepository spyed = Mockito.spy(repo);
    Map<String, Object> out = new Hashtable<>();
    out.put(PN_ERROR_CODE, EC_UPDATE_TO_ERROR);
    Mockito.doReturn(out).when(simpleJdbcCall).execute(Mockito.any(MapSqlParameterSource.class));
    Mockito.doReturn(simpleJdbcCall).when(spyed).newSimpleJdbcCall();
    try {
      spyed.processTitleOrder("TO12345");
      fail("Expected BusinessException has not been thrown");
    } catch (BusinessException ex) {
      String str = String.format(EM_UPDATE_TO_ERROR, EC_UPDATE_TO_ERROR, "TO12345");
      assertThat(ex.getMessage(), equalToIgnoringCase(str));
    }
  }
}
