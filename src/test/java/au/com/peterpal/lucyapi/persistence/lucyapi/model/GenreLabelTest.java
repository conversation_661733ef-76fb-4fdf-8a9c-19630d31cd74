package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.UUID;
import org.junit.Before;
import org.junit.Test;

public class GenreLabelTest {

  GenreLabel genreLabel;

  @Before
  public void setUp() throws Exception {
    genreLabel = GenreLabel.builder().id(UUID.randomUUID()).customerCode("TEST")
        .image("test image").holdingField("").holdingValue("").holdingValue("").build();
  }

  @Test
  public void genreLabel1() {
    genreLabel = genreLabel.withHoldingField("genre")
        .withHoldingValue("COMPUTERS, BUSINESS & FINANCE");

    assertThat(genreLabel.isValid()).isEqualTo(true);
  }

  @Test
  public void genreLabel2() {
    genreLabel = genreLabel.withHoldingField("genre")
        .withHoldingValue("COMPUTERS, BUSINESS, FINANCE");

    assertThat(genreLabel.isValid()).isEqualTo(true);
  }

  @Test
  public void genreLabel3() {
    genreLabel = genreLabel.withHoldingField("collectionCode,cutter")
        .withHoldingValue("PB,J");

    assertThat(genreLabel.isValid()).isEqualTo(false);
  }
  @Test
  public void genreLabel4() {
    genreLabel = genreLabel.withHoldingField("collectionCode,cutter")
        .withHoldingValue("PB|J");

    assertThat(genreLabel.isValid()).isEqualTo(true);
  }
}
