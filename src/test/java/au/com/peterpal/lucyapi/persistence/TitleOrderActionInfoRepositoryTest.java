package au.com.peterpal.lucyapi.persistence;

import au.com.peterpal.lucyapi.model.TitleOrderActionInfo;
import au.com.peterpal.lucyapi.persistence.lucy.TitleOrderActionInfoRepository;
import lucy.fulfillment.codes.CataloguingAction;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.hamcrest.core.IsNull.notNullValue;

@RunWith(SpringRunner.class)
public class TitleOrderActionInfoRepositoryTest {

  private TitleOrderActionInfoRepository repo;

  @Before
  public void setUp() {
    repo = new TitleOrderActionInfoRepository();
  }

  @Test
  public void convertNullActionTest() throws NoSuchMethodException,
          IllegalAccessException, InvocationTargetException {

    List<Object[]> list = Arrays.asList(new Object[][] {
            {null, 10}
    });

    Method method = TitleOrderActionInfoRepository.class.getDeclaredMethod("convert", List.class);
    method.setAccessible(true);
    List<TitleOrderActionInfo> out = (List) method.invoke(repo, list);
    method.setAccessible(false);

    assertThat(out, is(notNullValue()));
    assertThat(out.size(), is(1));
    assertThat(out.get(0).getAction(), equalTo(CataloguingAction.RETRIEVE));
  }

  @Test
  public void convertUnknownActionTest() throws NoSuchMethodException,
          IllegalAccessException, InvocationTargetException {

    List<Object[]> list = Arrays.asList(new Object[][] {
            {"RETRIEVE", 10},
            {"UNKNOWN", 3}
    });

    Method method = TitleOrderActionInfoRepository.class.getDeclaredMethod("convert", List.class);
    method.setAccessible(true);
    List<TitleOrderActionInfo> out = (List) method.invoke(repo, list);
    method.setAccessible(false);

    assertThat(out, is(notNullValue()));
    assertThat(out.size(), is(1));
    assertThat(out.get(0).getAction(), equalTo(CataloguingAction.RETRIEVE));
  }

  @Test
  public void initializeListFromArray() {
    List<Object[]> list = Arrays.asList(new Object[][] {
            {"Mr", 1},
            {"Ms", 10}
    });

    assertThat(list, is(notNullValue()));
    assertThat(list.size(), is(2));
  }
}
