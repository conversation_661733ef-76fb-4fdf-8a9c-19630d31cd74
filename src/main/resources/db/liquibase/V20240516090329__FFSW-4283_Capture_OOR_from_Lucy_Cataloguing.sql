CREATE TABLE IF NOT EXISTS onorder_event (
    id SERIAL PRIMARY KEY,
    date_time_created timestamptz NOT NULL,
    event_type text NOT NULL,
    entity_name text NOT NULL,
    entity_key text NOT NULL
);

GRANT ALL ON onorder_event TO PUBLIC;
GRANT ALL ON onorder_event TO pplsit;

CREATE OR REPLACE FUNCTION insert_into_onorder_events()
RETURNS TRIGGER AS $$
BEGIN

    IF NEW.status = 'SENT' THEN
        INSERT INTO onorder_event (date_time_created, event_type, entity_name, entity_key)
            SELECT CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'onorder', 'ORDER', controlnumber
            from bibrecordbatch brb
            JOIN public.bibrecordbatch_bibrecords bb on brb.pk = bb.bibrecordbatch_pk
            JOIN bibrecord br on bb.bibrecord_pk = br.pk
            WHERE brb.type = 'ONORDER'
            AND brb.pk = NEW.pk;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE TRIGGER trigger_insert_into_onorder_events
AFTER UPDATE ON bibrecordbatch
FOR EACH ROW
EXECUTE FUNCTION insert_into_onorder_events();

CREATE TABLE IF NOT EXISTS onorder_event_hist (
    id int NOT NULL,
    date_time_created timestamptz NOT NULL,
    event_type text NOT NULL,
    entity_name text NOT NULL,
    entity_key text NOT NULL
);

GRANT ALL ON onorder_event_hist TO PUBLIC;

CREATE OR REPLACE FUNCTION insert_into_onorder_events_history()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO onorder_event_history
    SELECT * from onorder_event
    WHERE id = NEW.id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_insert_into_onorder_events_history
AFTER UPDATE ON onorder_event
FOR EACH ROW
EXECUTE FUNCTION insert_into_onorder_events_history();

GRANT ALL ON trigger_insert_into_onorder_events_history TO PUBLIC;

GRANT ALL ON trigger_insert_into_onorder_events_history TO pplsit;
