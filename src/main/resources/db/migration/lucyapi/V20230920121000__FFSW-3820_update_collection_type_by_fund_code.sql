CREATE EXTENSION IF NOT EXISTS plpgsql WITH SCHEMA pg_catalog;

DROP TABLE IF EXISTS public.collection_type_by_fund;

CREATE TABLE IF NOT EXISTS public.collection_type_by_fund (
    customer_code text NOT NULL,
    fund text NOT NULL,
    collection text NOT NULL,
    reduce_quantity_to_one boolean NOT NULL,
    constraint collection_type_by_fund_primary_key primary key (customer_code, fund)
);

INSERT INTO public.collection_type_by_fund (customer_code, fund, collection, reduce_quantity_to_one) VALUES ('SUNSHINECOAST', 'BKCLUB', 'PRINT_LTD', true) ON CONFLICT DO NOTHING;
INSERT INTO public.collection_type_by_fund (customer_code, fund, collection, reduce_quantity_to_one) VALUES ('SUNSHINECOAST', 'GR', 'CD_MUSIC', false) ON CONFLICT DO NOTHING;
INSERT INTO public.collection_type_by_fund (customer_code, fund, collection, reduce_quantity_to_one) VALUES ('SUNSHINECOAST', 'LP', 'PRINT_LARGE', false) ON CONFLICT DO NOTHING;
