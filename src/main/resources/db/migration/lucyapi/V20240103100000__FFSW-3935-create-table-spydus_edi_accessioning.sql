ALTER TABLE public.lms_configuration ADD COLUMN IF NOT EXISTS spydus_edi boolean not null default false;
CREATE TABLE IF NOT EXISTS public.spydus_edi_orchestration (
                                                               id uuid NOT NULL,
                                                               customer_code varchar NOT NULL,
                                                               retry_count int4 NOT NULL default 0,
                                                               body_message text NOT NULL,
                                                               work_flow_step_status varchar NULL,
                                                               all_invoice_posted boolean not null default false,
                                                               CONSTRAINT spydus_edi_orchestration_pkey PRIMARY KEY (id));
CREATE TABLE IF NOT EXISTS public.spydus_edi_orchestration_completed_steps (
                                                             spydus_edi_orchestration_id uuid NOT NULL,
                                                             completed_steps varchar NULL,
                                                             CONSTRAINT spydus_edi_orchestration_completed_steps_fkey FOREIGN KEY (spydus_edi_orchestration_id) REFERENCES public.spydus_edi_orchestration(id)
                                                         );
