
CREATE TABLE IF NOT EXISTS public.incoming_message (
                                         id  varchar(36) NOT NULL,
                                         message_source varchar NULL,
                                         message_type varchar NULL,
                                         queue_name varchar NULL,
                                         body text NULL,
                                         hash varchar NULL,
                                         status varchar(30) NULL,
                                         retry_count int4 NULL,
                                         received_date_time timestamptz NULL,
                                         last_modified_date_time timestamptz NULL,
                                         CONSTRAINT incoming_message_pkey PRIMARY KEY (id)
);