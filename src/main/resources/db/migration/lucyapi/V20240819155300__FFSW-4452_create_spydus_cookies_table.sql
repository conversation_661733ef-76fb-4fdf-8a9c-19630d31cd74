CREATE TABLE IF NOT EXISTS public.spydus_cookies
(
    version varchar NOT NULL,
    cookie varchar NOT NULL,
    CONSTRAINT spydus_cookies_pkey PRIMARY KEY (version)
);

INSERT INTO public.spydus_cookies (version, cookie) VALUES ('V11_3_2024R1', '.AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8BI4EyEFPQJGmz-lwm9Wk5j2AhDNlQ61FIBs4Sp3fuJncANqxle9ZcHGuqH_Wf-P3KACtjIa88qZFXx4DQIiT2xkQUkxtUS0ySH-mTpLAa3cWvWzPo0bnYWMy0rv32IzYVwZOQmZEKqPOeehYT64wsk') ON CONFLICT DO NOTHING;
INSERT INTO public.spydus_cookies (version, cookie) VALUES ('V11_3_2023R3_1', '.AspNetCore.Antiforgery.PxIq_3VpCkA=CfDJ8Kk7U8lZV5hHllgnmhE_U0jh4k6lSURPk89ZEQND47ifBDVt3puKtfK0W_qbedRX-Mu1PJlxLCaR-BmcO9V_ExcfyUf94GvBMal6fA5zmK3Xq6kjzJXfaNPdpvuKT46npYhp7dtEn9GQ3Aet2usyoHI') ON CONFLICT DO NOTHING;
