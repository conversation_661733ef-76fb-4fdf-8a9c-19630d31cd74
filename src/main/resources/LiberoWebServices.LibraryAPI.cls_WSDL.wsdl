<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:s0="http://libero.com.au" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" targetNamespace="http://libero.com.au">
    <types>
        <s:schema elementFormDefault="qualified" targetNamespace="http://libero.com.au">
            <s:element name="AddArticle">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                        <s:element minOccurs="0" name="Title" type="s:string"/>
                        <s:element minOccurs="0" name="URL" type="s:string"/>
                        <s:element minOccurs="0" name="Writer1" type="s:string"/>
                        <s:element minOccurs="0" name="Writer2" type="s:string"/>
                        <s:element minOccurs="0" name="Writer3" type="s:string"/>
                        <s:element minOccurs="0" name="IssueNumber" type="s:string"/>
                        <s:element minOccurs="0" name="Notes" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="AddArticleResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="AddArticleResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="ItemStatusUpdateResponse">
                <s:complexContent>
                    <s:extension base="s0:Message">
                        <s:sequence>
                            <s:element minOccurs="0" name="PreviousExceptionStatus" type="s0:ExceptionCodes"/>
                            <s:element minOccurs="0" name="UpdatedExceptionStatus" type="s0:ExceptionCodes"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="Message">
                <s:sequence>
                    <s:element name="Status" type="s:long"/>
                    <s:element name="Message" type="s:string"/>
                    <s:element minOccurs="0" name="DueDate" type="s:string"/>
                    <s:element minOccurs="0" name="Reference" type="s:string"/>
                    <s:element minOccurs="0" name="Token" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ExceptionCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GridDescription" type="s:string"/>
                            <s:element minOccurs="0" name="DisallowNormalLoans" type="s:boolean"/>
                            <s:element minOccurs="0" name="PromptForConfirmAtIssue" type="s:boolean"/>
                            <s:element minOccurs="0" name="ResetToNormalViaIssue" type="s:boolean"/>
                            <s:element minOccurs="0" name="NoGMDFeeOnIssue" type="s:boolean"/>
                            <s:element minOccurs="0" name="DisallowRenewalsViaCir" type="s:boolean"/>
                            <s:element minOccurs="0" name="DisallowRenewalsViaOPAC" type="s:boolean"/>
                            <s:element minOccurs="0" name="DisallowAutoRenewals" type="s:boolean"/>
                            <s:element minOccurs="0" name="PromptForConfirmAtReturn" type="s:boolean"/>
                            <s:element minOccurs="0" name="ResetToNormalViaReturn" type="s:boolean"/>
                            <s:element minOccurs="0" name="NoFinesOnReturn" type="s:string"/>
                            <s:element minOccurs="0" name="SuppressOPACDisplay" type="s:boolean"/>
                            <s:element minOccurs="0" name="ExcludeItemNotes" type="s:boolean"/>
                            <s:element minOccurs="0" name="ItemStatusNotAvail" type="s:boolean"/>
                            <s:element minOccurs="0" name="ExcludeHoldingStatements" type="s:string"/>
                            <s:element minOccurs="0" name="TriggerNewItemsPrompt" type="s:boolean"/>
                            <s:element minOccurs="0" name="ChangeCodeOnSerialCheckin" type="s0:ExceptionCodes"/>
                            <s:element minOccurs="0" name="AllowShortLoans" type="s:boolean"/>
                            <s:element minOccurs="0" name="ShortLoansMins">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="10"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MinsBeforeCloseTime">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="10"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MinsAfterOpenTime">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="10"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="OnlyForReadingRoom" type="s:boolean"/>
                            <s:element minOccurs="0" name="SoundFile" type="s:string"/>
                            <s:element minOccurs="0" name="AllowselectiononPermanentLoan" type="s:boolean"/>
                            <s:element minOccurs="0" name="NotPermittedToReserve" type="s:boolean"/>
                            <s:element minOccurs="0" name="NotPermitFulFillRes" type="s:boolean"/>
                            <s:element minOccurs="0" name="ExcludeFromRes" type="s:boolean"/>
                            <s:element minOccurs="0" name="ResetBackException" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType abstract="true" name="Persistent"/>
            <s:complexType name="CheckIns">
                <s:complexContent>
                    <s:extension base="s0:Message">
                        <s:sequence>
                            <s:element maxOccurs="unbounded" minOccurs="0" name="CheckIn" nillable="true" type="s0:CheckIn"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfCheckIn">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="CheckIn" nillable="true" type="s0:CheckIn"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="CheckIn">
                <s:sequence>
                    <s:element minOccurs="0" name="VolumeNo" type="s:string"/>
                    <s:element minOccurs="0" name="IssueNo" type="s:string"/>
                    <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    <s:element minOccurs="0" name="Copies" type="s:long"/>
                    <s:element minOccurs="0" name="Status" type="s:string"/>
                    <s:element minOccurs="0" name="Binding" type="s:string"/>
                    <s:element minOccurs="0" name="DueDate" type="s:date"/>
                    <s:element minOccurs="0" name="DateReceived" type="s:date"/>
                    <s:element minOccurs="0" name="IssueReference" type="s:string"/>
                    <s:element minOccurs="0" name="ExceptionCode" type="s:string"/>
                    <s:element minOccurs="0" name="CollectionCode" type="s:string"/>
                    <s:element minOccurs="0" name="RoutingCopy" type="s:string"/>
                    <s:element minOccurs="0" name="CallNo" type="s:string"/>
                    <s:element minOccurs="0" name="OwnerBranch" type="s:string"/>
                    <s:element minOccurs="0" name="CurrentBranch" type="s:string"/>
                    <s:element minOccurs="0" name="StackLocation" type="s:string"/>
                    <s:element minOccurs="0" name="SubscriptionNote" type="s:string"/>
                    <s:element minOccurs="0" name="SubscriptionProcessingNote" type="s:string"/>
                    <s:element minOccurs="0" name="SerialProcessingNote" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:element name="AddHolding">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="rsn" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                        <s:element minOccurs="0" name="Acquisition" type="s:string"/>
                        <s:element minOccurs="0" name="Collection" type="s:string"/>
                        <s:element minOccurs="0" name="Location" type="s:string"/>
                        <s:element minOccurs="0" name="FundCC" type="s:string"/>
                        <s:element minOccurs="0" name="Supplier" type="s:string"/>
                        <s:element minOccurs="0" name="DateAcquired" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="AddHoldingResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="AddHoldingResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="AuditLog">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="Type" type="s:string"/>
                        <s:element minOccurs="0" name="DateFrom" type="s:date"/>
                        <s:element minOccurs="0" name="DateTo" type="s:date"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="AuditLogResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="AuditLogResult" type="s0:AuditLogResult"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="AuditLogResult">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" ref="s0:qryAuditRange"/>
                </s:sequence>
            </s:complexType>
            <s:element name="qryAuditRange" nillable="true" type="s0:AuditLog"/>
            <s:complexType name="ArrayOfAuditLog">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="AuditLog" nillable="true" type="s0:AuditLog"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="AuditLog">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="ClassName">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AccessKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DateModified" type="s:date"/>
                            <s:element minOccurs="0" name="TimeModified" type="s:time"/>
                            <s:element minOccurs="0" name="UserName" type="s:string"/>
                            <s:element minOccurs="0" name="Property">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="OldValue">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="2000"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NewValue">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="2000"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="5000"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Source" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:element name="Branch">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="BranchResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="BranchResult" type="s0:BranchList"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="BranchList">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Branches" nillable="true" type="s0:Branch"/>
                    <s:element minOccurs="0" name="Status" type="s:boolean"/>
                    <s:element minOccurs="0" name="Message" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfBranch">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Branch" nillable="true" type="s0:Branch"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Branch">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element minOccurs="0" name="BranchURL">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DisplayOrder" type="s:decimal"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GridDescription" type="s:string"/>
                            <s:element minOccurs="0" name="GroupCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Street">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="City">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Postcode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="8"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PostalAddress">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="70"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Department">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Telephone" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="FaxNumber" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="InternalNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PublicNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="eMail" type="s:string"/>
                            <s:element minOccurs="0" name="VirtualBranchIndicator" type="s:boolean"/>
                            <s:element minOccurs="0" name="OPACSelection" type="s:boolean"/>
                            <s:element minOccurs="0" name="OPACSelectionPickup" type="s:boolean"/>
                            <s:element minOccurs="0" name="BranchIMG">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="UnionMemberIndicator" type="s:boolean"/>
                            <s:element minOccurs="0" name="SlipPrinterHeader1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SlipPrinterHeader2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SlipPrinterHeader3">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SlipPrinterHeader4">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SlipPrinterHeader5">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SlipPrinterHeader6">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SlipPrinterHeader7">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SlipPrinterHeader8">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SlipPrinterHeader9">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="StackLocation">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="VirtualBranchCode" type="s:string"/>
                            <s:element minOccurs="0" name="ExportHoldings" type="s:boolean"/>
                            <s:element minOccurs="0" name="BibMap1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="BibMap2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="BibMap3">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ExcludeDisplay" type="s:boolean"/>
                            <s:element minOccurs="0" name="ReRegistrationDays" type="s:decimal"/>
                            <s:element minOccurs="0" name="ChargeFeeOnReRegistration" type="s:boolean"/>
                            <s:element minOccurs="0" name="MembershipFee" type="s:decimal"/>
                            <s:element minOccurs="0" name="TransactionType" type="s0:TransactionTypes"/>
                            <s:element minOccurs="0" name="ReRegReminderDays" type="s:decimal"/>
                            <s:element minOccurs="0" name="BranchInTransit" type="s:boolean"/>
                            <s:element minOccurs="0" name="OperatingHours" type="s0:ArrayOfOperatingHour"/>
                            <s:element minOccurs="0" name="InProcessing" type="s:boolean"/>
                            <s:element minOccurs="0" name="ILLPickup" type="s:boolean"/>
                            <s:element minOccurs="0" name="MainBranchTransfer" type="s:boolean"/>
                            <s:element minOccurs="0" name="RemoteTransfer" type="s:boolean"/>
                            <s:element minOccurs="0" name="ExportChanges" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:simpleType name="PhoneNumber">
                <s:restriction base="s:string">
                    <s:maxLength value="30"/>
                </s:restriction>
            </s:simpleType>
            <s:complexType name="TransactionTypes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="IncomeFund" type="s0:FundCodes"/>
                            <s:element minOccurs="0" name="IncomeCostCentre" type="s0:CostCentres"/>
                            <s:element minOccurs="0" name="DefaultChargeAmount" type="s:decimal"/>
                            <s:element minOccurs="0" name="TaxType" type="s0:TaxTypes"/>
                            <s:element minOccurs="0" name="OutgoingILLFeesDefault" type="s:boolean"/>
                            <s:element minOccurs="0" name="IncomingILLFeesDefault" type="s:boolean"/>
                            <s:element minOccurs="0" name="SpecialMembershipTransCode" type="s:boolean"/>
                            <s:element minOccurs="0" name="RestrictPatronSelfCheck" type="s:boolean"/>
                            <s:element minOccurs="0" name="DoNotDeleteTransCode" type="s:boolean"/>
                            <s:element minOccurs="0" name="LedgerAccount" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="FundCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="CostCentres">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="TaxTypes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="TaxPercentage" type="s:decimal"/>
                            <s:element minOccurs="0" name="CostTransaction" type="s0:CostTransactionTypes"/>
                            <s:element minOccurs="0" name="InclusiveTaxAmount" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="CostTransactionTypes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="InvoiceTransaction" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfOperatingHour">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="OperatingHour" nillable="true" type="s0:OperatingHour"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="OperatingHour">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="OrderNum">
                                <s:simpleType>
                                    <s:restriction base="s:long">
                                        <s:maxInclusive value="7"/>
                                        <s:minInclusive value="1"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Day" type="s:string"/>
                            <s:element minOccurs="0" name="ClosingTime" type="s:time"/>
                            <s:element minOccurs="0" name="OpeningTime" type="s:time"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:element name="Budget">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="FundCode" type="s:string"/>
                        <s:element minOccurs="0" name="CCode" type="s:string"/>
                        <s:element minOccurs="0" name="Year" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="BudgetResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="BudgetResult" type="s0:LBfin_Budgets_FundAllocation_qryGetBudget"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="LBfin_Budgets_FundAllocation_qryGetBudget">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="qryGetBudget">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="FundDes" type="s:string"/>
                                <s:element minOccurs="0" name="Cost_Centre" type="s:string"/>
                                <s:element minOccurs="0" name="Budget" type="s:decimal"/>
                                <s:element minOccurs="0" name="Committed" type="s:decimal"/>
                                <s:element minOccurs="0" name="Actual" type="s:decimal"/>
                                <s:element minOccurs="0" name="Total" type="s:decimal"/>
                                <s:element minOccurs="0" name="Variance" type="s:decimal"/>
                                <s:element minOccurs="0" name="Warn" type="s:string"/>
                                <s:element minOccurs="0" name="Over" type="s:string"/>
                                <s:element minOccurs="0" name="FiscalId" type="s:string"/>
                                <s:element minOccurs="0" name="BudType" type="s:string"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="CheckIn">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="CheckInResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="CheckInResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="CheckInIssues">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:string"/>
                        <s:element minOccurs="0" name="Acronym" type="s:string"/>
                        <s:element minOccurs="0" name="SubNo" type="s:string"/>
                        <s:element minOccurs="0" name="CheckIn" type="s0:CheckIn"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="CheckInIssuesResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="CheckInIssuesResult" type="s0:CheckIns"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="CheckOut">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="CheckOutResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="CheckOutResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="DeleteReservation">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:string"/>
                        <s:element minOccurs="0" name="QueueNumber" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="DeleteReservationResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="DeleteReservationResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetItemChangeList">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="dbName" type="s:string"/>
                        <s:element minOccurs="0" name="DateFrom" type="s:date"/>
                        <s:element minOccurs="0" name="DateTo" type="s:date"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetItemChangeListResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetItemChangeListResult" type="s0:ItemChangeList"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="ItemChangeList">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemDetailChanges" nillable="true" type="s0:ItemDetailChange"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfItemDetailChange">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemDetailChange" nillable="true" type="s0:ItemDetailChange"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ItemDetailChange">
                <s:simpleContent>
                    <s:extension base="s:string">
                        <s:attribute name="id" type="s:long"/>
                        <s:attribute name="action" type="s:long"/>
                        <s:attribute name="dateChanged" type="s:date"/>
                    </s:extension>
                </s:simpleContent>
            </s:complexType>
            <s:element name="GetItemDetails">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetItemDetailsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetItemDetailsResult" type="s0:StockItems"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="StockItems">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="UserDefinedFields" type="s0:ArrayOfUserDefinedFields"/>
                            <s:element minOccurs="0" name="ItemsOnHold" type="s0:ArrayOfItemsOnHold"/>
                            <s:element minOccurs="0" name="ItemArticle" type="s0:ArrayOfArticles"/>
                            <s:element minOccurs="0" name="ItemAuditHistory" type="s0:ArrayOfItemAuditHistoryItemString"/>
                            <s:element minOccurs="0" name="ItemCallNumber" type="s0:ArrayOfCallNumbers"/>
                            <s:element minOccurs="0" name="MoreNotes" type="s0:ArrayOfItemNotes"/>
                            <s:element minOccurs="0" name="CostTrans" type="s0:ArrayOfCostTransactions"/>
                            <s:element minOccurs="0" name="TitleBound" type="s0:ArrayOfTitlesInBoundVolumes"/>
                            <s:element minOccurs="0" name="IssuesYearlyCount" type="s0:ArrayOfIssuesCountByYear"/>
                            <s:element minOccurs="0" name="RenewalsYearlyCount" type="s0:ArrayOfRenewalsCountByYear"/>
                            <s:element minOccurs="0" name="RSNText" type="s:string"/>
                            <s:element minOccurs="0" name="Title">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GMD" type="s0:LongString"/>
                            <s:element minOccurs="0" name="TotalItemsOnRSN" type="s:decimal"/>
                            <s:element name="Barcode" type="s0:LongString"/>
                            <s:element minOccurs="0" name="LendingStatus" type="s0:LongString"/>
                            <s:element minOccurs="0" name="StatusDescription" type="s0:LongString"/>
                            <s:element minOccurs="0" name="SerialRecord" type="s:boolean"/>
                            <s:element minOccurs="0" name="CallNumber" type="s0:LongString"/>
                            <s:element minOccurs="0" name="InventoryNumber" type="s0:LongString"/>
                            <s:element minOccurs="0" name="VolumeTitle" type="s0:LongString"/>
                            <s:element minOccurs="0" name="VolumeURL" type="s0:LongString"/>
                            <s:element minOccurs="0" name="OwnerBranch" type="s0:Branch"/>
                            <s:element minOccurs="0" name="LastSighted" type="s:string"/>
                            <s:element minOccurs="0" name="TotalCopies">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="10"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="TotalItemsInKit" type="s0:LongString"/>
                            <s:element minOccurs="0" name="SpineLabels" type="s:decimal"/>
                            <s:element minOccurs="0" name="RenewalCount" type="s:long"/>
                            <s:element minOccurs="0" name="DoNotDelete" type="s:boolean"/>
                            <s:element minOccurs="0" name="DeletionNotes" type="s0:LongString"/>
                            <s:element minOccurs="0" name="MaxReserves" type="s:decimal"/>
                            <s:element minOccurs="0" name="BoxNumber">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="150"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="HoldingsRange">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SecurityMarkerType" type="s:string"/>
                            <s:element minOccurs="0" name="ItemNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="65536"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ItemException" type="s:string"/>
                            <s:element minOccurs="0" name="ExceptionProgressed" type="s:boolean"/>
                            <s:element minOccurs="0" name="Collection" type="s:string"/>
                            <s:element minOccurs="0" name="CurrentBranch" type="s0:Branch"/>
                            <s:element minOccurs="0" name="TransitMessage">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="500"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="LastTransit" type="s:string"/>
                            <s:element minOccurs="0" name="LastTransitDateTime" type="s:string"/>
                            <s:element minOccurs="0" name="LastTransitDate" type="s:date"/>
                            <s:element minOccurs="0" name="LastTransitTime" type="s:time"/>
                            <s:element minOccurs="0" name="TransitFrom" type="s0:Branch"/>
                            <s:element minOccurs="0" name="TransitTo" type="s0:Branch"/>
                            <s:element minOccurs="0" name="TrArrived" type="s:string"/>
                            <s:element minOccurs="0" name="TrArrBranch" type="s0:Branch"/>
                            <s:element minOccurs="0" name="Statistic1" type="s0:ItemStatistics1"/>
                            <s:element minOccurs="0" name="Statistic1Code" type="s:string"/>
                            <s:element minOccurs="0" name="Statistic2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Statistic3" type="s0:ItemStatistics3"/>
                            <s:element minOccurs="0" name="Statistic4" type="s0:ItemStatistics4"/>
                            <s:element minOccurs="0" name="LocalRecordRID" type="s:string"/>
                            <s:element minOccurs="0" name="SerialsSortCode" type="s:string"/>
                            <s:element minOccurs="0" name="CirculationNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="32767"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ExceptionDate" type="s:date"/>
                            <s:element minOccurs="0" name="ExceptionDateTime" type="s0:Horolog"/>
                            <s:element minOccurs="0" name="FloatingStock" type="s:boolean"/>
                            <s:element minOccurs="0" name="JournalSummary" type="s:boolean"/>
                            <s:element minOccurs="0" name="StackLocation" type="s0:StackLocationCodes"/>
                            <s:element minOccurs="0" name="LastBorrowedDate" type="s:date"/>
                            <s:element minOccurs="0" name="LastRenewalDate" type="s:date"/>
                            <s:element minOccurs="0" name="NewItemActDate" type="s:date"/>
                            <s:element minOccurs="0" name="BranchRotationUseByDate" type="s:date"/>
                            <s:element minOccurs="0" name="ReviewDate" type="s:date"/>
                            <s:element minOccurs="0" name="CreationUser" type="s:string"/>
                            <s:element minOccurs="0" name="CreationDateTime" type="s0:Horolog"/>
                            <s:element minOccurs="0" name="ILLFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="ILLDue" type="s:date"/>
                            <s:element minOccurs="0" name="ILLID" type="s:string"/>
                            <s:element minOccurs="0" name="ILLEntry" type="s:string"/>
                            <s:element minOccurs="0" name="BranchPurchasedBy" type="s0:Branch"/>
                            <s:element minOccurs="0" name="AcquisitionType" type="s0:AcquisitionTypes"/>
                            <s:element minOccurs="0" name="ExpiryDate" type="s:date"/>
                            <s:element minOccurs="0" name="Keyword" type="s:string"/>
                            <s:element minOccurs="0" name="ClaimsDisputeMemberCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ClaimsDisputeMessage">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ItemExceptionBefClaimDisp" type="s:string"/>
                            <s:element minOccurs="0" name="CurrentBorrowerFileKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GST" type="s:double"/>
                            <s:element minOccurs="0" name="ForCurrencyCode" type="s0:CurrencyConversion"/>
                            <s:element minOccurs="0" name="ForCurrencyValue" type="s:decimal"/>
                            <s:element minOccurs="0" name="FundCostCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AllocatedFCC1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AllocatedAmount1" type="s:decimal"/>
                            <s:element minOccurs="0" name="AllocatedFCC2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AllocatedAmount2" type="s:decimal"/>
                            <s:element minOccurs="0" name="AllocatedFCC3">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AllocatedAmount3" type="s:decimal"/>
                            <s:element minOccurs="0" name="AllocatedFCC4">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AllocatedAmount4" type="s:decimal"/>
                            <s:element minOccurs="0" name="AllocatedFCC5">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AllocatedAmount5" type="s:decimal"/>
                            <s:element minOccurs="0" name="OnHoldMemberCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ItemValue" type="s:decimal"/>
                            <s:element minOccurs="0" name="PastBorrower1" type="s0:MemberCode"/>
                            <s:element minOccurs="0" name="PastBorrower1BorrowedAtTimestamp" type="s:dateTime"/>
                            <s:element minOccurs="0" name="PastBorrower2" type="s0:MemberCode"/>
                            <s:element minOccurs="0" name="PastBorrower2BorrowedAtTimestamp" type="s:dateTime"/>
                            <s:element minOccurs="0" name="PastBorrower3" type="s0:MemberCode"/>
                            <s:element minOccurs="0" name="PastBorrower3BorrowedAtTimestamp" type="s:dateTime"/>
                            <s:element minOccurs="0" name="PastBorrower4" type="s0:MemberCode"/>
                            <s:element minOccurs="0" name="PastBorrower4BorrowedAtTimestamp" type="s:dateTime"/>
                            <s:element minOccurs="0" name="PastBorrower5" type="s0:MemberCode"/>
                            <s:element minOccurs="0" name="PastBorrower5BorrowedAtTimestamp" type="s:dateTime"/>
                            <s:element minOccurs="0" name="StackOrder" type="s0:StackRequests"/>
                            <s:element minOccurs="0" name="LastReturnBranch" type="s0:Branch"/>
                            <s:element minOccurs="0" name="LastReturnByUser">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="LastReturnDate" type="s:date"/>
                            <s:element minOccurs="0" name="LastReturnTime" type="s:time"/>
                            <s:element minOccurs="0" name="LastStocktake" type="s:date"/>
                            <s:element minOccurs="0" name="LastSeenDate" type="s:date"/>
                            <s:element minOccurs="0" name="DatePurchased" type="s:date"/>
                            <s:element minOccurs="0" name="ReplacementCost" type="s:double"/>
                            <s:element minOccurs="0" name="OrderCode" type="s:string"/>
                            <s:element minOccurs="0" name="TransferFlag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ExcludeItemNotes" type="s:boolean"/>
                            <s:element minOccurs="0" name="ItemField16" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField01" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField02" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField03" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField04" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField05" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField06" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField07" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField08" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField09" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField10" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField11" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField12" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField13" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField14" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField15" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField17" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField18" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField19" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField20" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField21" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField22" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField23" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField24" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField25" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField26" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField27" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="SupplierCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ItemField27Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField28" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField28Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField29" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField29Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField30" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField30Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField31" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField31Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField32" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField32Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="TitleLoansLastYear" type="s:double"/>
                            <s:element minOccurs="0" name="TitleLoansPreviousToLastYear" type="s:double"/>
                            <s:element minOccurs="0" name="TitleLoansThisYear" type="s:double"/>
                            <s:element minOccurs="0" name="TitleRenewalsLastYear" type="s:double"/>
                            <s:element minOccurs="0" name="TitleRenewalsThisYear" type="s:double"/>
                            <s:element minOccurs="0" name="TitleRenPrevToLastYr" type="s:double"/>
                            <s:element minOccurs="0" name="ItemRenewalsLastYear" type="s:double"/>
                            <s:element minOccurs="0" name="ItemRenewalsThisYear" type="s:decimal"/>
                            <s:element minOccurs="0" name="ItemRenPrevToLastYr" type="s:double"/>
                            <s:element minOccurs="0" name="TitleSortKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="250"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="LoansLastYear" type="s:decimal"/>
                            <s:element minOccurs="0" name="LoansPreviousToLastYear" type="s:decimal"/>
                            <s:element minOccurs="0" name="LoansThisYear" type="s:decimal"/>
                            <s:element minOccurs="0" name="OrderLine">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="10"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="OrderNumber" type="s:string"/>
                            <s:element minOccurs="0" name="TransferFlagString">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Author">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="250"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ClaimsDisputeDateTime">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ClaimsLostMemberCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ClaimsLostMessage">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ClaimsLostDateTime">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="CurrentBorrower" type="s:string"/>
                            <s:element minOccurs="0" name="CurrentBorrowerID" type="s0:s_Member"/>
                            <s:element minOccurs="0" name="dateDueString">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="11"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DateDue" type="s:date"/>
                            <s:element minOccurs="0" name="HighestReplacementCost" type="s:decimal"/>
                            <s:element minOccurs="0" name="DateDueInternal" type="s:date"/>
                            <s:element minOccurs="0" name="TimeDueInternal" type="s:time"/>
                            <s:element minOccurs="0" name="DateDueExternal" type="s:date"/>
                            <s:element minOccurs="0" name="PermanentLoanMemberCode" type="s0:MemberCode"/>
                            <s:element minOccurs="0" name="PermanentLoanMember" type="s0:s_Member"/>
                            <s:element minOccurs="0" name="PermanentLoanReserve" type="s:decimal"/>
                            <s:element minOccurs="0" name="PermanentLoanException" type="s0:ExceptionCodes"/>
                            <s:element minOccurs="0" name="TimesIssued" type="s:long"/>
                            <s:element minOccurs="0" name="WebOPACDisplay" type="s:boolean"/>
                            <s:element minOccurs="0" name="LastCostTransactionNumber" type="s:long"/>
                            <s:element minOccurs="0" name="DefaultCostN" type="s:decimal"/>
                            <s:element minOccurs="0" name="DefaultCost" type="s:string"/>
                            <s:element minOccurs="0" name="ExceptionFlag" type="s:string"/>
                            <s:element minOccurs="0" name="TotalIssues" type="s:decimal"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfUserDefinedFields">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="UserDefinedFields" nillable="true" type="s0:UserDefinedFields"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="UserDefinedFields">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Field01" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field02" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field03" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field04" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field05" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field06" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field07" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field08" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field09" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field10" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field11" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field12" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field13" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field14" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field15" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field16" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field17" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field18" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field19" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field20" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field21" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field22" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field23" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field24" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field25" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field26" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field27" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field27Description" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="Field28" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field28Description" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="Field29" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field29Description" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="Field30" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field30Description" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="Field31" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field31Description" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="Field32" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="Field32Description" type="s0:UserDefinedFieldCodeAndDescription"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:simpleType name="UserDefinedField">
                <s:restriction base="s:string">
                    <s:maxLength value="400"/>
                </s:restriction>
            </s:simpleType>
            <s:simpleType name="UserDefinedFieldCodeAndDescription">
                <s:restriction base="s:string">
                    <s:maxLength value="453"/>
                </s:restriction>
            </s:simpleType>
            <s:complexType name="ArrayOfItemsOnHold">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemsOnHold" nillable="true" type="s0:ItemsOnHold"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ItemsOnHold">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="HoldNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="9999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NoticeSentFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="PickupPoint" type="s0:PickupPoints"/>
                            <s:element minOccurs="0" name="MemberName">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MemberDetails">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="MemberCode" type="s0:MemberCode"/>
                            <s:element minOccurs="0" name="BranchToCollectAt" type="s0:Branch"/>
                            <s:element minOccurs="0" name="DateHoldPlaced" type="s:date"/>
                            <s:element minOccurs="0" name="TimeHoldPlaced" type="s:time"/>
                            <s:element minOccurs="0" name="CollectByDate" type="s:date"/>
                            <s:element minOccurs="0" name="Notes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="80"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AvailabilityNotice">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NotificationMethod" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="ShortTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NoReserveAvailable" type="s:boolean"/>
                            <s:element minOccurs="0" name="LastNotified" type="s:string"/>
                            <s:element minOccurs="0" name="LastNotifiedBy" type="s0:NotificationMethods"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="PickupPoints">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="12"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="250"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GridDescription" type="s:string"/>
                            <s:element minOccurs="0" name="PickupPointDays" type="s0:ArrayOfPickupPointDays"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfPickupPointDays">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="PickupPointDays" nillable="true" type="s0:PickupPointDays"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="PickupPointDays">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Days" type="s:string"/>
                            <s:element minOccurs="0" name="FromTime" type="s:string"/>
                            <s:element minOccurs="0" name="ToTime" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:simpleType name="MemberCode">
                <s:restriction base="s:string">
                    <s:maxLength value="20"/>
                </s:restriction>
            </s:simpleType>
            <s:complexType name="NotificationMethods">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DefaultNotfyMethod" type="s:boolean"/>
                            <s:element minOccurs="0" name="DoNotChargePerMember" type="s:boolean"/>
                            <s:element minOccurs="0" name="DoNotChargePerClaim" type="s:boolean"/>
                            <s:element minOccurs="0" name="DoNotChargeFeePerMember" type="s:boolean"/>
                            <s:element minOccurs="0" name="DoNotChargeFeePerItem" type="s:boolean"/>
                            <s:element minOccurs="0" name="SendDirectEmail" type="s:boolean"/>
                            <s:element minOccurs="0" name="SendDirectSMS" type="s:boolean"/>
                            <s:element minOccurs="0" name="OverdueNotice" type="s:boolean"/>
                            <s:element minOccurs="0" name="ResAvailNotice" type="s:boolean"/>
                            <s:element minOccurs="0" name="CourtRemNotice" type="s:boolean"/>
                            <s:element minOccurs="0" name="ReRegNotice" type="s:boolean"/>
                            <s:element minOccurs="0" name="GeneralNotif" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfArticles">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Articles" nillable="true" type="s0:Articles"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Articles">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="ArticleTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ArticleURL">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Writer1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Writer2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Writer3">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="VolumeTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="IssueTitleNumber" type="s:decimal"/>
                            <s:element minOccurs="0" name="Notes" type="s:string"/>
                            <s:element minOccurs="0" name="Pages" type="s0:ArrayOfArticlePage"/>
                            <s:element minOccurs="0" name="RSN" type="s0:s_Biblio"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfArticlePage">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ArticlePage" nillable="true" type="s0:ArticlePage"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArticlePage">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="PatronComments" type="s0:ArrayOfArticleUserComments"/>
                            <s:element minOccurs="0" name="RawText" type="s:string"/>
                            <s:element minOccurs="0" name="RawImage" type="s:base64Binary"/>
                            <s:element minOccurs="0" name="RawDocument" type="s:base64Binary"/>
                            <s:element minOccurs="0" name="DocumentType" type="s:string"/>
                            <s:element name="PageNo" type="s:long"/>
                            <s:element minOccurs="0" name="PageRSN" type="s:string"/>
                            <s:element minOccurs="0" name="SequenceNo" type="s:long"/>
                            <s:element minOccurs="0" name="Filename">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Verified" type="s:boolean"/>
                            <s:element minOccurs="0" name="DocumentSHA1">
                                <s:simpleType>
                                    <s:restriction base="s:base64Binary">
                                        <s:maxLength value="40"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ImageSHA1">
                                <s:simpleType>
                                    <s:restriction base="s:base64Binary">
                                        <s:maxLength value="40"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfArticleUserComments">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ArticleUserComments" nillable="true" type="s0:ArticleUserComments"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArticleUserComments">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Member" type="s0:s_Member"/>
                            <s:element name="commentNo" type="s:long"/>
                            <s:element minOccurs="0" name="LatestDate" type="s:date"/>
                            <s:element minOccurs="0" name="LatestTime" type="s:time"/>
                            <s:element minOccurs="0" name="Rating" type="s:long"/>
                            <s:element minOccurs="0" name="OpacDisplay" type="s:boolean"/>
                            <s:element minOccurs="0" name="Moderated" type="s:boolean"/>
                            <s:element minOccurs="0" name="ModeratedText" type="s:string"/>
                            <s:element minOccurs="0" name="Comments">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="32767"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="s_Member">
                <s:sequence>
                    <s:element name="BorrowerID" type="s:decimal"/>
                    <s:element name="BorrowerCode">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="20"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                </s:sequence>
            </s:complexType>
            <s:complexType name="s_Biblio">
                <s:sequence>
                    <s:element minOccurs="0" name="RID" type="s:string"/>
                    <s:element name="RSN" type="s:decimal"/>
                    <s:element minOccurs="0" name="Title">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="200"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="MainAuthor">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="255"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfItemAuditHistoryItemString">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemAuditHistoryItem" nillable="true" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfCallNumbers">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="CallNumbers" nillable="true" type="s0:CallNumbers"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="CallNumbers">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="CallNumber">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="500"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MainCallNumber">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MainCallFlag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DateSetAsMainCallNumber" type="s0:Horolog"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:simpleType name="Horolog">
                <s:restriction base="s:dateTime"/>
            </s:simpleType>
            <s:complexType name="ArrayOfItemNotes">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemNotes" nillable="true" type="s0:ItemNotes"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ItemNotes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="LastEditDetails">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="40"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="NoteLine" type="s:decimal"/>
                            <s:element minOccurs="0" name="Note">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfCostTransactions">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="CostTransactions" nillable="true" type="s0:CostTransactions"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="CostTransactions">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="TransNumber" type="s:long"/>
                            <s:element minOccurs="0" name="Type" type="s0:CostTransactionTypes"/>
                            <s:element minOccurs="0" name="TransDate" type="s:date"/>
                            <s:element minOccurs="0" name="Amount" type="s:decimal"/>
                            <s:element minOccurs="0" name="FundCost" type="s:string"/>
                            <s:element minOccurs="0" name="OrderNum" type="s:string"/>
                            <s:element minOccurs="0" name="DocketNum" type="s:string"/>
                            <s:element minOccurs="0" name="InvoiceNum" type="s:string"/>
                            <s:element minOccurs="0" name="Description" type="s:string"/>
                            <s:element minOccurs="0" name="BudgetYear" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfTitlesInBoundVolumes">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="TitlesInBoundVolumes" nillable="true" type="s0:TitlesInBoundVolumes"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="TitlesInBoundVolumes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Number" type="s:decimal"/>
                            <s:element minOccurs="0" name="TitleInBoundVolumes" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfIssuesCountByYear">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="IssuesCountByYear" nillable="true" type="s0:IssuesCountByYear"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="IssuesCountByYear">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Year" type="s:decimal"/>
                            <s:element minOccurs="0" name="IssueCount" type="s:decimal"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfRenewalsCountByYear">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="RenewalsCountByYear" nillable="true" type="s0:RenewalsCountByYear"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="RenewalsCountByYear">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Year" type="s:decimal"/>
                            <s:element minOccurs="0" name="RenewalCount" type="s:decimal"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:simpleType name="LongString">
                <s:restriction base="s:string">
                    <s:maxLength value="400"/>
                </s:restriction>
            </s:simpleType>
            <s:complexType name="ItemStatistics1">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ItemStatistics3">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ItemStatistics4">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="StackLocationCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element minOccurs="0" name="Branch" type="s0:Branch"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="AcquisitionTypes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ProcessOrders" type="s:boolean"/>
                            <s:element minOccurs="0" name="ProcessClaims" type="s:boolean"/>
                            <s:element minOccurs="0" name="AllowDelivery" type="s:boolean"/>
                            <s:element minOccurs="0" name="ApplyToBudget" type="s:long"/>
                            <s:element minOccurs="0" name="ExcludeFromNewItemList" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="CurrencyConversion">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Rate" type="s:string"/>
                            <s:element minOccurs="0" name="AsAtDate" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="StackRequests">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="StackID" type="s:string"/>
                            <s:element minOccurs="0" name="DateCreated" type="s0:Horolog"/>
                            <s:element minOccurs="0" name="DateShow" type="s:date"/>
                            <s:element minOccurs="0" name="TimeShow" type="s:string"/>
                            <s:element name="Barcode" type="s0:s_StockItems"/>
                            <s:element minOccurs="0" name="OnlyForReadingRoomIndicator" type="s:string"/>
                            <s:element minOccurs="0" name="PickupPointCode" type="s0:PickupPoints"/>
                            <s:element minOccurs="0" name="StatusCode" type="s0:StackStatus"/>
                            <s:element minOccurs="0" name="DateUnfilled" type="s:date"/>
                            <s:element minOccurs="0" name="Notes" type="s:string"/>
                            <s:element minOccurs="0" name="PublicationYear" type="s:string"/>
                            <s:element minOccurs="0" name="Volume" type="s:string"/>
                            <s:element minOccurs="0" name="ArticleAuthor" type="s:string"/>
                            <s:element minOccurs="0" name="ArticleIssue" type="s:string"/>
                            <s:element minOccurs="0" name="ArticleTitle" type="s:string"/>
                            <s:element minOccurs="0" name="ArticlePages" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="s_StockItems">
                <s:sequence>
                    <s:element name="Barcode" type="s0:LongString"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="StackStatus">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GridDescription">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:element name="GetItemsDue">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="Date" type="s:date"/>
                        <s:element minOccurs="0" name="Barcode" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetItemsDueResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetItemsDueResult" type="s0:LBmem_Loans_qryItemsDue"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="LBmem_Loans_qryItemsDue">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="qryItemsDue">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="BorrowerCode" type="s:string"/>
                                <s:element minOccurs="0" name="Barcode" type="s:string"/>
                                <s:element minOccurs="0" name="DateIssued" type="s:date"/>
                                <s:element minOccurs="0" name="OutstandingDays" type="s:long"/>
                                <s:element minOccurs="0" name="RenewalCount" type="s:string"/>
                                <s:element minOccurs="0" name="DueDate" type="s:date"/>
                                <s:element minOccurs="0" name="ReserveCount" type="s:string"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="GetMemberChangeList">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="dbName" type="s:string"/>
                        <s:element minOccurs="0" name="DateFrom" type="s:date"/>
                        <s:element minOccurs="0" name="DateTo" type="s:date"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetMemberChangeListResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetMemberChangeListResult" type="s0:MemberChangeList"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="MemberChangeList">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="MemberDetailChanges" nillable="true" type="s0:MemberDetailChange"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfMemberDetailChange">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="MemberDetailChange" nillable="true" type="s0:MemberDetailChange"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="MemberDetailChange">
                <s:simpleContent>
                    <s:extension base="s:string">
                        <s:attribute name="id" type="s:long"/>
                        <s:attribute name="action" type="s:string"/>
                        <s:attribute name="dateChanged" type="s:date"/>
                    </s:extension>
                </s:simpleContent>
            </s:complexType>
            <s:element name="GetMemberDetails">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberId" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetMemberDetailsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetMemberDetailsResult" type="s0:Member"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="Member">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Loans" type="s0:ArrayOfLoans"/>
                            <s:element minOccurs="0" name="PermanentLoans" type="s0:ArrayOfPermanentLoans"/>
                            <s:element minOccurs="0" name="MemberStatus" type="s0:ArrayOfStatusCodes"/>
                            <s:element minOccurs="0" name="Reserves" type="s0:ArrayOfReserves"/>
                            <s:element minOccurs="0" name="Finance" type="s0:ArrayOfFinanceItemString"/>
                            <s:element minOccurs="0" name="AddressSchedule" type="s0:ArrayOfAddressSchedule"/>
                            <s:element minOccurs="0" name="AddressTemplate" type="s0:ArrayOfAddressTemplate"/>
                            <s:element minOccurs="0" name="SDIProfile" type="s0:ArrayOfSDIProfile"/>
                            <s:element minOccurs="0" name="SDIMemberTemplate" type="s0:ArrayOfSDIMemberTemplate"/>
                            <s:element minOccurs="0" name="SystemMessage" type="s0:ArrayOfSystemMessages"/>
                            <s:element minOccurs="0" name="RoutingList" type="s0:ArrayOfRouting"/>
                            <s:element minOccurs="0" name="ReadingRoomItems" type="s0:ArrayOfReadingRoom"/>
                            <s:element minOccurs="0" name="AdditionalCodes" type="s0:ArrayOfMemberAdditionalCodes"/>
                            <s:element minOccurs="0" name="StackRequests" type="s0:ArrayOfStackRequests"/>
                            <s:element minOccurs="0" name="Holds" type="s0:ArrayOfItemsOnHold"/>
                            <s:element minOccurs="0" name="AmountOutstanding" type="s:decimal"/>
                            <s:element minOccurs="0" name="NextRegistrationDate" type="s:date"/>
                            <s:element minOccurs="0" name="LDAPName">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ErrMsg" type="s:string"/>
                            <s:element minOccurs="0" name="GuarantorEmail">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="FlagsMemo">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="32767"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AddressToPrint" type="s:decimal"/>
                            <s:element minOccurs="0" name="AgeGroup" type="s0:AgeGroupCodes"/>
                            <s:element minOccurs="0" name="AltAddress1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DoMandatoryCheck" type="s:boolean"/>
                            <s:element minOccurs="0" name="AltAddress2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AltAddressCity">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AltAddressPostCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="10"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AltAddressCountry">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="BorrowerID" type="s:decimal"/>
                            <s:element minOccurs="0" name="BusinessPhone" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="BusinessPhoneShrunk" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="DateOfBirth" type="s:date"/>
                            <s:element minOccurs="0" name="DateOfBirthStr">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="15"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DateOfJoining" type="s:date"/>
                            <s:element minOccurs="0" name="DateRegistrationDue" type="s:date"/>
                            <s:element minOccurs="0" name="EmailAddress">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DateOfExpiry" type="s:date"/>
                            <s:element minOccurs="0" name="LastBorrowedDate" type="s:date"/>
                            <s:element minOccurs="0" name="FaxNumber" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="FaxNumberShrunk">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="FileKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="21"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ForbiddenCollections">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GivenNames">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GuarantorMember" type="s0:s_Member"/>
                            <s:element minOccurs="0" name="GuarantorRelationship" type="s:string"/>
                            <s:element minOccurs="0" name="GuarantorIdentification" type="s:string"/>
                            <s:element minOccurs="0" name="GuarantorAddress1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GuarantorAddress2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GuarantorAddressCity">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GuarantorAddressPostCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GuarantorAddressCountry">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GuarantorName">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GuarantorPhone" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="GuarantorPhoneShrunk" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="GuarantorMobile" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="HomePhone" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="HomePhoneShrunk" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="HistoryFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="Identification">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="LanguageType" type="s0:LanguageCodes_Parameters"/>
                            <s:element minOccurs="0" name="Locality" type="s0:LocalityCodes"/>
                            <s:element minOccurs="0" name="MaterialResponsibiltyFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="UnionDB" type="s:string"/>
                            <s:element minOccurs="0" name="UnionBranch" type="s:string"/>
                            <s:element minOccurs="0" name="UnionBranchName" type="s:string"/>
                            <s:element minOccurs="0" name="DateNoLoan" type="s:date"/>
                            <s:element minOccurs="0" name="LastActivity" type="s:date"/>
                            <s:element minOccurs="0" name="CopyRightFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="NoMarketing" type="s:boolean"/>
                            <s:element minOccurs="0" name="DisableReserveHistoryFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="DisableInternetUsageFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="NotifyMethodCode" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="NotifyMethodOverdueClaims" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="NotifyMethodOrderClaims" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="NotifyMethodHolds" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="NotifyMethodDateDue" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="NotifyMethodReReg" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="MobilePhone" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="NotifySmartPhone" type="s:boolean"/>
                            <s:element minOccurs="0" name="MobilePhoneShrunk" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="Organisation">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="BankName">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="BankAccountCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="BankAccountNo">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="IBAN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="BIC">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ResAddress1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ResAddress2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ResAddressCity">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ResAddressPostCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ResAddressCountry">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="RestrictLendPermitFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="UseAddressSchedule" type="s:boolean"/>
                            <s:element minOccurs="0" name="ReRegReminderSent" type="s:boolean"/>
                            <s:element minOccurs="0" name="Gender">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                        <s:enumeration value="M"/>
                                        <s:enumeration value="F"/>
                                        <s:enumeration value="O"/>
                                        <s:enumeration value="X"/>
                                        <s:enumeration value="U"/>
                                        <s:enumeration value="N"/>
                                        <s:enumeration value=""/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ShortName">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SpecialMembershipExpiryDate" type="s:date"/>
                            <s:element minOccurs="0" name="Title">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="TotalCardUsage" type="s:decimal"/>
                            <s:element minOccurs="0" name="NotesFlag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:enumeration value="2"/>
                                        <s:enumeration value="1"/>
                                        <s:enumeration value="0"/>
                                        <s:enumeration value=""/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MaxPermanentLoans" type="s:decimal"/>
                            <s:element minOccurs="0" name="MaxLoans" type="s:decimal"/>
                            <s:element minOccurs="0" name="GuarantorPhoneFlag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="1"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="BlackListCode" type="s0:MemberStatusCodes"/>
                            <s:element minOccurs="0" name="Branch" type="s0:Branch"/>
                            <s:element minOccurs="0" name="Category" type="s0:Categories"/>
                            <s:element minOccurs="0" name="Surname">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="HomeLibraryIndicator">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:enumeration value="Y"/>
                                        <s:enumeration value="N"/>
                                        <s:enumeration value=""/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="LoanReminder" type="s:boolean"/>
                            <s:element minOccurs="0" name="Occupation">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PostalAddress1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PostalAddress2">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PostalCity">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PostalPostCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PostalCountry">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AllowDirectDebit" type="s:boolean"/>
                            <s:element minOccurs="0" name="DateLastEdit" type="s:date"/>
                            <s:element minOccurs="0" name="DateLastRegistered" type="s:date"/>
                            <s:element minOccurs="0" name="AuthenticationFailures" type="s:long"/>
                            <s:element minOccurs="0" name="DaysRegisteredEarlier" type="s:long"/>
                            <s:element minOccurs="0" name="BorField01" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField02" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField03" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField04" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField05" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField06" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField07" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField08" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField09" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField10" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField11" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField12" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField13" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField14" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField15" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField16" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField17" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField18" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField19" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField20" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField21" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField22" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField23" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField24" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField25" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField26" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField27" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField28" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField29" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField30" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField31" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="BorField32" type="s0:UserDefinedField"/>
                            <s:element name="BorrowerCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AddressToUseDescription">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="11"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DateLastBorrowed" type="s:date"/>
                            <s:element minOccurs="0" name="TotalHolds" type="s:decimal"/>
                            <s:element minOccurs="0" name="TotalReserves" type="s:decimal"/>
                            <s:element minOccurs="0" name="TotalILLRequests" type="s:decimal"/>
                            <s:element minOccurs="0" name="FamilyMemberCount" type="s:decimal"/>
                            <s:element minOccurs="0" name="IsPassword" type="s:boolean"/>
                            <s:element minOccurs="0" name="IsPIN" type="s:boolean"/>
                            <s:element minOccurs="0" name="IsNote" type="s:boolean"/>
                            <s:element minOccurs="0" name="IsAdditional" type="s:boolean"/>
                            <s:element minOccurs="0" name="ImagePath">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="300"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AdditionalCode" type="s:string"/>
                            <s:element minOccurs="0" name="LanguageText">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="300"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SelfCheckPin" type="s:string"/>
                            <s:element minOccurs="0" name="Password" type="s:string"/>
                            <s:element minOccurs="0" name="CashOnly" type="s:boolean"/>
                            <s:element minOccurs="0" name="TotalClaimReturned" type="s:decimal"/>
                            <s:element minOccurs="0" name="TotalClaimNeverBorrowed" type="s:decimal"/>
                            <s:element minOccurs="0" name="TotalClaimLost" type="s:decimal"/>
                            <s:element minOccurs="0" name="TotalCurrentCR" type="s:decimal"/>
                            <s:element minOccurs="0" name="TotalCurrentCNB" type="s:decimal"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfLoans">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Loans" nillable="true" type="s0:Loans"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Loans">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Claims" type="s0:ArrayOfClaims"/>
                            <s:element name="Barcode" type="s0:s_StockItems"/>
                            <s:element minOccurs="0" name="DateIssued" type="s:date"/>
                            <s:element minOccurs="0" name="TimeIssued" type="s:time"/>
                            <s:element minOccurs="0" name="DateRenewed" type="s:date"/>
                            <s:element minOccurs="0" name="TimeShow" type="s:string"/>
                            <s:element minOccurs="0" name="InternalDueDate" type="s:string"/>
                            <s:element minOccurs="0" name="RenewalCount" type="s:string"/>
                            <s:element minOccurs="0" name="CourtesyReminderSent" type="s:boolean"/>
                            <s:element minOccurs="0" name="ClaimLevel" type="s:string"/>
                            <s:element minOccurs="0" name="ClaimLevelREM" type="s:string"/>
                            <s:element minOccurs="0" name="DueDate" type="s:date"/>
                            <s:element minOccurs="0" name="DueTime" type="s:time"/>
                            <s:element minOccurs="0" name="ExtendFlag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PauseFromDate" type="s:date"/>
                            <s:element minOccurs="0" name="PauseToDate" type="s:date"/>
                            <s:element minOccurs="0" name="LoanDateTime">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="25"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="OutstandingDays" type="s:long"/>
                            <s:element minOccurs="0" name="ClaimDispute" type="s:string"/>
                            <s:element minOccurs="0" name="ClaimedReturnFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="ClaimDisputeFlag" type="s:string"/>
                            <s:element minOccurs="0" name="ReplacementCostCharged" type="s:boolean"/>
                            <s:element minOccurs="0" name="ShortLoan" type="s:boolean"/>
                            <s:element minOccurs="0" name="NumberReservesOnTitle" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfClaims">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Claims" nillable="true" type="s0:Claims"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Claims">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="ClaimLevel" type="s:string"/>
                            <s:element minOccurs="0" name="DateClaimProcessed" type="s:date"/>
                            <s:element minOccurs="0" name="SentBy" type="s:string"/>
                            <s:element minOccurs="0" name="BorrowerId" type="s:decimal"/>
                            <s:element minOccurs="0" name="Barcode" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfPermanentLoans">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="PermanentLoans" nillable="true" type="s0:PermanentLoans"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="PermanentLoans">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Barcode" type="s0:s_StockItems"/>
                            <s:element minOccurs="0" name="DateIssued" type="s0:Horolog"/>
                            <s:element minOccurs="0" name="DateShow" type="s:date"/>
                            <s:element minOccurs="0" name="TimeShow" type="s:string"/>
                            <s:element minOccurs="0" name="MemberDetails">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfStatusCodes">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="StatusCodes" nillable="true" type="s0:StatusCodes"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="StatusCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="DateTime">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="IntDateSet">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="Code" type="s0:MemberStatusCodes"/>
                            <s:element minOccurs="0" name="LiberoSystemUser">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="MemberStatusCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4096"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DisallowIssues">
                                <s:simpleType>
                                    <s:restriction base="s:long">
                                        <s:maxInclusive value="1"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DisallowRenewals">
                                <s:simpleType>
                                    <s:restriction base="s:long">
                                        <s:maxInclusive value="1"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DisallowMemberMaintenance">
                                <s:simpleType>
                                    <s:restriction base="s:long">
                                        <s:maxInclusive value="1"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DisallowRenewalsWebOPAC">
                                <s:simpleType>
                                    <s:restriction base="s:long">
                                        <s:maxInclusive value="1"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DisallowMemberSelfManagement">
                                <s:simpleType>
                                    <s:restriction base="s:long">
                                        <s:maxInclusive value="1"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AllowReadOnlyAccess" type="s:boolean"/>
                            <s:element minOccurs="0" name="AllowILL" type="s:boolean"/>
                            <s:element minOccurs="0" name="AllowILLRenewals" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfReserves">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Reserves" nillable="true" type="s0:Reserves"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Reserves">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Barcode" type="s0:s_StockItems"/>
                            <s:element minOccurs="0" name="DateReservedString" type="s:string"/>
                            <s:element minOccurs="0" name="DateCancelNotice" type="s:date"/>
                            <s:element minOccurs="0" name="DateReserved" type="s:date"/>
                            <s:element minOccurs="0" name="DateNoticePrint" type="s:date"/>
                            <s:element minOccurs="0" name="HoldFlag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="8"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="InternalDateReserved" type="s:decimal"/>
                            <s:element minOccurs="0" name="ItemRequest" type="s0:s_StockItems"/>
                            <s:element name="ReservesID" type="s:string"/>
                            <s:element minOccurs="0" name="ReservesNumber" type="s0:Biblio_Reserves"/>
                            <s:element minOccurs="0" name="RSN" type="s0:s_Biblio"/>
                            <s:element minOccurs="0" name="PlacementType" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="Biblio_Reserves">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Barcode" type="s0:s_StockItems"/>
                            <s:element minOccurs="0" name="MemberCode" type="s0:MemberCode"/>
                            <s:element minOccurs="0" name="PickUpBranch" type="s0:Branch"/>
                            <s:element minOccurs="0" name="ReservedAtBranch">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="32767"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="TrapBranches">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="32767"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Notes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="32767"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NotificationMethod" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="ReserveDate" type="s:date"/>
                            <s:element minOccurs="0" name="ReserveExpiryDate" type="s:date"/>
                            <s:element name="ReserveNumber" type="s:long"/>
                            <s:element minOccurs="0" name="TimeReservePlaced" type="s:time"/>
                            <s:element minOccurs="0" name="PlacementType" type="s:boolean"/>
                            <s:element minOccurs="0" name="WebOpacFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="VolumeTitle" type="s0:LongString"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfFinanceItemString">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="FinanceItem" nillable="true" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfAddressSchedule">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="AddressSchedule" nillable="true" type="s0:AddressSchedule"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="AddressSchedule">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="ScheduleDate" type="s:date"/>
                            <s:element minOccurs="0" name="NotifyBy" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="AddressToPrintFlag" type="s:string"/>
                            <s:element minOccurs="0" name="MemberCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NotifyByDescription" type="s:string"/>
                            <s:element minOccurs="0" name="AddressToPrint">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="11"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfAddressTemplate">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="AddressTemplate" nillable="true" type="s0:AddressTemplate"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="AddressTemplate">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="SequenceNumber" type="s:decimal"/>
                            <s:element minOccurs="0" name="AddressToPrintFlag" type="s:string"/>
                            <s:element minOccurs="0" name="NotifyBy" type="s0:NotificationMethods"/>
                            <s:element minOccurs="0" name="ScheduleDayMonth" type="s:string"/>
                            <s:element minOccurs="0" name="MemberCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NotifyByDescription" type="s:string"/>
                            <s:element minOccurs="0" name="ScheduleDay" type="s:decimal"/>
                            <s:element minOccurs="0" name="ScheduleMonth" type="s:decimal"/>
                            <s:element minOccurs="0" name="AddressToPrint">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="11"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDIProfile">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDIProfile" nillable="true" type="s0:SDIProfile"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDIProfile">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="SDISubject" type="s0:ArrayOfSDISubject"/>
                            <s:element minOccurs="0" name="SDIAuthor" type="s0:ArrayOfSDIAuthor"/>
                            <s:element minOccurs="0" name="SDICorporateAuthor" type="s0:ArrayOfSDICorporateAuthor"/>
                            <s:element minOccurs="0" name="SDISeries" type="s0:ArrayOfSDISeries"/>
                            <s:element minOccurs="0" name="SDITitle" type="s0:ArrayOfSDITitle"/>
                            <s:element minOccurs="0" name="SDIVolumeTitle" type="s0:ArrayOfSDIVolumeTitle"/>
                            <s:element minOccurs="0" name="SDIgmd" type="s0:ArrayOfSDIgmd"/>
                            <s:element minOccurs="0" name="SDICollection" type="s0:ArrayOfSDICollection"/>
                            <s:element minOccurs="0" name="SDICollectionGroup" type="s0:ArrayOfSDICollectionGroup"/>
                            <s:element minOccurs="0" name="SDIMaterialGroup" type="s0:ArrayOfSDIMaterialGroup"/>
                            <s:element minOccurs="0" name="UserId" type="s0:LibraryAccess"/>
                            <s:element name="ProfileCode" type="s:string"/>
                            <s:element minOccurs="0" name="ProfileDescription" type="s:string"/>
                            <s:element minOccurs="0" name="FromPublicationYear" type="s:decimal"/>
                            <s:element minOccurs="0" name="ToPublicationYear" type="s:decimal"/>
                            <s:element minOccurs="0" name="CreationTime" type="s:dateTime"/>
                            <s:element minOccurs="0" name="LastEditTime" type="s:dateTime"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDISubject">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDISubject" nillable="true" type="s0:SDISubject"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDISubject">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Subject">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDIAuthor">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDIAuthor" nillable="true" type="s0:SDIAuthor"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDIAuthor">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Author">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDICorporateAuthor">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDICorporateAuthor" nillable="true" type="s0:SDICorporateAuthor"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDICorporateAuthor">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="CorporateAuthor">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDISeries">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDISeries" nillable="true" type="s0:SDISeries"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDISeries">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Series">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDITitle">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDITitle" nillable="true" type="s0:SDITitle"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDITitle">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Title">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDIVolumeTitle">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDIVolumeTitle" nillable="true" type="s0:SDIVolumeTitle"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDIVolumeTitle">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="VolumeTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDIgmd">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDIgmd" nillable="true" type="s0:SDIgmd"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDIgmd">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="GMD">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDICollection">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDICollection" nillable="true" type="s0:SDICollection"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDICollection">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Collection" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDICollectionGroup">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDICollectionGroup" nillable="true" type="s0:SDICollectionGroup"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDICollectionGroup">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="CollectionGroup">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="CollectionGroupDescription">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDIMaterialGroup">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDIMaterialGroup" nillable="true" type="s0:SDIMaterialGroup"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDIMaterialGroup">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="MaterialGroup">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MaterialGroupDescription">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="LibraryAccess">
                <s:complexContent>
                    <s:extension base="s0:Access">
                        <s:sequence>
                            <s:element minOccurs="0" name="ResponsibleOfficer" type="s:string"/>
                            <s:element minOccurs="0" name="DatabaseCode" type="s:string"/>
                            <s:element minOccurs="0" name="BranchCode" type="s:string"/>
                            <s:element minOccurs="0" name="AccessLocked" type="s:boolean"/>
                            <s:element minOccurs="0" name="GivenName" type="s:string"/>
                            <s:element minOccurs="0" name="Surname" type="s:string"/>
                            <s:element minOccurs="0" name="Department" type="s:string"/>
                            <s:element minOccurs="0" name="Office" type="s:string"/>
                            <s:element minOccurs="0" name="Address1" type="s:string"/>
                            <s:element minOccurs="0" name="Address2" type="s:string"/>
                            <s:element minOccurs="0" name="Postcode" type="s:string"/>
                            <s:element minOccurs="0" name="City" type="s:string"/>
                            <s:element minOccurs="0" name="Phone" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="Fax" type="s0:PhoneNumber"/>
                            <s:element minOccurs="0" name="Note" type="s:string"/>
                            <s:element minOccurs="0" name="Settings" type="s0:ArrayOfUserSettings"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="Access">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="AccessId" type="s:string"/>
                            <s:element minOccurs="0" name="AccessName">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="LastDatePwsChanged" type="s:string"/>
                            <s:element minOccurs="0" name="NoOfLogins" type="s:decimal"/>
                            <s:element minOccurs="0" name="CntPwdNotChnaged" type="s:decimal"/>
                            <s:element minOccurs="0" name="emailAddress" type="s:string"/>
                            <s:element minOccurs="0" name="NetworkLogin" type="s:string"/>
                            <s:element minOccurs="0" name="AdministratorIndicator" type="s:boolean"/>
                            <s:element minOccurs="0" name="xsysUserNumber" type="s:string"/>
                            <s:element minOccurs="0" name="xsysUserName" type="s:string"/>
                            <s:element minOccurs="0" name="CacheUserName" type="s:string"/>
                            <s:element minOccurs="0" name="CachePassword" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfUserSettings">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="UserSettings" nillable="true" type="s0:UserSettings"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="UserSettings">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="PageReference" type="s:string"/>
                            <s:element minOccurs="0" name="PageSize" type="s:long"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSDIMemberTemplate">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SDIMemberTemplate" nillable="true" type="s0:SDIMemberTemplate"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SDIMemberTemplate">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="TemplateCode" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSystemMessages">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SystemMessages" nillable="true" type="s0:SystemMessages"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SystemMessages">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Ref" type="s0:Horolog"/>
                            <s:element minOccurs="0" name="DateTime" type="s:string"/>
                            <s:element minOccurs="0" name="Detail" type="s0:StringWithControlCharacters"/>
                            <s:element minOccurs="0" name="DetailPlain" type="s:string"/>
                            <s:element minOccurs="0" name="TheDate" type="s:date"/>
                            <s:element minOccurs="0" name="TheTime" type="s:time"/>
                            <s:element minOccurs="0" name="UserNumber" type="s:string"/>
                            <s:element minOccurs="0" name="SecurityId" type="s0:Access"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:simpleType name="StringWithControlCharacters">
                <s:restriction base="s:base64Binary"/>
            </s:simpleType>
            <s:complexType name="MemberAccess">
                <s:complexContent>
                    <s:extension base="s0:Access"/>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfRouting">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Routing" nillable="true" type="s0:Routing"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Routing">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="RefCode" type="s:string"/>
                            <s:element minOccurs="0" name="RSN" type="s:decimal"/>
                            <s:element minOccurs="0" name="SubscriptionNumber" type="s:decimal"/>
                            <s:element minOccurs="0" name="CopyNumber" type="s:decimal"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfReadingRoom">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ReadingRoom" nillable="true" type="s0:ReadingRoom"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ReadingRoom">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Item" type="s:string"/>
                            <s:element minOccurs="0" name="DayTime" type="s0:Horolog"/>
                            <s:element minOccurs="0" name="Barcode" type="s0:s_StockItems"/>
                            <s:element minOccurs="0" name="Pickup" type="s0:PickupPoints"/>
                            <s:element minOccurs="0" name="Notes" type="s:string"/>
                            <s:element minOccurs="0" name="DateShow" type="s:date"/>
                            <s:element minOccurs="0" name="TimeShow" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfMemberAdditionalCodes">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="MemberAdditionalCodes" nillable="true" type="s0:MemberAdditionalCodes"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="MemberAdditionalCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="NewCode" type="s0:AdditionalCodes"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="AdditionalCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="NewCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MemberId" type="s0:s_Member"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfStackRequests">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="StackRequests" nillable="true" type="s0:StackRequests"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="AgeGroupCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Category" type="s0:Categories"/>
                            <s:element minOccurs="0" name="FromAge">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ToAge">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MaxAge" type="s:long"/>
                            <s:element minOccurs="0" name="NextCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="Categories">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4096"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="WebLinks" type="s0:ArrayOfCategoriesWebLink"/>
                            <s:element minOccurs="0" name="AgeLimit" type="s:decimal"/>
                            <s:element minOccurs="0" name="AfterAgeLimitReachedCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="IncompleteKitCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MandDOBAgeGroup" type="s:boolean"/>
                            <s:element minOccurs="0" name="MandatoryDOB" type="s:boolean"/>
                            <s:element minOccurs="0" name="MandatoryAgeGroup" type="s:boolean"/>
                            <s:element minOccurs="0" name="EnableBorrowHistory" type="s:boolean"/>
                            <s:element minOccurs="0" name="EnableReserveHistory" type="s:boolean"/>
                            <s:element minOccurs="0" name="EnableInternetUsage" type="s:boolean"/>
                            <s:element minOccurs="0" name="StatisticsCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="256"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SuppressDisputeWarnings" type="s:boolean"/>
                            <s:element minOccurs="0" name="ReRegistrationDays" type="s:decimal"/>
                            <s:element minOccurs="0" name="ChargeMemberReRegistration" type="s:boolean"/>
                            <s:element minOccurs="0" name="ReRegReminderDays" type="s:decimal"/>
                            <s:element minOccurs="0" name="MaxNumberLoans" type="s:decimal"/>
                            <s:element minOccurs="0" name="MaxPermanentLoans" type="s:decimal"/>
                            <s:element minOccurs="0" name="GMDBorrowPermitted">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3641144"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GMDBorrowNotPermitted">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3641144"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="CollectionBorrowPermitted">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3641144"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="CollectionBorrowNotPermitted">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3641144"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="IntNORenewalsPermitted" type="s:decimal"/>
                            <s:element minOccurs="0" name="CirculationRenewNotPermitted" type="s:boolean"/>
                            <s:element minOccurs="0" name="WebOPACRenewNotPermitted" type="s:boolean"/>
                            <s:element minOccurs="0" name="IntAutoRenewalsPermitted" type="s:boolean"/>
                            <s:element minOccurs="0" name="MaxChargesOutstanding" type="s:string"/>
                            <s:element minOccurs="0" name="ChangeStatusWhenMax" type="s:string"/>
                            <s:element minOccurs="0" name="ChangeStatusUnderMax" type="s:string"/>
                            <s:element minOccurs="0" name="ItemsOverdueLimit" type="s:string"/>
                            <s:element minOccurs="0" name="ChangeStatusWhenOverdue" type="s:string"/>
                            <s:element minOccurs="0" name="ChangeStatusUnderOverdue" type="s:string"/>
                            <s:element minOccurs="0" name="DisplayMemberDetailsOnLoan" type="s:boolean"/>
                            <s:element minOccurs="0" name="DisplayMemberDetailsOnPermanent" type="s:boolean"/>
                            <s:element minOccurs="0" name="MaxWebFineCreation" type="s:decimal"/>
                            <s:element minOccurs="0" name="MaxILLRequests" type="s:string"/>
                            <s:element minOccurs="0" name="ILLRequestAnnualQuota" type="s:string"/>
                            <s:element minOccurs="0" name="ChargeNoILLIncomingFee" type="s:boolean"/>
                            <s:element minOccurs="0" name="ChargeNoILLOutgoingFee" type="s:boolean"/>
                            <s:element minOccurs="0" name="MemberFee" type="s:decimal"/>
                            <s:element minOccurs="0" name="ChargeCodeMemberFee" type="s0:TransactionTypes"/>
                            <s:element minOccurs="0" name="ResAvailableFeeMember" type="s:decimal"/>
                            <s:element minOccurs="0" name="ResAvailableFeeMemberChargeCode" type="s0:TransactionTypes"/>
                            <s:element minOccurs="0" name="BorrowFee" type="s:decimal"/>
                            <s:element minOccurs="0" name="ChargeFeeOnReRegistration" type="s:boolean"/>
                            <s:element minOccurs="0" name="BorrowFeeChargeCode" type="s0:TransactionTypes"/>
                            <s:element minOccurs="0" name="ReserveAtBranches">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3641144"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ChargeCashDesignatedMaterials" type="s:boolean"/>
                            <s:element minOccurs="0" name="MaxNoPermittedLoans" type="s:decimal"/>
                            <s:element minOccurs="0" name="MaxReserves" type="s:decimal"/>
                            <s:element minOccurs="0" name="MaxOnShelfReserves" type="s:decimal"/>
                            <s:element minOccurs="0" name="MaxReturnDate" type="s:date"/>
                            <s:element minOccurs="0" name="CollectCodeNotPermitReserve">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3641144"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="GMDCodeNotPermitReserve">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3641144"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NoticeProcessingFee" type="s:decimal"/>
                            <s:element minOccurs="0" name="NoticeProcessingFeeChargeCode" type="s0:TransactionTypes"/>
                            <s:element minOccurs="0" name="NoticeProcessingFeeEmail" type="s:decimal"/>
                            <s:element minOccurs="0" name="NoticeProcessingFeeEmailChargeCode" type="s0:TransactionTypes"/>
                            <s:element minOccurs="0" name="NoticeProcessingFeeSMS" type="s:decimal"/>
                            <s:element minOccurs="0" name="NoticeProcessingFeeSMSChargeCode" type="s0:TransactionTypes"/>
                            <s:element minOccurs="0" name="OneTimeFees" type="s:boolean"/>
                            <s:element minOccurs="0" name="OpacPrintCharge" type="s:decimal"/>
                            <s:element minOccurs="0" name="OpacPrintChargeCode">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SpecialMemberFee" type="s:decimal"/>
                            <s:element minOccurs="0" name="SpecialMemberGMD" type="s:string"/>
                            <s:element minOccurs="0" name="WaitDaysForRenewal" type="s:decimal"/>
                            <s:element minOccurs="0" name="WaitDaysForSubsqRenewal" type="s:decimal"/>
                            <s:element minOccurs="0" name="DefaultFemaleTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DefaultMaleTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DefaultOrganisationTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ReserveCost" type="s:decimal"/>
                            <s:element minOccurs="0" name="WebOPACReserveExpiryDays" type="s:decimal"/>
                            <s:element minOccurs="0" name="supClaimDispCnt" type="s:boolean"/>
                            <s:element minOccurs="0" name="supClaimDispItm" type="s:boolean"/>
                            <s:element minOccurs="0" name="LimitDueDateToMemExp" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfCategoriesWebLink">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="CategoriesWebLink" nillable="true" type="s0:CategoriesWebLink"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="CategoriesWebLink">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="WebPage">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="500"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="LanguageCodes_Parameters">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="LocalityCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:element name="GetMemberOutstanding">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetMemberOutstandingResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetMemberOutstandingResult" type="s0:ItemCharge"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="ItemCharge">
                <s:sequence>
                    <s:element minOccurs="0" name="RefCode" type="s:string"/>
                    <s:element minOccurs="0" name="Date" type="s:date"/>
                    <s:element minOccurs="0" name="Time" type="s:time"/>
                    <s:element minOccurs="0" name="TransType" type="s:string"/>
                    <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    <s:element minOccurs="0" name="Title" type="s:string"/>
                    <s:element minOccurs="0" name="DaysOverdue" type="s:long"/>
                    <s:element minOccurs="0" name="AmountOutstanding" type="s:decimal"/>
                    <s:element minOccurs="0" name="Paid" type="s:decimal"/>
                    <s:element minOccurs="0" name="LastPaid" type="s:date"/>
                </s:sequence>
            </s:complexType>
            <s:element name="GetMemberStatement">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                        <s:element minOccurs="0" name="Group" type="s:string"/>
                        <s:element minOccurs="0" name="Count" type="s:boolean"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetMemberStatementResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetMemberStatementResult" type="s0:GetMemberStatementResult"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="GetMemberStatementResult">
                <s:sequence>
                    <s:element name="Status" type="s:long"/>
                    <s:element name="Message" type="s:string"/>
                    <s:element minOccurs="0" name="Loans" type="s0:Member_Loans"/>
                    <s:element minOccurs="0" name="Reserves" type="s0:Member_Reserves"/>
                    <s:element minOccurs="0" name="Holds" type="s0:Member_Holds"/>
                    <s:element minOccurs="0" name="Charges" type="s0:Member_Charges"/>
                    <s:element minOccurs="0" name="LoansCount" type="s:decimal"/>
                    <s:element minOccurs="0" name="ReservesCount" type="s:decimal"/>
                    <s:element minOccurs="0" name="HoldsCount" type="s:decimal"/>
                    <s:element minOccurs="0" name="ChargesCount" type="s:decimal"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Member_Loans">
                <s:sequence>
                    <s:element minOccurs="0" ref="s0:ItemLoanQuery"/>
                    <s:element minOccurs="0" ref="s0:RenewOk"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ItemLoanQuery" type="s0:ItemLoanQuery"/>
            <s:element name="RenewOk" type="s:boolean"/>
            <s:complexType name="ItemLoanQuery">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" ref="s0:ItemLoan"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ItemLoan" nillable="true" type="s0:ItemLoan"/>
            <s:complexType name="ArrayOfItemLoan">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemLoan" nillable="true" type="s0:ItemLoan"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ItemLoan">
                <s:sequence>
                    <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    <s:element minOccurs="0" name="Author" type="s:string"/>
                    <s:element minOccurs="0" name="Title" type="s:string"/>
                    <s:element minOccurs="0" name="DueDate" type="s:date"/>
                    <s:element minOccurs="0" name="Overdue" type="s:string"/>
                    <s:element minOccurs="0" name="IssueDate" type="s:date"/>
                    <s:element minOccurs="0" name="RSN" type="s:long"/>
                    <s:element minOccurs="0" name="RID" type="s:long"/>
                    <s:element minOccurs="0" name="RenewalCount" type="s:long"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Member_Reserves">
                <s:sequence>
                    <s:element minOccurs="0" ref="s0:ItemReserveQuery"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ItemReserveQuery" type="s0:ItemReserveQuery"/>
            <s:complexType name="ItemReserveQuery">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" ref="s0:ItemReserve"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ItemReserve" nillable="true" type="s0:ItemReserve"/>
            <s:complexType name="ArrayOfItemReserve">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemReserve" nillable="true" type="s0:ItemReserve"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ItemReserve">
                <s:sequence>
                    <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    <s:element minOccurs="0" name="Author" type="s:string"/>
                    <s:element minOccurs="0" name="Title" type="s:string"/>
                    <s:element minOccurs="0" name="QueueNumber" type="s:long"/>
                    <s:element minOccurs="0" name="ReservedDate" type="s:date"/>
                    <s:element minOccurs="0" name="Branch" type="s:string"/>
                    <s:element minOccurs="0" name="PickupBranch" type="s:string"/>
                    <s:element minOccurs="0" name="RSN" type="s:long"/>
                    <s:element minOccurs="0" name="RID" type="s:long"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Member_Holds">
                <s:sequence>
                    <s:element minOccurs="0" ref="s0:ItemHoldQuery"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ItemHoldQuery" type="s0:ItemHoldQuery"/>
            <s:complexType name="ItemHoldQuery">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" ref="s0:ItemHold"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ItemHold" nillable="true" type="s0:ItemHold"/>
            <s:complexType name="ArrayOfItemHold">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemHold" nillable="true" type="s0:ItemHold"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ItemHold">
                <s:sequence>
                    <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    <s:element minOccurs="0" name="Author" type="s:string"/>
                    <s:element minOccurs="0" name="Title" type="s:string"/>
                    <s:element minOccurs="0" name="DateHold" type="s:date"/>
                    <s:element minOccurs="0" name="TimeHold" type="s:time"/>
                    <s:element minOccurs="0" name="PickupBranch" type="s:string"/>
                    <s:element minOccurs="0" name="CollectBy" type="s:date"/>
                    <s:element minOccurs="0" name="CallNumber" type="s:string"/>
                    <s:element minOccurs="0" name="Availability" type="s:string"/>
                    <s:element minOccurs="0" name="RSN" type="s:long"/>
                    <s:element minOccurs="0" name="RID" type="s:long"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Member_Charges">
                <s:sequence>
                    <s:element minOccurs="0" ref="s0:ItemChargeQuery"/>
                    <s:element minOccurs="0" ref="s0:CurrencyType"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ItemChargeQuery" type="s0:ItemChargeQuery"/>
            <s:element name="CurrencyType" type="s:string"/>
            <s:complexType name="ItemChargeQuery">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" ref="s0:ItemCharge"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ItemCharge" nillable="true" type="s0:ItemCharge"/>
            <s:complexType name="ArrayOfItemCharge">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="ItemCharge" nillable="true" type="s0:ItemCharge"/>
                </s:sequence>
            </s:complexType>
            <s:element name="GetMemberTransactions">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetMemberTransactionsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetMemberTransactionsResult" type="s0:LBmem_Finance_soapMemberTransactions"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="LBmem_Finance_soapMemberTransactions">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="soapMemberTransactions">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="RefCode" type="s:string"/>
                                <s:element minOccurs="0" name="Title" type="s:string"/>
                                <s:element minOccurs="0" name="TransactionType" type="s:string"/>
                                <s:element minOccurs="0" name="AmountCharged" type="s:decimal"/>
                                <s:element minOccurs="0" name="TaxAmount" type="s:decimal"/>
                                <s:element minOccurs="0" name="AmountOutstanding" type="s:decimal"/>
                                <s:element minOccurs="0" name="AmountPaid" type="s:decimal"/>
                                <s:element minOccurs="0" name="AmountPerDay" type="s:decimal"/>
                                <s:element minOccurs="0" name="AmountWaived" type="s:decimal"/>
                                <s:element minOccurs="0" name="DaysOverdue" type="s:decimal"/>
                                <s:element minOccurs="0" name="ItemReference" type="s:string"/>
                                <s:element minOccurs="0" name="OutstandingDays" type="s:long"/>
                                <s:element minOccurs="0" name="TransDateRef" type="s:date"/>
                                <s:element minOccurs="0" name="TransTimeRef" type="s:time"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="GetSerialSubscription">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:string"/>
                        <s:element minOccurs="0" name="SubNo" type="s:long"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetSerialSubscriptionResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetSerialSubscriptionResult" type="s0:Subscription"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="Subscription">
                <s:sequence>
                    <s:element minOccurs="0" name="BindEvery" type="s:string"/>
                    <s:element minOccurs="0" name="BindGroup">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="255"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="CopiesRouted" type="s:decimal"/>
                    <s:element minOccurs="0" name="CheckinAlert" type="s:boolean"/>
                    <s:element minOccurs="0" name="SubExpiryDate" type="s:date"/>
                    <s:element minOccurs="0" name="DaysBeforeExpiry" type="s:decimal"/>
                    <s:element minOccurs="0" name="DaysXfreq">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="10"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="FirstIssueDate" type="s:date"/>
                    <s:element minOccurs="0" name="Description" type="s:string"/>
                    <s:element minOccurs="0" name="InitialIssueNumber">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="30"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="DisplayMonthYear">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="3"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="DisplayYearOnly">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="3"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="InitialVolumeNumber">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="30"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="DistributionNotes">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="255"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="InternalSubscriptionNo">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="30"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="ExceptionCode">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="16"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="IssuePerVolume" type="s:decimal"/>
                    <s:element minOccurs="0" name="Initiator">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="20"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="LevelCode">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="5"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="SubscriptionNotes">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="32767"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="OrderNumber">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="30"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="PrepaidIndicator">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="5"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="RSN" type="s:decimal"/>
                    <s:element minOccurs="0" name="SubBaseInventoryNo">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="30"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="SubscriptionNumber">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="30"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="Stats3">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="12"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="SubStatusCodeDescription">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="30"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="VolumeSequentialInd">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="3"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="Stats4">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="12"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="VolumeSequenceInd">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="3"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="SubscriptionStatus">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="12"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="TextIssue">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="20"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="TextVolume">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="20"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="intAlertLastIssue" type="s:boolean"/>
                    <s:element minOccurs="0" name="AlertLastIssue" type="s:boolean"/>
                    <s:element minOccurs="0" name="intAlert" type="s:boolean"/>
                    <s:element minOccurs="0" name="Alert" type="s:boolean"/>
                    <s:element minOccurs="0" name="intNotAllocatePriceOnCheckin" type="s:boolean"/>
                    <s:element minOccurs="0" name="NotAllocatePriceOnCheckin" type="s:boolean"/>
                    <s:element minOccurs="0" name="Acronym" type="s:string"/>
                    <s:element minOccurs="0" name="Title" type="s:string"/>
                    <s:element minOccurs="0" name="JournalTitle" type="s:string"/>
                    <s:element minOccurs="0" name="CallNumber" type="s:string"/>
                    <s:element minOccurs="0" name="Frequency" type="s:string"/>
                    <s:element minOccurs="0" name="NumberOfSubs" type="s:string"/>
                    <s:element minOccurs="0" name="NoOfCopiesRouted" type="s:string"/>
                    <s:element minOccurs="0" name="AcronymInventoryNo" type="s:string"/>
                    <s:element minOccurs="0" name="DisplayDDYOMY" type="s:string"/>
                    <s:element minOccurs="0" name="GeneralNotes" type="s:string"/>
                    <s:element minOccurs="0" name="CancelMsg" type="s:string"/>
                    <s:element minOccurs="0" name="CancellationNote" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:element name="GetTitleDetails">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:long"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTitleDetailsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="GetTitleDetailsResult" type="s0:Biblio"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="Biblio">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Illustrators" type="s0:ArrayOfIllustrators"/>
                            <s:element minOccurs="0" name="AlternateISBNs" type="s0:ArrayOfAlternateISBNs"/>
                            <s:element minOccurs="0" name="AlternateISMNs" type="s0:ArrayOfAlternateISMNs"/>
                            <s:element minOccurs="0" name="AlternateISSNs" type="s0:ArrayOfAlternateISSNs"/>
                            <s:element minOccurs="0" name="URLs" type="s0:ArrayOfURL"/>
                            <s:element minOccurs="0" name="Author" type="s0:ArrayOfAuthors"/>
                            <s:element minOccurs="0" name="CorporateAuthor" type="s0:ArrayOfCorporateAuthors"/>
                            <s:element minOccurs="0" name="Classification" type="s0:ArrayOfClassifications"/>
                            <s:element minOccurs="0" name="SerialNotes" type="s0:ArrayOfSerialNotes"/>
                            <s:element minOccurs="0" name="StockItems" type="s0:ArrayOfStockItemss_"/>
                            <s:element minOccurs="0" name="Notes" type="s0:ArrayOfBibliographic_Notes"/>
                            <s:element minOccurs="0" name="EditHistory" type="s0:ArrayOfEditHistory"/>
                            <s:element minOccurs="0" name="Comments" type="s0:ArrayOfBiblioUserComments"/>
                            <s:element minOccurs="0" name="ReserveNumber" type="s0:ArrayOfBiblio_Reserves"/>
                            <s:element minOccurs="0" name="SeriesKey" type="s0:ArrayOfSeriesKeys"/>
                            <s:element minOccurs="0" name="SubjectKey" type="s0:ArrayOfSubjectKeys"/>
                            <s:element minOccurs="0" name="UDN" type="s0:ArrayOfUDNs"/>
                            <s:element minOccurs="0" name="Images" type="s0:ArrayOfImages"/>
                            <s:element minOccurs="0" name="Marc21" type="s0:ArrayOfMarc21"/>
                            <s:element minOccurs="0" name="MAB" type="s0:ArrayOfMAB"/>
                            <s:element minOccurs="0" name="UKMarc" type="s0:ArrayOfUKMarc"/>
                            <s:element minOccurs="0" name="UniMarc" type="s0:ArrayOfUniMarc"/>
                            <s:element minOccurs="0" name="UserDefinedFields" type="s0:ArrayOfUserFields"/>
                            <s:element minOccurs="0" name="SupplierInvoices" type="s0:ArrayOfSupplierInvoices"/>
                            <s:element minOccurs="0" name="Subscription" type="s0:ArrayOfSubscriptions"/>
                            <s:element minOccurs="0" name="SerialYear" type="s0:ArrayOfSerialYears"/>
                            <s:element minOccurs="0" name="BindingGroups" type="s0:ArrayOfBindingGroups"/>
                            <s:element minOccurs="0" name="CreatedDate" type="s:string"/>
                            <s:element minOccurs="0" name="RawCreatedDate" type="s:date"/>
                            <s:element minOccurs="0" name="RawCreatedDateTime" type="s:string"/>
                            <s:element minOccurs="0" name="CreatedByUser" type="s:string"/>
                            <s:element minOccurs="0" name="CreatedAtBranch" type="s0:Branch"/>
                            <s:element minOccurs="0" name="EditDate" type="s:date"/>
                            <s:element minOccurs="0" name="EditByUser" type="s:string"/>
                            <s:element minOccurs="0" name="LastSavedDate" type="s:date"/>
                            <s:element minOccurs="0" name="CallNumber">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="160"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Collation">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="NumberOfReservations" type="s:decimal"/>
                            <s:element minOccurs="0" name="Edition">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="60"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ExternalDocument">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="FilingIndicator" type="s:decimal"/>
                            <s:element minOccurs="0" name="GMD" type="s0:s_GMDCodes"/>
                            <s:element minOccurs="0" name="ISBN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MainAlternateISBN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MainAlternateISSN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MainAlternateISMN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ISMN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ISSN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Language" type="s0:BibliographicLanguageCodes"/>
                            <s:element minOccurs="0" name="LanguagesAll">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="60"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="LocalContents">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="OPACDisplayFlag" type="s:boolean"/>
                            <s:element minOccurs="0" name="JournalSummary" type="s:boolean"/>
                            <s:element minOccurs="0" name="InternalNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="1000"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Publication">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="PublicationYear">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="RID" type="s:string"/>
                            <s:element name="RSN" type="s:decimal"/>
                            <s:element minOccurs="0" name="Series">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="120"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SubTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Title">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DisplayTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="400"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Volume">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="50"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="URL" type="s0:Url"/>
                            <s:element minOccurs="0" name="MainURL" type="s:string"/>
                            <s:element minOccurs="0" name="LeaderData" type="s:string"/>
                            <s:element minOccurs="0" name="SupplierOrderNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="500"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="WebopacOrderNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="500"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="WebopacGrouping">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="500"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Acronym" type="s:string"/>
                            <s:element minOccurs="0" name="Frequency" type="s:string"/>
                            <s:element minOccurs="0" name="RoutedIndicator" type="s:string"/>
                            <s:element minOccurs="0" name="BindingGroup" type="s:string"/>
                            <s:element minOccurs="0" name="ReviewDays" type="s:long"/>
                            <s:element minOccurs="0" name="ContinueTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ContinuedByTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ExpiryDate" type="s:date"/>
                            <s:element minOccurs="0" name="JournalTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="InventoryNumber" type="s:string"/>
                            <s:element minOccurs="0" name="MainRSN" type="s:decimal"/>
                            <s:element minOccurs="0" name="ClassMain">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MABTitle">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MainAuthor">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AuthorKey" type="s:string"/>
                            <s:element minOccurs="0" name="Illustrator" type="s:string"/>
                            <s:element minOccurs="0" name="NumberOfOrders" type="s:decimal"/>
                            <s:element minOccurs="0" name="NumberOfOrdersText" type="s:string"/>
                            <s:element minOccurs="0" name="TitleFirstCharacter">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="1"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MaxReserve" type="s:decimal"/>
                            <s:element minOccurs="0" name="MABType" type="s:string"/>
                            <s:element minOccurs="0" name="CataloguingLevel" type="s:long"/>
                            <s:element minOccurs="0" name="OAIDate" type="s:long"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfIllustrators">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Illustrators" nillable="true" type="s0:Illustrators"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Illustrators">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Illustrator" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfAlternateISBNs">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="AlternateISBNs" nillable="true" type="s0:AlternateISBNs"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="AlternateISBNs">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="AlternateISBN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfAlternateISMNs">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="AlternateISMNs" nillable="true" type="s0:AlternateISMNs"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="AlternateISMNs">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="AlternateISMN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfAlternateISSNs">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="AlternateISSNs" nillable="true" type="s0:AlternateISSNs"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="AlternateISSNs">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="AlternateISSN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfURL">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="URL" nillable="true" type="s0:URL"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="URL">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="URL">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="30"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfAuthors">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Authors" nillable="true" type="s0:Authors"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Authors">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Author">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="60"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="AuthorDisplayForm">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="60"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MainIndicator" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfCorporateAuthors">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="CorporateAuthors" nillable="true" type="s0:CorporateAuthors"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="CorporateAuthors">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="CorporateAuthor">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="60"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="CorporateAuthorDisplayForm">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="60"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MainIndicator" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfClassifications">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Classifications" nillable="true" type="s0:Classifications"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Classifications">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Classification">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSerialNotes">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SerialNotes" nillable="true" type="s0:SerialNotes"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SerialNotes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="NoteType" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfStockItemss_">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="StockItems" nillable="true" type="s0:s_StockItems"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfBibliographic_Notes">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Bibliographic_Notes" nillable="true" type="s0:Bibliographic_Notes"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Bibliographic_Notes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="AllNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="9999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="LastEditDetails">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="40"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="NoteLine" type="s:decimal"/>
                            <s:element minOccurs="0" name="Note">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfEditHistory">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="EditHistory" nillable="true" type="s0:EditHistory"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="EditHistory">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="NoteLine" type="s:decimal"/>
                            <s:element minOccurs="0" name="Note" type="s:string"/>
                            <s:element minOccurs="0" name="Date" type="s:date"/>
                            <s:element minOccurs="0" name="TimeString" type="s:string"/>
                            <s:element minOccurs="0" name="UserName" type="s:string"/>
                            <s:element minOccurs="0" name="NoteString" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfBiblioUserComments">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="BiblioUserComments" nillable="true" type="s0:BiblioUserComments"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="BiblioUserComments">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="commentNo" type="s:long"/>
                            <s:element minOccurs="0" name="DateTime" type="s0:Horolog"/>
                            <s:element minOccurs="0" name="Date" type="s:date"/>
                            <s:element minOccurs="0" name="BorrowerCode" type="s:string"/>
                            <s:element minOccurs="0" name="Rating" type="s:long"/>
                            <s:element minOccurs="0" name="BorrowerID" type="s0:s_Member"/>
                            <s:element minOccurs="0" name="OpacDisplay" type="s:boolean"/>
                            <s:element minOccurs="0" name="Moderated" type="s:boolean"/>
                            <s:element minOccurs="0" name="ModeratedText" type="s:string"/>
                            <s:element minOccurs="0" name="Comments">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="131072"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfBiblio_Reserves">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Biblio_Reserves" nillable="true" type="s0:Biblio_Reserves"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfSeriesKeys">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SeriesKeys" nillable="true" type="s0:SeriesKeys"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SeriesKeys">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="SeriesKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SeriesKeyDisplayForm">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="60"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubjectKeys">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubjectKeys" nillable="true" type="s0:SubjectKeys"/>
                </s:sequence>
            </s:complexType>
            <s:complexType abstract="true" name="SubjectKeys">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="SubjectKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="SubjectKeyDisplayForm">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="60"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfUDNs">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="UDNs" nillable="true" type="s0:UDNs"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="UDNs">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="UDN">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="9"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="UDN4">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Document" type="s0:ArrayOfMain"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfMain">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Main" nillable="true" type="s0:Main"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Main">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="PatronComments" type="s0:ArrayOfDocUserComment"/>
                            <s:element minOccurs="0" name="MemberId" type="s0:s_Member"/>
                            <s:element minOccurs="0" name="DocumentName">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="255"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="1024"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Comments">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="1024"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Document" type="s:base64Binary"/>
                            <s:element minOccurs="0" name="RSN" type="s0:s_Biblio"/>
                            <s:element minOccurs="0" name="SHA1">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="40"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfDocUserComment">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="DocUserComment" nillable="true" type="s0:DocUserComment"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="DocUserComment">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Member" type="s0:s_Member"/>
                            <s:element minOccurs="0" name="Biblio" type="s0:s_Biblio"/>
                            <s:element name="commentNo" type="s:long"/>
                            <s:element minOccurs="0" name="LatestDate" type="s:date"/>
                            <s:element minOccurs="0" name="LatestTime" type="s:time"/>
                            <s:element minOccurs="0" name="Rating" type="s:long"/>
                            <s:element minOccurs="0" name="OpacDisplay" type="s:boolean"/>
                            <s:element minOccurs="0" name="Moderated" type="s:boolean"/>
                            <s:element minOccurs="0" name="ModeratedText" type="s:string"/>
                            <s:element minOccurs="0" name="Comments">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="32767"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfImages">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Images" nillable="true" type="s0:Images"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Images">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="ImageFile" type="s:string"/>
                            <s:element minOccurs="0" name="Location" type="s:string"/>
                            <s:element minOccurs="0" name="Note">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="999"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfMarc21">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Marc21" nillable="true" type="s0:Marc21"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Marc21">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="IndicatorKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="TagKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MarcData" type="s0:StringWithControlCharacters"/>
                            <s:element minOccurs="0" name="MarcDataPlain" type="s:string"/>
                            <s:element minOccurs="0" name="IndicatorString">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="Sequence" type="s:decimal"/>
                            <s:element name="Subfield">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="2"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Tag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Marc21HeaderDate" type="s:string"/>
                            <s:element minOccurs="0" name="Status">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Type">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfMAB">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="MAB" nillable="true" type="s0:MAB"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="MAB">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="TagKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="IndicatorKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MABData">
                                <s:simpleType>
                                    <s:restriction base="s0:StringWithControlCharacters">
                                        <s:maxLength value="15000"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MABDataPlain">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="15000"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="Sequence" type="s:double"/>
                            <s:element name="Subfield">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="2"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Tag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MabHeaderDate" type="s:string"/>
                            <s:element minOccurs="0" name="Source">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Status">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Type">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfUKMarc">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="UKMarc" nillable="true" type="s0:UKMarc"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="UKMarc">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="IndicatorKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="TagKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MarcData">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="15000"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Indicator">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="Sequence" type="s:decimal"/>
                            <s:element name="Subfield">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="2"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Tag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MarcHeaderDate" type="s:string"/>
                            <s:element minOccurs="0" name="Status">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Type">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfUniMarc">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="UniMarc" nillable="true" type="s0:UniMarc"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="UniMarc">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="IndicatorKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="TagKey">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="4"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MarcData">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="15000"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Indicator">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element name="Sequence" type="s:decimal"/>
                            <s:element name="Subfield">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="2"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Tag">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="3"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="MarcHeaderDate" type="s:string"/>
                            <s:element minOccurs="0" name="Status">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Type">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="20"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfUserFields">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="UserFields" nillable="true" type="s0:UserFields"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="UserFields">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="ItemField01" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField02" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField03" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField04" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField05" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField06" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField07" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField08" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField09" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField10" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField11" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField12" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField13" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField14" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField15" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField16" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField17" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField18" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField19" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField20" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField21" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField22" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField23" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField24" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField25" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField26" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField27" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField27Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField28" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField28Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField29" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField29Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField30" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField30Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField31" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField31Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                            <s:element minOccurs="0" name="ItemField32" type="s0:UserDefinedField"/>
                            <s:element minOccurs="0" name="ItemField32Desc" type="s0:UserDefinedFieldCodeAndDescription"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSupplierInvoices">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SupplierInvoices" nillable="true" type="s0:SupplierInvoices"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SupplierInvoices">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="serSubscription" type="s:string"/>
                            <s:element name="SupplierCode" type="s:string"/>
                            <s:element name="InvoiceNumber" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptions">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="Subscriptions" nillable="true" type="s0:Subscriptions"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="Subscriptions">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="SubscriptionOrder" type="s0:ArrayOfSubscriptionOrders"/>
                            <s:element minOccurs="0" name="SubscriptionCallNumber" type="s0:ArrayOfSubscriptionCallNumbers"/>
                            <s:element minOccurs="0" name="SubscriptionRoutedCopies" type="s0:ArrayOfSubscriptionRouted"/>
                            <s:element minOccurs="0" name="SubscriptionBranchCopy" type="s0:ArrayOfSubscriptionBranchCopies"/>
                            <s:element minOccurs="0" name="SubscriptionSupplierInvoice" type="s0:ArrayOfSubscriptionSupplierInvoices"/>
                            <s:element minOccurs="0" name="SubscriptionCostTransaction" type="s0:ArrayOfSubscriptionCostTransactions"/>
                            <s:element minOccurs="0" name="SubscriptionPredictionList" type="s0:ArrayOfSubscriptionPredictionLists"/>
                            <s:element name="serSubscription" type="s:string"/>
                            <s:element minOccurs="0" name="SubscriptionNumber" type="s:decimal"/>
                            <s:element minOccurs="0" name="ExpiryDate" type="s:date"/>
                            <s:element minOccurs="0" name="VolumeSequentialIndicator" type="s:string"/>
                            <s:element minOccurs="0" name="FirstIssueDate" type="s:date"/>
                            <s:element minOccurs="0" name="InitialVolumeNumber" type="s:decimal"/>
                            <s:element minOccurs="0" name="PrepaidIndicator" type="s:string"/>
                            <s:element minOccurs="0" name="LevelCode" type="s:string"/>
                            <s:element minOccurs="0" name="AppendToIssueNumber" type="s:string"/>
                            <s:element minOccurs="0" name="CreatedByBranch" type="s0:Branch"/>
                            <s:element minOccurs="0" name="NumberOfDaysOption" type="s:long"/>
                            <s:element minOccurs="0" name="LastCheckedInBarcode" type="s:string"/>
                            <s:element minOccurs="0" name="OrderLineNumber" type="s:long"/>
                            <s:element minOccurs="0" name="SubscriptionDescription">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="100"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="ItemException" type="s0:ExceptionCodes"/>
                            <s:element minOccurs="0" name="Statistic4" type="s0:ItemStatistics4"/>
                            <s:element minOccurs="0" name="ProcessingNotes">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="32767"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="DaysForFrequencyZ" type="s:long"/>
                            <s:element minOccurs="0" name="DisplayMonthYear" type="s:boolean"/>
                            <s:element minOccurs="0" name="DisplayYearOnly" type="s:boolean"/>
                            <s:element minOccurs="0" name="DisplayDayDate" type="s:boolean"/>
                            <s:element minOccurs="0" name="StatusCode" type="s0:SubscriptionStatusCodes"/>
                            <s:element minOccurs="0" name="LastIssueAlertOnCheckIn" type="s:boolean"/>
                            <s:element minOccurs="0" name="ExpiryAlert" type="s:boolean"/>
                            <s:element minOccurs="0" name="DaysBeforeExpiry" type="s:long"/>
                            <s:element minOccurs="0" name="InventoryNumber" type="s:string"/>
                            <s:element minOccurs="0" name="LastTransactionNumberUsed" type="s:decimal"/>
                            <s:element minOccurs="0" name="ShortNote1" type="s:string"/>
                            <s:element minOccurs="0" name="ShortNote2" type="s:string"/>
                            <s:element minOccurs="0" name="Credit1FundCostCentre" type="s:string"/>
                            <s:element minOccurs="0" name="Credit1Amount" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit1PercentageSplit" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit2FundCostCentre" type="s:string"/>
                            <s:element minOccurs="0" name="Credit2Amount" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit2PercentageSplit" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit3FundCostCentre" type="s:string"/>
                            <s:element minOccurs="0" name="Credit3Amount" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit3PercentageSplit" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit4FundCostCentre" type="s:string"/>
                            <s:element minOccurs="0" name="Credit4Amount" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit4PercentageSplit" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit5FundCostCentre" type="s:string"/>
                            <s:element minOccurs="0" name="Credit5Amount" type="s:decimal"/>
                            <s:element minOccurs="0" name="Credit5PercentageSplit" type="s:decimal"/>
                            <s:element minOccurs="0" name="OrderBranch" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptionOrders">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubscriptionOrders" nillable="true" type="s0:SubscriptionOrders"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SubscriptionOrders">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="OrderCode" type="s:string"/>
                            <s:element minOccurs="0" name="InvoiceNumber" type="s:string"/>
                            <s:element minOccurs="0" name="AmountPaid" type="s:decimal"/>
                            <s:element minOccurs="0" name="DatePaid" type="s:date"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptionCallNumbers">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubscriptionCallNumbers" nillable="true" type="s0:SubscriptionCallNumbers"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SubscriptionCallNumbers">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="CallNumber" type="s:string"/>
                            <s:element minOccurs="0" name="MainIndicator" type="s:string"/>
                            <s:element minOccurs="0" name="DateMainIndicatorSet" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptionRouted">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubscriptionRouted" nillable="true" type="s0:SubscriptionRouted"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SubscriptionRouted">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Volume" type="s:string"/>
                            <s:element name="Issue" type="s:string"/>
                            <s:element minOccurs="0" name="RoutedCopies" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptionBranchCopies">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubscriptionBranchCopies" nillable="true" type="s0:SubscriptionBranchCopies"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SubscriptionBranchCopies">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="CopyNumber" type="s:string"/>
                            <s:element minOccurs="0" name="BranchCode" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptionSupplierInvoices">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubscriptionSupplierInvoices" nillable="true" type="s0:SubscriptionSupplierInvoices"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SubscriptionSupplierInvoices">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="SupplierCode" type="s:string"/>
                            <s:element name="InvoiceNumber" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptionCostTransactions">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubscriptionCostTransactions" nillable="true" type="s0:SubscriptionCostTransactions"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SubscriptionCostTransactions">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="TransactionNumber" type="s:decimal"/>
                            <s:element minOccurs="0" name="CostTransactionCode" type="s:string"/>
                            <s:element minOccurs="0" name="Date" type="s:date"/>
                            <s:element minOccurs="0" name="FundCostCentre" type="s:string"/>
                            <s:element minOccurs="0" name="OrderLine" type="s:string"/>
                            <s:element minOccurs="0" name="DeliveryDocket" type="s:string"/>
                            <s:element minOccurs="0" name="InvoiceNumber" type="s:string"/>
                            <s:element minOccurs="0" name="Description" type="s:string"/>
                            <s:element minOccurs="0" name="BudgetYear" type="s:long"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptionPredictionLists">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubscriptionPredictionLists" nillable="true" type="s0:SubscriptionPredictionLists"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SubscriptionPredictionLists">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="SubscriptionPredictionNote" type="s0:ArrayOfSubscriptionPredictionNotes"/>
                            <s:element name="VolumeNumber" type="s:string"/>
                            <s:element name="IssueNumber" type="s:string"/>
                            <s:element minOccurs="0" name="DateReceived" type="s:date"/>
                            <s:element minOccurs="0" name="Received" type="s:boolean"/>
                            <s:element minOccurs="0" name="DateDue" type="s:date"/>
                            <s:element minOccurs="0" name="DateOverdue" type="s:date"/>
                            <s:element minOccurs="0" name="NumberOfCopies" type="s:long"/>
                            <s:element minOccurs="0" name="FirstClaimDate" type="s:date"/>
                            <s:element minOccurs="0" name="SecondClaimDate" type="s:date"/>
                            <s:element minOccurs="0" name="ThirdClaimDate" type="s:date"/>
                            <s:element minOccurs="0" name="IssueReference" type="s:string"/>
                            <s:element minOccurs="0" name="Status" type="s:string"/>
                            <s:element minOccurs="0" name="Barcode" type="s:string"/>
                            <s:element minOccurs="0" name="BindingNumber" type="s:string"/>
                            <s:element minOccurs="0" name="ClaimCode" type="s:string"/>
                            <s:element minOccurs="0" name="DateIssued" type="s:date"/>
                            <s:element minOccurs="0" name="AverageCost" type="s:decimal"/>
                            <s:element minOccurs="0" name="BoundVolumeIndicator" type="s:boolean"/>
                            <s:element minOccurs="0" name="ShortNote1" type="s:string"/>
                            <s:element minOccurs="0" name="ShortNote2" type="s:string"/>
                            <s:element minOccurs="0" name="IssueNumberOnly" type="s:long"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSubscriptionPredictionNotes">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SubscriptionPredictionNotes" nillable="true" type="s0:SubscriptionPredictionNotes"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SubscriptionPredictionNotes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="NoteLine" type="s:decimal"/>
                            <s:element minOccurs="0" name="Note" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="SubscriptionStatusCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfSerialYears">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="SerialYears" nillable="true" type="s0:SerialYears"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="SerialYears">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="YYYY" type="s:long"/>
                            <s:element minOccurs="0" name="RSN" type="s:decimal"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="ArrayOfBindingGroups">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="BindingGroups" nillable="true" type="s0:BindingGroups"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="BindingGroups">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element name="Group" type="s:string"/>
                            <s:element minOccurs="0" name="SubscriptionNumber" type="s:decimal"/>
                            <s:element minOccurs="0" name="DateSentToBindery" type="s:date"/>
                            <s:element minOccurs="0" name="DateExpectedBack" type="s:date"/>
                            <s:element minOccurs="0" name="DateReceivedBack" type="s:date"/>
                            <s:element minOccurs="0" name="History" type="s:string"/>
                            <s:element minOccurs="0" name="MaterialColourAndName" type="s:string"/>
                            <s:element minOccurs="0" name="VolumePeriodAndNumberOfIssues" type="s:string"/>
                            <s:element minOccurs="0" name="LetteringColourAndName" type="s:string"/>
                            <s:element minOccurs="0" name="MaterialType" type="s:string"/>
                            <s:element minOccurs="0" name="SpecialInstructions" type="s:string"/>
                            <s:element minOccurs="0" name="Note1" type="s:string"/>
                            <s:element minOccurs="0" name="Note2" type="s:string"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="s_GMDCodes">
                <s:sequence>
                    <s:element name="Code">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="16"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="Description">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="200"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                </s:sequence>
            </s:complexType>
            <s:complexType name="BibliographicLanguageCodes">
                <s:complexContent>
                    <s:extension base="s0:Persistent">
                        <s:sequence>
                            <s:element minOccurs="0" name="Inactive" type="s:boolean"/>
                            <s:element name="Code">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="16"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="Description">
                                <s:simpleType>
                                    <s:restriction base="s:string">
                                        <s:maxLength value="200"/>
                                    </s:restriction>
                                </s:simpleType>
                            </s:element>
                            <s:element minOccurs="0" name="HideFromOPAC" type="s:boolean"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:simpleType name="Url">
                <s:restriction base="s:string">
                    <s:maxLength value="2000"/>
                </s:restriction>
            </s:simpleType>
            <s:element name="Invoice">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="Supplier" type="s:string"/>
                        <s:element minOccurs="0" name="InvoiceNumber" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="InvoiceResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="InvoiceResult" type="s0:FullDetails"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="FullDetails">
                <s:complexContent>
                    <s:extension base="s0:Invoice">
                        <s:sequence>
                            <s:element minOccurs="0" name="InvoiceHeader" type="s0:InvoiceSuppliers"/>
                            <s:element minOccurs="0" name="TransactionLines" type="s0:services_Acquisitions_InvoiceTransaction_TransactionLine"/>
                        </s:sequence>
                    </s:extension>
                </s:complexContent>
            </s:complexType>
            <s:complexType name="Invoice">
                <s:sequence>
                    <s:element minOccurs="0" name="SupplierCode" type="s:string"/>
                    <s:element minOccurs="0" name="InvoiceNumber" type="s:string"/>
                    <s:element minOccurs="0" name="DateApproved" type="s:date"/>
                    <s:element minOccurs="0" name="FinalPaymentDate" type="s:date"/>
                    <s:element minOccurs="0" name="InvoiceNotes" type="s:string"/>
                    <s:element minOccurs="0" name="PaymentMethod" type="s:string"/>
                    <s:element minOccurs="0" name="InvoiceDate" type="s:date"/>
                    <s:element minOccurs="0" name="OrderLine" type="s:string"/>
                    <s:element minOccurs="0" name="SpecialAdditionalCostsFCC" type="s:string"/>
                    <s:element minOccurs="0" name="SpecialAdditionalCostsNotes" type="s:string"/>
                    <s:element minOccurs="0" name="Tax1FCC" type="s:string"/>
                    <s:element minOccurs="0" name="Tax2FCC" type="s:string"/>
                    <s:element minOccurs="0" name="Tax3FCC" type="s:string"/>
                    <s:element minOccurs="0" name="FreightFCC" type="s:string"/>
                    <s:element minOccurs="0" name="HandlingFCC" type="s:string"/>
                    <s:element minOccurs="0" name="DiscountFCC" type="s:string"/>
                    <s:element minOccurs="0" name="ClientNumber" type="s:string"/>
                    <s:element minOccurs="0" name="InvoiceType" type="s:string"/>
                    <s:element minOccurs="0" name="TaxAmount" type="s:decimal"/>
                    <s:element minOccurs="0" name="ArchiveDateTime" type="s0:Horolog"/>
                    <s:element minOccurs="0" name="InvoiceStatus" type="s:string"/>
                    <s:element minOccurs="0" name="AmountApproved" type="s:decimal"/>
                    <s:element minOccurs="0" name="AmountPaid" type="s:decimal"/>
                    <s:element minOccurs="0" name="CostTotal" type="s:decimal"/>
                    <s:element minOccurs="0" name="CostTotalForeign" type="s:decimal"/>
                    <s:element minOccurs="0" name="OrderLineTotal" type="s:decimal"/>
                    <s:element minOccurs="0" name="OrderLineTotalForeign" type="s:decimal"/>
                    <s:element minOccurs="0" name="AdjustmentTotal" type="s:decimal"/>
                    <s:element minOccurs="0" name="AdjustmentTotalForeign" type="s:decimal"/>
                    <s:element minOccurs="0" name="InvoiceTotal" type="s:decimal"/>
                    <s:element minOccurs="0" name="InvoiceTotalForeign" type="s:decimal"/>
                    <s:element minOccurs="0" name="Currency" type="s:string"/>
                    <s:element minOccurs="0" name="InternalNotes" type="s:string"/>
                    <s:element minOccurs="0" name="SupplierDisabled" type="s:boolean"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="InvoiceSuppliers">
                <s:sequence>
                    <s:element minOccurs="0" name="SupplierCode" type="s:string"/>
                    <s:element minOccurs="0" name="SupplierName" type="s:string"/>
                    <s:element minOccurs="0" name="InvoiceAmount" type="s:decimal"/>
                    <s:element minOccurs="0" name="InvoiceAmountForeign" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre1" type="s:string"/>
                    <s:element minOccurs="0" name="FCCValue1" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre2" type="s:string"/>
                    <s:element minOccurs="0" name="FCCValue2" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre3" type="s:string"/>
                    <s:element minOccurs="0" name="FCCValue3" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre4" type="s:string"/>
                    <s:element minOccurs="0" name="FCCValue4" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre5" type="s:string"/>
                    <s:element minOccurs="0" name="FCCValue5" type="s:decimal"/>
                    <s:element minOccurs="0" name="LastInvoiceTransactionNumber" type="s:long"/>
                    <s:element minOccurs="0" name="PaymentVoucherNumber" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="services_Acquisitions_InvoiceTransaction_TransactionLine">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="TransactionLine">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="TransactionNumber" type="s:long"/>
                                <s:element minOccurs="0" name="TransactionDate" type="s:date"/>
                                <s:element minOccurs="0" name="Amount" type="s:decimal"/>
                                <s:element minOccurs="0" name="PaymentMethod" type="s:string"/>
                                <s:element minOccurs="0" name="Narration" type="s:string"/>
                                <s:element minOccurs="0" name="UserNumber" type="s:string"/>
                                <s:element minOccurs="0" name="DateEntered" type="s:dateTime"/>
                                <s:element minOccurs="0" name="WorkstationID" type="s:string"/>
                                <s:element minOccurs="0" name="TransactionType" type="s:string"/>
                                <s:element minOccurs="0" name="GrossAmountForeign" type="s:decimal"/>
                                <s:element minOccurs="0" name="GrossAmount" type="s:decimal"/>
                                <s:element minOccurs="0" name="DiscountPercent" type="s:decimal"/>
                                <s:element minOccurs="0" name="DiscountAmountForeign" type="s:decimal"/>
                                <s:element minOccurs="0" name="DiscountAmount" type="s:decimal"/>
                                <s:element minOccurs="0" name="AmountForeign" type="s:decimal"/>
                                <s:element minOccurs="0" name="OrderLine" type="s:string"/>
                                <s:element minOccurs="0" name="InvoiceStatisticsCode" type="s:string"/>
                                <s:element minOccurs="0" name="CostType" type="s:string"/>
                                <s:element minOccurs="0" name="InvoiceCostLocation" type="s:string"/>
                                <s:element minOccurs="0" name="TaxType" type="s:string"/>
                                <s:element minOccurs="0" name="BasePeriod" type="s:string"/>
                                <s:element minOccurs="0" name="CurrencyCode" type="s:string"/>
                                <s:element minOccurs="0" name="BudgetYear" type="s:long"/>
                                <s:element minOccurs="0" name="AllocatedTo" type="s:string"/>
                                <s:element minOccurs="0" name="BarcodeOrAcronymSubscription" type="s:string"/>
                                <s:element minOccurs="0" name="TaxAmount" type="s:decimal"/>
                                <s:element minOccurs="0" name="OriginalOrderValue" type="s:decimal"/>
                                <s:element minOccurs="0" name="EUIndicator" type="s:string"/>
                                <s:element minOccurs="0" name="BibliographicUnits" type="s:string"/>
                                <s:element minOccurs="0" name="SequenceNumber" type="s:string"/>
                                <s:element minOccurs="0" name="Field01" type="s:string"/>
                                <s:element minOccurs="0" name="Field02" type="s:string"/>
                                <s:element minOccurs="0" name="Field03" type="s:string"/>
                                <s:element minOccurs="0" name="Field04" type="s:string"/>
                                <s:element minOccurs="0" name="Field05" type="s:string"/>
                                <s:element minOccurs="0" name="Field06" type="s:string"/>
                                <s:element minOccurs="0" name="Field07" type="s:string"/>
                                <s:element minOccurs="0" name="Field08" type="s:string"/>
                                <s:element minOccurs="0" name="Field09" type="s:string"/>
                                <s:element minOccurs="0" name="Field10" type="s:string"/>
                                <s:element minOccurs="0" name="Field11" type="s:string"/>
                                <s:element minOccurs="0" name="Field12" type="s:string"/>
                                <s:element minOccurs="0" name="Field13" type="s:string"/>
                                <s:element minOccurs="0" name="Field14" type="s:string"/>
                                <s:element minOccurs="0" name="Field15" type="s:string"/>
                                <s:element minOccurs="0" name="Field16" type="s:string"/>
                                <s:element minOccurs="0" name="Field17" type="s:string"/>
                                <s:element minOccurs="0" name="Field18" type="s:string"/>
                                <s:element minOccurs="0" name="Field19" type="s:string"/>
                                <s:element minOccurs="0" name="Field20" type="s:string"/>
                                <s:element minOccurs="0" name="Field21" type="s:string"/>
                                <s:element minOccurs="0" name="Field22" type="s:string"/>
                                <s:element minOccurs="0" name="Field23" type="s:string"/>
                                <s:element minOccurs="0" name="Field24" type="s:string"/>
                                <s:element minOccurs="0" name="Field25" type="s:string"/>
                                <s:element minOccurs="0" name="Field26" type="s:string"/>
                                <s:element minOccurs="0" name="Field27Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field28" type="s:string"/>
                                <s:element minOccurs="0" name="Field28Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field29" type="s:string"/>
                                <s:element minOccurs="0" name="Field29Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field30" type="s:string"/>
                                <s:element minOccurs="0" name="Field30Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field31" type="s:string"/>
                                <s:element minOccurs="0" name="Field31Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field32" type="s:string"/>
                                <s:element minOccurs="0" name="Field32Description" type="s:string"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="InvoiceEntry">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="InvoiceDetail" type="s0:Invoice"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="InvoiceEntryResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="InvoiceEntryResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ItemStatusUpdate">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                        <s:element minOccurs="0" name="ExceptionCode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ItemStatusUpdateResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="ItemStatusUpdateResult" type="s0:ItemStatusUpdateResponse"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="LoanHistory">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="LoanHistoryResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="LoanHistoryResult" type="s0:LBmem_LoanHistory_qryHistoryForMember"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="LBmem_LoanHistory_qryHistoryForMember">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="qryHistoryForMember">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="ID" type="s:string"/>
                                <s:element minOccurs="0" name="ArchivedAuthor" type="s:string"/>
                                <s:element minOccurs="0" name="ArchivedTitle" type="s:string"/>
                                <s:element minOccurs="0" name="Barcode" type="s:string"/>
                                <s:element minOccurs="0" name="BorrowerID" type="s:decimal"/>
                                <s:element minOccurs="0" name="DateOfLoan" type="s:date"/>
                                <s:element minOccurs="0" name="DateOfReturn" type="s:date"/>
                                <s:element minOccurs="0" name="DateTimeOfLoan" type="s:string"/>
                                <s:element minOccurs="0" name="RSN" type="s:decimal"/>
                                <s:element minOccurs="0" name="RSNString" type="s:string"/>
                                <s:element minOccurs="0" name="ReturnDateTime" type="s:string"/>
                                <s:element minOccurs="0" name="TimeString" type="s:string"/>
                                <s:element minOccurs="0" name="TimeStringReturn" type="s:string"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="OrderEntry">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:string"/>
                        <s:element minOccurs="0" name="Template" type="s:string"/>
                        <s:element minOccurs="0" name="OrderDetail" type="s0:Order"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="Order">
                <s:sequence>
                    <s:element minOccurs="0" name="OrderCode" type="s:string"/>
                    <s:element minOccurs="0" name="Invoices" type="s:string"/>
                    <s:element minOccurs="0" name="Claims" type="s:string"/>
                    <s:element minOccurs="0" name="Parts" type="s:string"/>
                    <s:element minOccurs="0" name="OrderLine" type="s:long"/>
                    <s:element minOccurs="0" name="RSN" type="s:string"/>
                    <s:element minOccurs="0" name="Title">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="1000"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="ForBranch" type="s:string"/>
                    <s:element minOccurs="0" name="DateOrdered" type="s:date"/>
                    <s:element minOccurs="0" name="TotalAmount" type="s:decimal"/>
                    <s:element minOccurs="0" name="SupplierID" type="s:long"/>
                    <s:element minOccurs="0" name="SupplierCode" type="s:string"/>
                    <s:element minOccurs="0" name="RequestingBorrower" type="s:string"/>
                    <s:element minOccurs="0" name="Barcode" type="s:string"/>
                    <s:element minOccurs="0" name="Collection" type="s:string"/>
                    <s:element minOccurs="0" name="Allocation" type="s:string"/>
                    <s:element minOccurs="0" name="ExpectedForeignCost" type="s:decimal"/>
                    <s:element minOccurs="0" name="ExpectedForeignCurrencyCode" type="s:string"/>
                    <s:element minOccurs="0" name="CancellationDate" type="s:date"/>
                    <s:element minOccurs="0" name="DatePaid" type="s:date"/>
                    <s:element minOccurs="0" name="InvoiceNumber" type="s:string"/>
                    <s:element minOccurs="0" name="AmountPaid" type="s:decimal"/>
                    <s:element minOccurs="0" name="OriginalPrice" type="s:decimal"/>
                    <s:element minOccurs="0" name="InitiatorCode" type="s:string"/>
                    <s:element minOccurs="0" name="Source" type="s:string"/>
                    <s:element minOccurs="0" name="Requestor" type="s:string"/>
                    <s:element minOccurs="0" name="OrderType" type="s:string"/>
                    <s:element minOccurs="0" name="ClaimCode" type="s:string"/>
                    <s:element minOccurs="0" name="User" type="s:string"/>
                    <s:element minOccurs="0" name="CreatedBy" type="s:string"/>
                    <s:element minOccurs="0" name="OrderStatus" type="s:string"/>
                    <s:element minOccurs="0" name="PrintStatus" type="s:string"/>
                    <s:element minOccurs="0" name="DatePrinted" type="s:date"/>
                    <s:element minOccurs="0" name="ClientCode" type="s:string"/>
                    <s:element minOccurs="0" name="InvoiceStatus" type="s:string"/>
                    <s:element minOccurs="0" name="InvoiceDate" type="s:date"/>
                    <s:element minOccurs="0" name="OwnerBranch" type="s:string"/>
                    <s:element minOccurs="0" name="CopyPrice" type="s:decimal"/>
                    <s:element minOccurs="0" name="Discount" type="s:decimal"/>
                    <s:element minOccurs="0" name="Acquisition" type="s:string"/>
                    <s:element minOccurs="0" name="DispatchCode" type="s:string"/>
                    <s:element minOccurs="0" name="ForeignDiscount" type="s:decimal"/>
                    <s:element minOccurs="0" name="PurchasedAt" type="s:string"/>
                    <s:element minOccurs="0" name="StackLocation" type="s:string"/>
                    <s:element minOccurs="0" name="SupplierNote">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="1000"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="InternalNotes">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="1000"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="ExpectedDeliveryDate" type="s:date"/>
                    <s:element minOccurs="0" name="ExpectedPaymentDate" type="s:date"/>
                    <s:element minOccurs="0" name="Quantity" type="s:long"/>
                    <s:element minOccurs="0" name="SubscriptionAmount" type="s:decimal"/>
                    <s:element minOccurs="0" name="Freight" type="s:decimal"/>
                    <s:element minOccurs="0" name="SupplierTitleCode" type="s:string"/>
                    <s:element minOccurs="0" name="FreightForeign" type="s:decimal"/>
                    <s:element minOccurs="0" name="DeliveryDocket" type="s:string"/>
                    <s:element minOccurs="0" name="DeliveryDocketDate" type="s:date"/>
                    <s:element minOccurs="0" name="DeliveryAddressCode" type="s:string"/>
                    <s:element minOccurs="0" name="OrderLineCount" type="s:long"/>
                    <s:element minOccurs="0" name="SubscriptionNumber" type="s:long"/>
                    <s:element minOccurs="0" name="ClaimDateOveride" type="s:date"/>
                    <s:element minOccurs="0" name="ClaimDispatchAddressCode" type="s:string"/>
                    <s:element minOccurs="0" name="Statistics1" type="s:string"/>
                    <s:element minOccurs="0" name="TaxCode" type="s:string"/>
                    <s:element minOccurs="0" name="TaxAmount" type="s:decimal"/>
                    <s:element minOccurs="0" name="BudgetYear" type="s:string"/>
                    <s:element minOccurs="0" name="FundCostCentre1" type="s:string"/>
                    <s:element minOccurs="0" name="FCCSplitPercentage1" type="s:long"/>
                    <s:element minOccurs="0" name="FCCValue1" type="s:decimal"/>
                    <s:element minOccurs="0" name="FCCMultiInvoiceAmount1" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre2" type="s:string"/>
                    <s:element minOccurs="0" name="FCCSplitPercentage2" type="s:long"/>
                    <s:element minOccurs="0" name="FCCValue2" type="s:decimal"/>
                    <s:element minOccurs="0" name="FCCMultiInvoiceAmount2" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre3" type="s:string"/>
                    <s:element minOccurs="0" name="FCCSplitPercentage3" type="s:long"/>
                    <s:element minOccurs="0" name="FCCValue3" type="s:decimal"/>
                    <s:element minOccurs="0" name="FCCMultiInvoiceAmount3" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre4" type="s:string"/>
                    <s:element minOccurs="0" name="FCCSplitPercentage4" type="s:long"/>
                    <s:element minOccurs="0" name="FCCValue4" type="s:decimal"/>
                    <s:element minOccurs="0" name="FCCMultiInvoiceAmount4" type="s:decimal"/>
                    <s:element minOccurs="0" name="FundCostCentre5" type="s:string"/>
                    <s:element minOccurs="0" name="FCCSplitPercentage5" type="s:long"/>
                    <s:element minOccurs="0" name="FCCValue5" type="s:decimal"/>
                    <s:element minOccurs="0" name="FCCMultiInvoiceAmount5" type="s:decimal"/>
                    <s:element minOccurs="0" name="PartsCount" type="s:long"/>
                    <s:element minOccurs="0" name="OwnRSN" type="s:boolean"/>
                </s:sequence>
            </s:complexType>
            <s:element name="OrderEntryResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="OrderEntryResult" type="s0:Order"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="OrderInformation">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="OrderNumber" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="OrderInformationResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="OrderInformationResult" type="s0:LBfin_Orders_OrderHeader_qryAllDetails"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="LBfin_Orders_OrderHeader_qryAllDetails">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="qryAllDetails">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="ID" type="s:string"/>
                                <s:element minOccurs="0" name="BlanketOrder" type="s:boolean"/>
                                <s:element minOccurs="0" name="ClientCode" type="s:string"/>
                                <s:element minOccurs="0" name="ClosedFlag" type="s:boolean"/>
                                <s:element minOccurs="0" name="InvoiceAddress" type="s:string"/>
                                <s:element minOccurs="0" name="LeaveStandingOrderOpen" type="s:boolean"/>
                                <s:element minOccurs="0" name="NotesSupplier" type="s:string"/>
                                <s:element minOccurs="0" name="NotesWebopac" type="s:string"/>
                                <s:element minOccurs="0" name="OpenOrder" type="s:boolean"/>
                                <s:element minOccurs="0" name="OrderCode" type="s:string"/>
                                <s:element minOccurs="0" name="OrderLineCount" type="s:long"/>
                                <s:element minOccurs="0" name="StandingOrder" type="s:boolean"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="OrderLineInformation">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="OrderNumber" type="s:string"/>
                        <s:element minOccurs="0" name="LineNumber" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="OrderLineInformationResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="OrderLineInformationResult" type="s0:Order"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="OrderStatus">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="OrderNumber" type="s:string"/>
                        <s:element minOccurs="0" name="OrderLine" type="s:string"/>
                        <s:element minOccurs="0" name="Status" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="OrderStatusResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="OrderStatusResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PayAnyFee">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                        <s:element minOccurs="0" name="PayAmount" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PayAnyFeeResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="PayAnyFeeResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PayFee">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                        <s:element minOccurs="0" name="Reference" type="s:string"/>
                        <s:element minOccurs="0" name="PayAmount" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PayFeeResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="PayFeeResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PurgeItemChangeList">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="dbName" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PurgeItemChangeListResponse">
                <s:complexType>
                    <s:sequence/>
                </s:complexType>
            </s:element>
            <s:element name="PurgeItemChangeListUpto">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="id" type="s:long"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PurgeItemChangeListUptoResponse">
                <s:complexType>
                    <s:sequence/>
                </s:complexType>
            </s:element>
            <s:element name="PurgeMemberChangeList">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="dbName" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PurgeMemberChangeListResponse">
                <s:complexType>
                    <s:sequence/>
                    <s:attribute name="PurgeMemberChangeListResult" type="s:string" use="required"/>
                </s:complexType>
            </s:element>
            <s:element name="PurgeMemberChangeListUpto">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="id" type="s:long"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PurgeMemberChangeListUptoResponse">
                <s:complexType>
                    <s:sequence/>
                    <s:attribute name="PurgeMemberChangeListUptoResult" type="s:string" use="required"/>
                </s:complexType>
            </s:element>
            <s:element name="RaiseCharge">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                        <s:element minOccurs="0" name="TransactionType" type="s:string"/>
                        <s:element minOccurs="0" name="Amount" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="RaiseChargeResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="RaiseChargeResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="RecordEntry">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:string"/>
                        <s:element minOccurs="0" name="format" type="s:string"/>
                        <s:element minOccurs="0" name="Record" type="s0:record"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="record">
                <s:sequence>
                    <s:element minOccurs="0" name="leader" type="s:string"/>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="controlfield" nillable="true" type="s0:controlfield"/>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="datafield" nillable="true" type="s0:datafield"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="ArrayOfcontrolfield">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="controlfield" nillable="true" type="s0:controlfield"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="controlfield">
                <s:simpleContent>
                    <s:extension base="s:string">
                        <s:attribute name="tag" type="s:string"/>
                    </s:extension>
                </s:simpleContent>
            </s:complexType>
            <s:complexType name="ArrayOfdatafield">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="datafield" nillable="true" type="s0:datafield"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="datafield">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="subfield" nillable="true" type="s0:subfield"/>
                </s:sequence>
                <s:attribute name="tag" type="s:string"/>
                <s:attribute name="ind1" type="s:string"/>
                <s:attribute name="ind2" type="s:string"/>
            </s:complexType>
            <s:complexType name="ArrayOfsubfield">
                <s:sequence>
                    <s:element maxOccurs="unbounded" minOccurs="0" name="subfield" nillable="true" type="s0:subfield"/>
                </s:sequence>
            </s:complexType>
            <s:complexType name="subfield">
                <s:simpleContent>
                    <s:extension base="s:string">
                        <s:attribute name="code" type="s:string"/>
                    </s:extension>
                </s:simpleContent>
            </s:complexType>
            <s:element name="RecordEntryResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="RecordEntryResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="RenewItem">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="RenewItemResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="RenewItemResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ReserveHistory">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ReserveHistoryResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="ReserveHistoryResult" type="s0:LBmem_ReserveHistory_qryHistoryForMember"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="LBmem_ReserveHistory_qryHistoryForMember">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="qryHistoryForMember">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="ID" type="s:string"/>
                                <s:element minOccurs="0" name="Barcode" type="s:string"/>
                                <s:element minOccurs="0" name="BorrowerID" type="s:decimal"/>
                                <s:element minOccurs="0" name="DateOfReserve" type="s:date"/>
                                <s:element minOccurs="0" name="DateTimeOfReserve" type="s:string"/>
                                <s:element minOccurs="0" name="DisplayBarcode" type="s:string"/>
                                <s:element minOccurs="0" name="DisplayStatus" type="s:string"/>
                                <s:element minOccurs="0" name="RSN" type="s:decimal"/>
                                <s:element minOccurs="0" name="RSNString" type="s:string"/>
                                <s:element minOccurs="0" name="Status" type="s:string"/>
                                <s:element minOccurs="0" name="TimeString" type="s:string"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="ReserveItem">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                        <s:element minOccurs="0" name="rsn" type="s:string"/>
                        <s:element minOccurs="0" name="ItemBarcode" type="s:string"/>
                        <s:element minOccurs="0" name="expirydate" type="s:string"/>
                        <s:element minOccurs="0" name="PickupBranch" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ReserveItemResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="ReserveItemResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="SerialSubscriptions">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:string"/>
                        <s:element minOccurs="0" name="SubNo" type="s:string"/>
                        <s:element minOccurs="0" name="Subscription" type="s0:Subscription"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="SerialSubscriptionsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="SerialSubscriptionsResult" type="s0:DataSet"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="DataSet">
                <s:sequence>
                    <s:element ref="s:schema"/>
                    <s:any/>
                </s:sequence>
            </s:complexType>
            <s:element name="SerialSubscriptionsForRSN">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="SerialSubscriptionsForRSNResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="SerialSubscriptionsForRSNResult" type="s0:LBser_Queries_qrySubSel"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="LBser_Queries_qrySubSel">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="qrySubSel">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="Nbr" type="s:string"/>
                                <s:element minOccurs="0" name="SubscriptionDescription" type="s:string"/>
                                <s:element minOccurs="0" name="Status" type="s:string"/>
                                <s:element minOccurs="0" name="OwnerBranch" type="s:string"/>
                                <s:element minOccurs="0" name="CurrentBranch" type="s:string"/>
                                <s:element minOccurs="0" name="CallNumber" type="s:string"/>
                                <s:element minOccurs="0" name="OrderNumber" type="s:string"/>
                                <s:element minOccurs="0" name="FirstIssueDate" type="s:string"/>
                                <s:element minOccurs="0" name="ExpiryDate" type="s:string"/>
                                <s:element minOccurs="0" name="LastReceived" type="s:string"/>
                                <s:element minOccurs="0" name="MYROWCOLOUR" type="s:string"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="SetMemberDetail">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="dbName" type="s:string"/>
                        <s:element minOccurs="0" name="detail" type="s0:MemberDetail"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="MemberDetail">
                <s:sequence>
                    <s:element minOccurs="0" name="borrowernumber" type="s:string"/>
                    <s:element minOccurs="0" name="firstname" type="s:string"/>
                    <s:element minOccurs="0" name="lastname" type="s:string"/>
                    <s:element minOccurs="0" name="address1" type="s:string"/>
                    <s:element minOccurs="0" name="address2" type="s:string"/>
                    <s:element minOccurs="0" name="suburb" type="s:string"/>
                    <s:element minOccurs="0" name="postcode" type="s:string"/>
                    <s:element minOccurs="0" name="country" type="s:string"/>
                    <s:element minOccurs="0" name="formofaddress" type="s:string"/>
                    <s:element minOccurs="0" name="gender" type="s:string"/>
                    <s:element minOccurs="0" name="restrictlendperm" type="s:string"/>
                    <s:element minOccurs="0" name="allowdirectdebt" type="s:string"/>
                    <s:element minOccurs="0" name="materialresp" type="s:string"/>
                    <s:element minOccurs="0" name="altaddress1" type="s:string"/>
                    <s:element minOccurs="0" name="altaddress2" type="s:string"/>
                    <s:element minOccurs="0" name="altsuburb" type="s:string"/>
                    <s:element minOccurs="0" name="altpostcode" type="s:string"/>
                    <s:element minOccurs="0" name="altcountry" type="s:string"/>
                    <s:element minOccurs="0" name="GuarantorName" type="s:string"/>
                    <s:element minOccurs="0" name="GuarantorAdd" type="s:string"/>
                    <s:element minOccurs="0" name="GuarantorTown" type="s:string"/>
                    <s:element minOccurs="0" name="GuarantorEmail" type="s:string"/>
                    <s:element minOccurs="0" name="GuarantorPostcode" type="s:string"/>
                    <s:element minOccurs="0" name="GuarantorCountry" type="s:string"/>
                    <s:element minOccurs="0" name="GuarantorPhone" type="s0:PhoneNumber"/>
                    <s:element minOccurs="0" name="GuarantorMobile" type="s:string"/>
                    <s:element minOccurs="0" name="memberstatus" type="s:string"/>
                    <s:element minOccurs="0" name="joindate" type="s:string"/>
                    <s:element minOccurs="0" name="birthdate" type="s:string"/>
                    <s:element minOccurs="0" name="expirydate" type="s:string"/>
                    <s:element minOccurs="0" name="locality" type="s:string"/>
                    <s:element minOccurs="0" name="branch" type="s:string"/>
                    <s:element minOccurs="0" name="category" type="s:string"/>
                    <s:element minOccurs="0" name="agegroup" type="s:string"/>
                    <s:element minOccurs="0" name="occupation" type="s:string"/>
                    <s:element minOccurs="0" name="homephone" type="s0:PhoneNumber"/>
                    <s:element minOccurs="0" name="mobile" type="s:string"/>
                    <s:element minOccurs="0" name="businessphone" type="s0:PhoneNumber"/>
                    <s:element minOccurs="0" name="fax" type="s0:PhoneNumber"/>
                    <s:element minOccurs="0" name="forbiddencoll" type="s:string"/>
                    <s:element minOccurs="0" name="memberid" type="s:string"/>
                    <s:element minOccurs="0" name="language" type="s:string"/>
                    <s:element minOccurs="0" name="organisation" type="s:string"/>
                    <s:element minOccurs="0" name="specialmembexp" type="s:string"/>
                    <s:element minOccurs="0" name="email" type="s:string"/>
                    <s:element minOccurs="0" name="residential1" type="s:string"/>
                    <s:element minOccurs="0" name="residential2" type="s:string"/>
                    <s:element minOccurs="0" name="ressuburb" type="s:string"/>
                    <s:element minOccurs="0" name="rescountry" type="s:string"/>
                    <s:element minOccurs="0" name="respostcode" type="s:string"/>
                    <s:element minOccurs="0" name="identification" type="s:string"/>
                    <s:element minOccurs="0" name="userfield1" type="s:string"/>
                    <s:element minOccurs="0" name="userfield2" type="s:string"/>
                    <s:element minOccurs="0" name="userfield3" type="s:string"/>
                    <s:element minOccurs="0" name="userfield4" type="s:string"/>
                    <s:element minOccurs="0" name="userfield5" type="s:string"/>
                    <s:element minOccurs="0" name="userfield6" type="s:string"/>
                    <s:element minOccurs="0" name="userfield7" type="s:string"/>
                    <s:element minOccurs="0" name="userfield8" type="s:string"/>
                    <s:element minOccurs="0" name="userfield9" type="s:string"/>
                    <s:element minOccurs="0" name="userfield10" type="s:string"/>
                    <s:element minOccurs="0" name="userfield11" type="s:string"/>
                    <s:element minOccurs="0" name="userfield12" type="s:string"/>
                    <s:element minOccurs="0" name="userfield13" type="s:string"/>
                    <s:element minOccurs="0" name="userfield14" type="s:string"/>
                    <s:element minOccurs="0" name="userfield15" type="s:string"/>
                    <s:element minOccurs="0" name="userfield16" type="s:string"/>
                    <s:element minOccurs="0" name="userfield17" type="s:string"/>
                    <s:element minOccurs="0" name="userfield18" type="s:string"/>
                    <s:element minOccurs="0" name="userfield19" type="s:string"/>
                    <s:element minOccurs="0" name="userfield20" type="s:string"/>
                    <s:element minOccurs="0" name="userfield21" type="s:string"/>
                    <s:element minOccurs="0" name="userfield22" type="s:string"/>
                    <s:element minOccurs="0" name="userfield23" type="s:string"/>
                    <s:element minOccurs="0" name="userfield24" type="s:string"/>
                    <s:element minOccurs="0" name="userfield25" type="s:string"/>
                    <s:element minOccurs="0" name="userfield26" type="s:string"/>
                    <s:element minOccurs="0" name="userfield27" type="s:string"/>
                    <s:element minOccurs="0" name="userfield28" type="s:string"/>
                    <s:element minOccurs="0" name="userfield29" type="s:string"/>
                    <s:element minOccurs="0" name="userfield30" type="s:string"/>
                    <s:element minOccurs="0" name="userfield31" type="s:string"/>
                    <s:element minOccurs="0" name="userfield32" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:element name="SetMemberDetailResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="SetMemberDetailResult" type="s0:CodeAlert"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="CodeAlert">
                <s:simpleContent>
                    <s:extension base="s:string">
                        <s:attribute name="code" type="s:string" use="required"/>
                    </s:extension>
                </s:simpleContent>
            </s:complexType>
            <s:element name="Supplier">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="Supplier" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="SupplierResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="SupplierResult" type="s0:LBfin_Suppliers_Supplier_qryGetAllById"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="LBfin_Suppliers_Supplier_qryGetAllById">
                <s:choice maxOccurs="unbounded" minOccurs="0">
                    <s:element name="qryGetAllById">
                        <s:complexType>
                            <s:sequence>
                                <s:element minOccurs="0" name="LIB_SUPPLIERS" type="s:long"/>
                                <s:element minOccurs="0" name="ABN" type="s:string"/>
                                <s:element minOccurs="0" name="Address" type="s:string"/>
                                <s:element minOccurs="0" name="Address2" type="s:string"/>
                                <s:element minOccurs="0" name="AddressCity" type="s:string"/>
                                <s:element minOccurs="0" name="AddressPostCode" type="s:string"/>
                                <s:element minOccurs="0" name="AddressState" type="s:string"/>
                                <s:element minOccurs="0" name="AgreementNumber" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Address" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Address2" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1City" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1CompanyName" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Contact" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Department" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Email" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Fax" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Phone1" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Phone2" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1Postcode" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate1State" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Address" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Address2" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2City" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2CompanyName" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Contact" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Department" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Email" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Fax" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Phone1" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Phone2" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2Postcode" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate2State" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Address" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Address2" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3City" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3CompanyName" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Contact" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Department" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Email" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Fax" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Phone1" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Phone2" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3Postcode" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate3State" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Address" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Address2" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4City" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4CompanyName" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Contact" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Department" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Email" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Fax" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Phone1" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Phone2" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4Postcode" type="s:string"/>
                                <s:element minOccurs="0" name="Alternate4State" type="s:string"/>
                                <s:element minOccurs="0" name="BIC" type="s:string"/>
                                <s:element minOccurs="0" name="BankAccountCode" type="s:string"/>
                                <s:element minOccurs="0" name="BankAccountNo" type="s:string"/>
                                <s:element minOccurs="0" name="BankName" type="s:string"/>
                                <s:element minOccurs="0" name="Budget" type="s:string"/>
                                <s:element minOccurs="0" name="BusinessPhone" type="s:string"/>
                                <s:element minOccurs="0" name="CityForObligatoryCopy" type="s:string"/>
                                <s:element minOccurs="0" name="CompanyName" type="s:string"/>
                                <s:element minOccurs="0" name="CompanyTaxNumber" type="s:string"/>
                                <s:element minOccurs="0" name="ContactGivenName" type="s:string"/>
                                <s:element minOccurs="0" name="ContactName" type="s:string"/>
                                <s:element minOccurs="0" name="ContactSurname" type="s:string"/>
                                <s:element minOccurs="0" name="ContactTitle" type="s:string"/>
                                <s:element minOccurs="0" name="ControlAccount" type="s:string"/>
                                <s:element minOccurs="0" name="CountryCode" type="s:string"/>
                                <s:element minOccurs="0" name="CurrencyCode" type="s:string"/>
                                <s:element minOccurs="0" name="DateLastChanged" type="s:date"/>
                                <s:element minOccurs="0" name="Department" type="s:string"/>
                                <s:element minOccurs="0" name="DisableSupplier" type="s:boolean"/>
                                <s:element minOccurs="0" name="Discount" type="s:string"/>
                                <s:element minOccurs="0" name="DispatchCode" type="s:string"/>
                                <s:element minOccurs="0" name="Email" type="s:string"/>
                                <s:element minOccurs="0" name="Fax" type="s:string"/>
                                <s:element minOccurs="0" name="Field27Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field28Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field29Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field30Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field31Description" type="s:string"/>
                                <s:element minOccurs="0" name="Field32Description" type="s:string"/>
                                <s:element minOccurs="0" name="IBAN" type="s:string"/>
                                <s:element minOccurs="0" name="InternalID" type="s:long"/>
                                <s:element minOccurs="0" name="LastClientCodeEntered" type="s:string"/>
                                <s:element minOccurs="0" name="LastOrderLine" type="s:string"/>
                                <s:element minOccurs="0" name="Modem" type="s:string"/>
                                <s:element minOccurs="0" name="Network" type="s:string"/>
                                <s:element minOccurs="0" name="PaymentTerms" type="s:long"/>
                                <s:element minOccurs="0" name="Period" type="s:string"/>
                                <s:element minOccurs="0" name="PostalAddress" type="s:string"/>
                                <s:element minOccurs="0" name="PostalAddress2" type="s:string"/>
                                <s:element minOccurs="0" name="PostalCity" type="s:string"/>
                                <s:element minOccurs="0" name="PostalPostCode" type="s:string"/>
                                <s:element minOccurs="0" name="PostalState" type="s:string"/>
                                <s:element minOccurs="0" name="PrivatePhone" type="s:string"/>
                                <s:element minOccurs="0" name="SalesTaxID" type="s:string"/>
                                <s:element minOccurs="0" name="SupplierAlphaKey" type="s:string"/>
                                <s:element minOccurs="0" name="SupplierCode" type="s:string"/>
                                <s:element minOccurs="0" name="SupplierFormType" type="s:string"/>
                                <s:element minOccurs="0" name="SupplierName" type="s:string"/>
                                <s:element minOccurs="0" name="SupplierPrintType" type="s:string"/>
                                <s:element minOccurs="0" name="SupplierStatisticsACode" type="s:string"/>
                                <s:element minOccurs="0" name="SupplierStatisticsBCode" type="s:string"/>
                                <s:element minOccurs="0" name="SupplierType" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField1" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField10" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField11" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField12" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField13" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField14" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField15" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField16" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField17" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField18" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField19" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField2" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField20" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField21" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField22" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField23" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField24" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField25" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField26" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField27" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField28" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField29" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField3" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField30" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField31" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField32" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField4" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField5" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField6" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField7" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField8" type="s:string"/>
                                <s:element minOccurs="0" name="UserDefinedField9" type="s:string"/>
                                <s:element minOccurs="0" name="WWWAddress" type="s:string"/>
                            </s:sequence>
                        </s:complexType>
                    </s:element>
                </s:choice>
            </s:complexType>
            <s:element name="UpdateBranch">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="Code" type="s:string"/>
                        <s:element minOccurs="0" name="Branch" type="s0:Branch"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="UpdateBranchResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="UpdateBranchResult" type="s0:Branch"/>
                        <s:element minOccurs="0" name="OperationResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="UpdateMember">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="MemberCode" type="s:string"/>
                        <s:element minOccurs="0" name="UpdatedMember" type="s0:Member"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="UpdateMemberResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="UpdateMemberResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="UpdateSerialSubscription">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="RSN" type="s:string"/>
                        <s:element minOccurs="0" name="SubNo" type="s:long"/>
                        <s:element minOccurs="0" name="Subscription" type="s0:Subscription"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="UpdateSerialSubscriptionResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="UpdateSerialSubscriptionResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="WebOpacTailoringParameters">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="WebOpacTailoringParametersResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="WebOpacTailoringParametersResult" type="s0:WebOpacTailoring"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="WebOpacTailoring">
                <s:sequence>
                    <s:element minOccurs="0" name="MainMenuSearchSimple" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchAdvancedStandard" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchAdvancedAlternate" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchJournal" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchExpert" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchRefine" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchNewItems" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchNewItemList" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchNewByGroup" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchZ3950" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchPortal" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuMemberServices" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSignUp" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuPreferences" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuLibraryMap" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuOpeningHours" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSignon" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuAbout" type="s:boolean"/>
                    <s:element minOccurs="0" name="MainMenuSearchHistory" type="s:boolean"/>
                    <s:element minOccurs="0" name="SignonDisplayLog" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchGOwnBrLimLoc" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimPubYear" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimServicePoint" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimSortPref" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimPhrase" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimSmartBrowse" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimLimLoc" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimLimSpecialColl" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimLimLang" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchSimLimAvail" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvPageSize" type="s:long"/>
                    <s:element minOccurs="0" name="SearchAdvLimCol" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvMatType" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvLimLoc" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvLimYr" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvServicePoint" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvSortPref" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvCollToGMD" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvGMDToColl" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchCollOrGMD" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvGMDGroups" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvCollGroups" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvIndexTerm" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvLimSpecialColl" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvLimLang" type="s:boolean"/>
                    <s:element minOccurs="0" name="SearchAdvLimAvail" type="s:boolean"/>
                    <s:element minOccurs="0" name="Z3950Max" type="s:long"/>
                    <s:element minOccurs="0" name="Z3950Timeout" type="s:long"/>
                    <s:element minOccurs="0" name="Z3950Code" type="s:string"/>
                    <s:element minOccurs="0" name="AlternateDisableAVSearch" type="s:boolean"/>
                    <s:element minOccurs="0" name="AlternateDisableAdv" type="s:boolean"/>
                    <s:element minOccurs="0" name="AlternateEnable" type="s:boolean"/>
                    <s:element minOccurs="0" name="DefaultSearchPage" type="s:long"/>
                    <s:element minOccurs="0" name="DefaultSortMethod" type="s:long"/>
                    <s:element minOccurs="0" name="NoItemsPerPage" type="s:long"/>
                    <s:element minOccurs="0" name="AuthorityEntries" type="s:boolean"/>
                    <s:element minOccurs="0" name="DisplayRSN" type="s:string"/>
                    <s:element minOccurs="0" name="DisplayMemberAddress" type="s:string"/>
                    <s:element minOccurs="0" name="DocumentsLocation" type="s:string"/>
                    <s:element minOccurs="0" name="FullSecureServer" type="s:string"/>
                    <s:element minOccurs="0" name="IntranetOnly" type="s:string"/>
                    <s:element minOccurs="0" name="LibraryHoursDocument" type="s:string"/>
                    <s:element minOccurs="0" name="LibraryMapImage" type="s:string"/>
                    <s:element minOccurs="0" name="LibraryMapNotation">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="1024"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="LogoFile" type="s:string"/>
                    <s:element minOccurs="0" name="NeverReservesToHolds" type="s:string"/>
                    <s:element minOccurs="0" name="StyleSheetFile" type="s:string"/>
                    <s:element minOccurs="0" name="UserDefinedHeader" type="s:boolean"/>
                    <s:element minOccurs="0" name="CurrentDBOnTimeout" type="s:boolean"/>
                    <s:element minOccurs="0" name="WebServiceTimeout" type="s:long"/>
                    <s:element minOccurs="0" name="DisplayHTMLLogo" type="s:boolean"/>
                    <s:element minOccurs="0" name="DisplayCSSLogo" type="s:boolean"/>
                    <s:element minOccurs="0" name="DisplayRSSFeed" type="s:boolean"/>
                    <s:element minOccurs="0" name="DisplayGoogleRSSFeed" type="s:boolean"/>
                    <s:element minOccurs="0" name="BackgroundColor" type="s:string"/>
                    <s:element minOccurs="0" name="DefaultDatabase" type="s:string"/>
                    <s:element minOccurs="0" name="ReservationMessage" type="s:string"/>
                    <s:element minOccurs="0" name="HelpLoginPage" type="s:string"/>
                    <s:element minOccurs="0" name="HelpMainMenu" type="s:string"/>
                    <s:element minOccurs="0" name="HelpWebCirculation" type="s:string"/>
                    <s:element minOccurs="0" name="HelpMemberServices" type="s:string"/>
                    <s:element minOccurs="0" name="IPAddress" type="s:string"/>
                    <s:element minOccurs="0" name="LibraryName" type="s:string"/>
                    <s:element minOccurs="0" name="AllowDBSelection" type="s:string"/>
                    <s:element minOccurs="0" name="EnableInternetLinks" type="s:boolean"/>
                    <s:element minOccurs="0" name="MemberLoginMessage">
                        <s:simpleType>
                            <s:restriction base="s:string">
                                <s:maxLength value="1024"/>
                            </s:restriction>
                        </s:simpleType>
                    </s:element>
                    <s:element minOccurs="0" name="MemberLoginGreet" type="s:long"/>
                    <s:element minOccurs="0" name="NameSpace" type="s:string"/>
                    <s:element minOccurs="0" name="SecureServer" type="s:string"/>
                    <s:element minOccurs="0" name="ServerType" type="s:string"/>
                    <s:element minOccurs="0" name="WebPath" type="s:string"/>
                    <s:element minOccurs="0" name="ServicePointOnStartup" type="s:boolean"/>
                    <s:element minOccurs="0" name="UserServicePointDefault" type="s:boolean"/>
                    <s:element minOccurs="0" name="ShowServicePointHeading" type="s:boolean"/>
                    <s:element minOccurs="0" name="UserTagSeq" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispRSN" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispMABSubjectChains" type="s:boolean"/>
                    <s:element minOccurs="0" name="LinkToRelatedWorks" type="s:boolean"/>
                    <s:element minOccurs="0" name="SupNoDispItem" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispOrdOnly" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispMABHOnly" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispSeriesVol" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispLocalImages" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispLocalImagesFirst" type="s:boolean"/>
                    <s:element minOccurs="0" name="AmazonImages" type="s:long"/>
                    <s:element minOccurs="0" name="ImageBackgrounds" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispCollection" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispCallNum" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispBranch" type="s:long"/>
                    <s:element minOccurs="0" name="DispBranchGoogle" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispBranchDetailsLink" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispSelectAll" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispBiblioBasket" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispDownloadTitle" type="s:boolean"/>
                    <s:element minOccurs="0" name="DispEmailTitle" type="s:boolean"/>
                    <s:element minOccurs="0" name="OpacFormat" type="s:long"/>
                    <s:element minOccurs="0" name="CatDispLinkRelWork" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispLinkTag" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispLinkISBD" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispLinkMarcXML" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispLinkBib" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispLinkHier" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispLinkEnContent" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispLinkRequestILL" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispImagesLink" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispCopyright" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispContPrefix" type="s:string"/>
                    <s:element minOccurs="0" name="CatDispContSuffix" type="s:string"/>
                    <s:element minOccurs="0" name="CatDispContURL" type="s:string"/>
                    <s:element minOccurs="0" name="CatDispContClientCode" type="s:string"/>
                    <s:element minOccurs="0" name="CatDispContLabel" type="s:string"/>
                    <s:element minOccurs="0" name="CatDispPopup" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispThumbnails" type="s:boolean"/>
                    <s:element minOccurs="0" name="CatDispWatermark" type="s:boolean"/>
                    <s:element minOccurs="0" name="ImagesforCatDisplay" type="s:string"/>
                    <s:element minOccurs="0" name="ImagesforPicCatalogue" type="s:string"/>
                    <s:element minOccurs="0" name="ItemInfoSortBranch" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoSortBranchCol" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoHoldingsTop" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispCol" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispVol" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispBranch" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispStatus" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispBorrower" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispBorrowerLink" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispDueDate" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispCall" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispBrAt" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispBarcode" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispStatistics1" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispStatistics3" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoDispPurchaseDate" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoShowArticlePane" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoShowArticleNotes" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoShowArticleByline" type="s:boolean"/>
                    <s:element minOccurs="0" name="ItemInfoShowArticleLinkless" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembSortBOP" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembDispOnlyResPrimary" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembPermitSelfCirc" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResPermItemRes" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResPermTitleRes" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembShowCardStatus" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembManageSDI" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembNotifyAddress" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembDetails" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembEmailList" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembEnableMessages" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembDeleteMessages" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembSendILL" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembShowHistory" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembResHistory" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembChangePassword" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembStatement" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembStatementOptions" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembPermHoldDelete" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembPermRenewals" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembPermReservesDelete" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembPermReturns" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembPermDetailsUpdate" type="s:boolean"/>
                    <s:element minOccurs="0" name="EnableDirectUpdate" type="s:boolean"/>
                    <s:element minOccurs="0" name="EnableOnlineRegistration" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembIncludeAddressTemplate" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembShowRenewCondition" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResDenyReserveExpiry" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResPermTitleResOption" type="s:long"/>
                    <s:element minOccurs="0" name="MembShowTransactionCode" type="s:boolean"/>
                    <s:element minOccurs="0" name="SuppressAccStatus" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembRSSFeeds" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembTagCloud" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembTagMembServ" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembTagWebOPAC" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembTagCatalogueDisplay" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembTagLabelHistory" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembTagLabelCatalogue" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembTagLabelSearch" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembTagLabelCommon" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembLoginMessage" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink1URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink10URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink10Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink1Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink2URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink2Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink3URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink3Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink4URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink4Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink5URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink5Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink6URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink6Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink7URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink7Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink8URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink8Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink9URL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuLink9Name" type="s:string"/>
                    <s:element minOccurs="0" name="MenuOpeanSearchURL" type="s:string"/>
                    <s:element minOccurs="0" name="MenuOpeanSearchName" type="s:string"/>
                    <s:element minOccurs="0" name="ResPermBOP" type="s:boolean"/>
                    <s:element minOccurs="0" name="MembLoginAttempts" type="s:long"/>
                    <s:element minOccurs="0" name="MenuLinkExit" type="s:string"/>
                    <s:element minOccurs="0" name="ResPermStack" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResPermJournal" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResJournalDetail" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResMaximumSelf" type="s:long"/>
                    <s:element minOccurs="0" name="ResAtLocalOnly" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResTitleInStock" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResNeverToHolds" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResItemInStock" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResBranchGroupCheck" type="s:boolean"/>
                    <s:element minOccurs="0" name="AllowReservations" type="s:boolean"/>
                    <s:element minOccurs="0" name="ResMessage" type="s:string"/>
                    <s:element minOccurs="0" name="MembSuggestEmailTo" type="s:string"/>
                    <s:element minOccurs="0" name="MembSuggestEmailFrom" type="s:string"/>
                    <s:element minOccurs="0" name="SortForm" type="s:boolean"/>
                    <s:element minOccurs="0" name="TagforAbstractDisplay" type="s:string"/>
                    <s:element minOccurs="0" name="BypassToken" type="s:boolean"/>
                    <s:element minOccurs="0" name="RSNonMaxTime" type="s:string"/>
                    <s:element minOccurs="0" name="MaxTimeSearch" type="s:long"/>
                    <s:element minOccurs="0" name="MaxSearchHits" type="s:long"/>
                    <s:element minOccurs="0" name="MemberLoginMinsUntilTimeout" type="s:long"/>
                    <s:element minOccurs="0" name="MaxTimeOut" type="s:long"/>
                    <s:element minOccurs="0" name="MembGlobal" type="s:boolean"/>
                    <s:element minOccurs="0" name="ScriptingHooks" type="s:boolean"/>
                    <s:element minOccurs="0" name="eMatLink" type="s:string"/>
                    <s:element minOccurs="0" name="eMatLabel" type="s:string"/>
                    <s:element minOccurs="0" name="eMatLabelTxt" type="s:string"/>
                    <s:element minOccurs="0" name="coverAmazon" type="s:boolean"/>
                    <s:element minOccurs="0" name="coverGoogleBooks" type="s:boolean"/>
                    <s:element minOccurs="0" name="coverOpenLibrary" type="s:boolean"/>
                    <s:element minOccurs="0" name="coverNLA" type="s:boolean"/>
                    <s:element minOccurs="0" name="coverVLB" type="s:boolean"/>
                    <s:element minOccurs="0" name="coverVLBtoken" type="s:string"/>
                    <s:element minOccurs="0" name="coverWheelers" type="s:boolean"/>
                    <s:element minOccurs="0" name="coverMusicBrainz" type="s:boolean"/>
                    <s:element minOccurs="0" name="coverTraktTV" type="s:boolean"/>
                    <s:element minOccurs="0" name="coverWheelersFor" type="s:string"/>
                    <s:element minOccurs="0" name="coverMusicBrainzFor" type="s:string"/>
                    <s:element minOccurs="0" name="coverTraktTVFor" type="s:string"/>
                    <s:element minOccurs="0" name="coverTraktTVKey" type="s:string"/>
                    <s:element minOccurs="0" name="coverSizeTable" type="s:long"/>
                    <s:element minOccurs="0" name="coverSizeDisplay" type="s:long"/>
                </s:sequence>
            </s:complexType>
            <s:element name="WebTransactionAdd">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="TOKEN" type="s:string"/>
                        <s:element minOccurs="0" name="detail" type="s0:WebTransaction"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="WebTransaction">
                <s:sequence>
                    <s:element minOccurs="0" name="BorrowerNumber" type="s:string"/>
                    <s:element minOccurs="0" name="TransUsername" type="s:string"/>
                    <s:element minOccurs="0" name="TransPassword" type="s:string"/>
                    <s:element minOccurs="0" name="Action" type="s:string"/>
                    <s:element minOccurs="0" name="TransAmount" type="s:string"/>
                    <s:element minOccurs="0" name="TransDescription" type="s:string"/>
                    <s:element minOccurs="0" name="TransBarcode" type="s:string"/>
                    <s:element minOccurs="0" name="TransTypeCode" type="s:string"/>
                    <s:element minOccurs="0" name="TransReferenceId" type="s:string"/>
                    <s:element minOccurs="0" name="TransPayment" type="s:string"/>
                    <s:element minOccurs="0" name="TransPaymentType" type="s:string"/>
                    <s:element minOccurs="0" name="TransDatabase" type="s:string"/>
                    <s:element minOccurs="0" name="LiberoUser" type="s:string"/>
                    <s:element minOccurs="0" name="TransBranch" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:element name="WebTransactionAddResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="WebTransactionAddResult" type="s0:Message"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
        </s:schema>
    </types>
    <message name="AddArticleSoapIn">
        <part name="parameters" element="s0:AddArticle"/>
    </message>
    <message name="AddArticleSoapOut">
        <part name="parameters" element="s0:AddArticleResponse"/>
    </message>
    <message name="AddHoldingSoapIn">
        <part name="parameters" element="s0:AddHolding"/>
    </message>
    <message name="AddHoldingSoapOut">
        <part name="parameters" element="s0:AddHoldingResponse"/>
    </message>
    <message name="AuditLogSoapIn">
        <part name="parameters" element="s0:AuditLog"/>
    </message>
    <message name="AuditLogSoapOut">
        <part name="parameters" element="s0:AuditLogResponse"/>
    </message>
    <message name="BranchSoapIn">
        <part name="parameters" element="s0:Branch"/>
    </message>
    <message name="BranchSoapOut">
        <part name="parameters" element="s0:BranchResponse"/>
    </message>
    <message name="BudgetSoapIn">
        <part name="parameters" element="s0:Budget"/>
    </message>
    <message name="BudgetSoapOut">
        <part name="parameters" element="s0:BudgetResponse"/>
    </message>
    <message name="CheckInSoapIn">
        <part name="parameters" element="s0:CheckIn"/>
    </message>
    <message name="CheckInSoapOut">
        <part name="parameters" element="s0:CheckInResponse"/>
    </message>
    <message name="CheckInIssuesSoapIn">
        <part name="parameters" element="s0:CheckInIssues"/>
    </message>
    <message name="CheckInIssuesSoapOut">
        <part name="parameters" element="s0:CheckInIssuesResponse"/>
    </message>
    <message name="CheckOutSoapIn">
        <part name="parameters" element="s0:CheckOut"/>
    </message>
    <message name="CheckOutSoapOut">
        <part name="parameters" element="s0:CheckOutResponse"/>
    </message>
    <message name="DeleteReservationSoapIn">
        <part name="parameters" element="s0:DeleteReservation"/>
    </message>
    <message name="DeleteReservationSoapOut">
        <part name="parameters" element="s0:DeleteReservationResponse"/>
    </message>
    <message name="GetItemChangeListSoapIn">
        <part name="parameters" element="s0:GetItemChangeList"/>
    </message>
    <message name="GetItemChangeListSoapOut">
        <part name="parameters" element="s0:GetItemChangeListResponse"/>
    </message>
    <message name="GetItemDetailsSoapIn">
        <part name="parameters" element="s0:GetItemDetails"/>
    </message>
    <message name="GetItemDetailsSoapOut">
        <part name="parameters" element="s0:GetItemDetailsResponse"/>
    </message>
    <message name="GetItemsDueSoapIn">
        <part name="parameters" element="s0:GetItemsDue"/>
    </message>
    <message name="GetItemsDueSoapOut">
        <part name="parameters" element="s0:GetItemsDueResponse"/>
    </message>
    <message name="GetMemberChangeListSoapIn">
        <part name="parameters" element="s0:GetMemberChangeList"/>
    </message>
    <message name="GetMemberChangeListSoapOut">
        <part name="parameters" element="s0:GetMemberChangeListResponse"/>
    </message>
    <message name="GetMemberDetailsSoapIn">
        <part name="parameters" element="s0:GetMemberDetails"/>
    </message>
    <message name="GetMemberDetailsSoapOut">
        <part name="parameters" element="s0:GetMemberDetailsResponse"/>
    </message>
    <message name="GetMemberOutstandingSoapIn">
        <part name="parameters" element="s0:GetMemberOutstanding"/>
    </message>
    <message name="GetMemberOutstandingSoapOut">
        <part name="parameters" element="s0:GetMemberOutstandingResponse"/>
    </message>
    <message name="GetMemberStatementSoapIn">
        <part name="parameters" element="s0:GetMemberStatement"/>
    </message>
    <message name="GetMemberStatementSoapOut">
        <part name="parameters" element="s0:GetMemberStatementResponse"/>
    </message>
    <message name="GetMemberTransactionsSoapIn">
        <part name="parameters" element="s0:GetMemberTransactions"/>
    </message>
    <message name="GetMemberTransactionsSoapOut">
        <part name="parameters" element="s0:GetMemberTransactionsResponse"/>
    </message>
    <message name="GetSerialSubscriptionSoapIn">
        <part name="parameters" element="s0:GetSerialSubscription"/>
    </message>
    <message name="GetSerialSubscriptionSoapOut">
        <part name="parameters" element="s0:GetSerialSubscriptionResponse"/>
    </message>
    <message name="GetTitleDetailsSoapIn">
        <part name="parameters" element="s0:GetTitleDetails"/>
    </message>
    <message name="GetTitleDetailsSoapOut">
        <part name="parameters" element="s0:GetTitleDetailsResponse"/>
    </message>
    <message name="InvoiceSoapIn">
        <part name="parameters" element="s0:Invoice"/>
    </message>
    <message name="InvoiceSoapOut">
        <part name="parameters" element="s0:InvoiceResponse"/>
    </message>
    <message name="InvoiceEntrySoapIn">
        <part name="parameters" element="s0:InvoiceEntry"/>
    </message>
    <message name="InvoiceEntrySoapOut">
        <part name="parameters" element="s0:InvoiceEntryResponse"/>
    </message>
    <message name="ItemStatusUpdateSoapIn">
        <part name="parameters" element="s0:ItemStatusUpdate"/>
    </message>
    <message name="ItemStatusUpdateSoapOut">
        <part name="parameters" element="s0:ItemStatusUpdateResponse"/>
    </message>
    <message name="LoanHistorySoapIn">
        <part name="parameters" element="s0:LoanHistory"/>
    </message>
    <message name="LoanHistorySoapOut">
        <part name="parameters" element="s0:LoanHistoryResponse"/>
    </message>
    <message name="OrderEntrySoapIn">
        <part name="parameters" element="s0:OrderEntry"/>
    </message>
    <message name="OrderEntrySoapOut">
        <part name="parameters" element="s0:OrderEntryResponse"/>
    </message>
    <message name="OrderInformationSoapIn">
        <part name="parameters" element="s0:OrderInformation"/>
    </message>
    <message name="OrderInformationSoapOut">
        <part name="parameters" element="s0:OrderInformationResponse"/>
    </message>
    <message name="OrderLineInformationSoapIn">
        <part name="parameters" element="s0:OrderLineInformation"/>
    </message>
    <message name="OrderLineInformationSoapOut">
        <part name="parameters" element="s0:OrderLineInformationResponse"/>
    </message>
    <message name="OrderStatusSoapIn">
        <part name="parameters" element="s0:OrderStatus"/>
    </message>
    <message name="OrderStatusSoapOut">
        <part name="parameters" element="s0:OrderStatusResponse"/>
    </message>
    <message name="PayAnyFeeSoapIn">
        <part name="parameters" element="s0:PayAnyFee"/>
    </message>
    <message name="PayAnyFeeSoapOut">
        <part name="parameters" element="s0:PayAnyFeeResponse"/>
    </message>
    <message name="PayFeeSoapIn">
        <part name="parameters" element="s0:PayFee"/>
    </message>
    <message name="PayFeeSoapOut">
        <part name="parameters" element="s0:PayFeeResponse"/>
    </message>
    <message name="PurgeItemChangeListSoapIn">
        <part name="parameters" element="s0:PurgeItemChangeList"/>
    </message>
    <message name="PurgeItemChangeListSoapOut">
        <part name="parameters" element="s0:PurgeItemChangeListResponse"/>
    </message>
    <message name="PurgeItemChangeListUptoSoapIn">
        <part name="parameters" element="s0:PurgeItemChangeListUpto"/>
    </message>
    <message name="PurgeItemChangeListUptoSoapOut">
        <part name="parameters" element="s0:PurgeItemChangeListUptoResponse"/>
    </message>
    <message name="PurgeMemberChangeListSoapIn">
        <part name="parameters" element="s0:PurgeMemberChangeList"/>
    </message>
    <message name="PurgeMemberChangeListSoapOut">
        <part name="parameters" element="s0:PurgeMemberChangeListResponse"/>
    </message>
    <message name="PurgeMemberChangeListUptoSoapIn">
        <part name="parameters" element="s0:PurgeMemberChangeListUpto"/>
    </message>
    <message name="PurgeMemberChangeListUptoSoapOut">
        <part name="parameters" element="s0:PurgeMemberChangeListUptoResponse"/>
    </message>
    <message name="RaiseChargeSoapIn">
        <part name="parameters" element="s0:RaiseCharge"/>
    </message>
    <message name="RaiseChargeSoapOut">
        <part name="parameters" element="s0:RaiseChargeResponse"/>
    </message>
    <message name="RecordEntrySoapIn">
        <part name="parameters" element="s0:RecordEntry"/>
    </message>
    <message name="RecordEntrySoapOut">
        <part name="parameters" element="s0:RecordEntryResponse"/>
    </message>
    <message name="RenewItemSoapIn">
        <part name="parameters" element="s0:RenewItem"/>
    </message>
    <message name="RenewItemSoapOut">
        <part name="parameters" element="s0:RenewItemResponse"/>
    </message>
    <message name="ReserveHistorySoapIn">
        <part name="parameters" element="s0:ReserveHistory"/>
    </message>
    <message name="ReserveHistorySoapOut">
        <part name="parameters" element="s0:ReserveHistoryResponse"/>
    </message>
    <message name="ReserveItemSoapIn">
        <part name="parameters" element="s0:ReserveItem"/>
    </message>
    <message name="ReserveItemSoapOut">
        <part name="parameters" element="s0:ReserveItemResponse"/>
    </message>
    <message name="SerialSubscriptionsSoapIn">
        <part name="parameters" element="s0:SerialSubscriptions"/>
    </message>
    <message name="SerialSubscriptionsSoapOut">
        <part name="parameters" element="s0:SerialSubscriptionsResponse"/>
    </message>
    <message name="SerialSubscriptionsForRSNSoapIn">
        <part name="parameters" element="s0:SerialSubscriptionsForRSN"/>
    </message>
    <message name="SerialSubscriptionsForRSNSoapOut">
        <part name="parameters" element="s0:SerialSubscriptionsForRSNResponse"/>
    </message>
    <message name="SetMemberDetailSoapIn">
        <part name="parameters" element="s0:SetMemberDetail"/>
    </message>
    <message name="SetMemberDetailSoapOut">
        <part name="parameters" element="s0:SetMemberDetailResponse"/>
    </message>
    <message name="SupplierSoapIn">
        <part name="parameters" element="s0:Supplier"/>
    </message>
    <message name="SupplierSoapOut">
        <part name="parameters" element="s0:SupplierResponse"/>
    </message>
    <message name="UpdateBranchSoapIn">
        <part name="parameters" element="s0:UpdateBranch"/>
    </message>
    <message name="UpdateBranchSoapOut">
        <part name="parameters" element="s0:UpdateBranchResponse"/>
    </message>
    <message name="UpdateMemberSoapIn">
        <part name="parameters" element="s0:UpdateMember"/>
    </message>
    <message name="UpdateMemberSoapOut">
        <part name="parameters" element="s0:UpdateMemberResponse"/>
    </message>
    <message name="UpdateSerialSubscriptionSoapIn">
        <part name="parameters" element="s0:UpdateSerialSubscription"/>
    </message>
    <message name="UpdateSerialSubscriptionSoapOut">
        <part name="parameters" element="s0:UpdateSerialSubscriptionResponse"/>
    </message>
    <message name="WebOpacTailoringParametersSoapIn">
        <part name="parameters" element="s0:WebOpacTailoringParameters"/>
    </message>
    <message name="WebOpacTailoringParametersSoapOut">
        <part name="parameters" element="s0:WebOpacTailoringParametersResponse"/>
    </message>
    <message name="WebTransactionAddSoapIn">
        <part name="parameters" element="s0:WebTransactionAdd"/>
    </message>
    <message name="WebTransactionAddSoapOut">
        <part name="parameters" element="s0:WebTransactionAddResponse"/>
    </message>
    <portType name="LibraryAPISoap">
        <operation name="AddArticle">
            <input message="s0:AddArticleSoapIn"/>
            <output message="s0:AddArticleSoapOut"/>
        </operation>
        <operation name="AddHolding">
            <input message="s0:AddHoldingSoapIn"/>
            <output message="s0:AddHoldingSoapOut"/>
        </operation>
        <operation name="AuditLog">
            <input message="s0:AuditLogSoapIn"/>
            <output message="s0:AuditLogSoapOut"/>
        </operation>
        <operation name="Branch">
            <input message="s0:BranchSoapIn"/>
            <output message="s0:BranchSoapOut"/>
        </operation>
        <operation name="Budget">
            <input message="s0:BudgetSoapIn"/>
            <output message="s0:BudgetSoapOut"/>
        </operation>
        <operation name="CheckIn">
            <input message="s0:CheckInSoapIn"/>
            <output message="s0:CheckInSoapOut"/>
        </operation>
        <operation name="CheckInIssues">
            <input message="s0:CheckInIssuesSoapIn"/>
            <output message="s0:CheckInIssuesSoapOut"/>
        </operation>
        <operation name="CheckOut">
            <input message="s0:CheckOutSoapIn"/>
            <output message="s0:CheckOutSoapOut"/>
        </operation>
        <operation name="DeleteReservation">
            <input message="s0:DeleteReservationSoapIn"/>
            <output message="s0:DeleteReservationSoapOut"/>
        </operation>
        <operation name="GetItemChangeList">
            <input message="s0:GetItemChangeListSoapIn"/>
            <output message="s0:GetItemChangeListSoapOut"/>
        </operation>
        <operation name="GetItemDetails">
            <input message="s0:GetItemDetailsSoapIn"/>
            <output message="s0:GetItemDetailsSoapOut"/>
        </operation>
        <operation name="GetItemsDue">
            <input message="s0:GetItemsDueSoapIn"/>
            <output message="s0:GetItemsDueSoapOut"/>
        </operation>
        <operation name="GetMemberChangeList">
            <input message="s0:GetMemberChangeListSoapIn"/>
            <output message="s0:GetMemberChangeListSoapOut"/>
        </operation>
        <operation name="GetMemberDetails">
            <input message="s0:GetMemberDetailsSoapIn"/>
            <output message="s0:GetMemberDetailsSoapOut"/>
        </operation>
        <operation name="GetMemberOutstanding">
            <input message="s0:GetMemberOutstandingSoapIn"/>
            <output message="s0:GetMemberOutstandingSoapOut"/>
        </operation>
        <operation name="GetMemberStatement">
            <input message="s0:GetMemberStatementSoapIn"/>
            <output message="s0:GetMemberStatementSoapOut"/>
        </operation>
        <operation name="GetMemberTransactions">
            <input message="s0:GetMemberTransactionsSoapIn"/>
            <output message="s0:GetMemberTransactionsSoapOut"/>
        </operation>
        <operation name="GetSerialSubscription">
            <input message="s0:GetSerialSubscriptionSoapIn"/>
            <output message="s0:GetSerialSubscriptionSoapOut"/>
        </operation>
        <operation name="GetTitleDetails">
            <input message="s0:GetTitleDetailsSoapIn"/>
            <output message="s0:GetTitleDetailsSoapOut"/>
        </operation>
        <operation name="Invoice">
            <input message="s0:InvoiceSoapIn"/>
            <output message="s0:InvoiceSoapOut"/>
        </operation>
        <operation name="InvoiceEntry">
            <input message="s0:InvoiceEntrySoapIn"/>
            <output message="s0:InvoiceEntrySoapOut"/>
        </operation>
        <operation name="ItemStatusUpdate">
            <input message="s0:ItemStatusUpdateSoapIn"/>
            <output message="s0:ItemStatusUpdateSoapOut"/>
        </operation>
        <operation name="LoanHistory">
            <input message="s0:LoanHistorySoapIn"/>
            <output message="s0:LoanHistorySoapOut"/>
        </operation>
        <operation name="OrderEntry">
            <input message="s0:OrderEntrySoapIn"/>
            <output message="s0:OrderEntrySoapOut"/>
        </operation>
        <operation name="OrderInformation">
            <input message="s0:OrderInformationSoapIn"/>
            <output message="s0:OrderInformationSoapOut"/>
        </operation>
        <operation name="OrderLineInformation">
            <input message="s0:OrderLineInformationSoapIn"/>
            <output message="s0:OrderLineInformationSoapOut"/>
        </operation>
        <operation name="OrderStatus">
            <input message="s0:OrderStatusSoapIn"/>
            <output message="s0:OrderStatusSoapOut"/>
        </operation>
        <operation name="PayAnyFee">
            <input message="s0:PayAnyFeeSoapIn"/>
            <output message="s0:PayAnyFeeSoapOut"/>
        </operation>
        <operation name="PayFee">
            <input message="s0:PayFeeSoapIn"/>
            <output message="s0:PayFeeSoapOut"/>
        </operation>
        <operation name="PurgeItemChangeList">
            <input message="s0:PurgeItemChangeListSoapIn"/>
            <output message="s0:PurgeItemChangeListSoapOut"/>
        </operation>
        <operation name="PurgeItemChangeListUpto">
            <input message="s0:PurgeItemChangeListUptoSoapIn"/>
            <output message="s0:PurgeItemChangeListUptoSoapOut"/>
        </operation>
        <operation name="PurgeMemberChangeList">
            <input message="s0:PurgeMemberChangeListSoapIn"/>
            <output message="s0:PurgeMemberChangeListSoapOut"/>
        </operation>
        <operation name="PurgeMemberChangeListUpto">
            <input message="s0:PurgeMemberChangeListUptoSoapIn"/>
            <output message="s0:PurgeMemberChangeListUptoSoapOut"/>
        </operation>
        <operation name="RaiseCharge">
            <input message="s0:RaiseChargeSoapIn"/>
            <output message="s0:RaiseChargeSoapOut"/>
        </operation>
        <operation name="RecordEntry">
            <input message="s0:RecordEntrySoapIn"/>
            <output message="s0:RecordEntrySoapOut"/>
        </operation>
        <operation name="RenewItem">
            <input message="s0:RenewItemSoapIn"/>
            <output message="s0:RenewItemSoapOut"/>
        </operation>
        <operation name="ReserveHistory">
            <input message="s0:ReserveHistorySoapIn"/>
            <output message="s0:ReserveHistorySoapOut"/>
        </operation>
        <operation name="ReserveItem">
            <input message="s0:ReserveItemSoapIn"/>
            <output message="s0:ReserveItemSoapOut"/>
        </operation>
        <operation name="SerialSubscriptions">
            <input message="s0:SerialSubscriptionsSoapIn"/>
            <output message="s0:SerialSubscriptionsSoapOut"/>
        </operation>
        <operation name="SerialSubscriptionsForRSN">
            <input message="s0:SerialSubscriptionsForRSNSoapIn"/>
            <output message="s0:SerialSubscriptionsForRSNSoapOut"/>
        </operation>
        <operation name="SetMemberDetail">
            <input message="s0:SetMemberDetailSoapIn"/>
            <output message="s0:SetMemberDetailSoapOut"/>
        </operation>
        <operation name="Supplier">
            <input message="s0:SupplierSoapIn"/>
            <output message="s0:SupplierSoapOut"/>
        </operation>
        <operation name="UpdateBranch">
            <input message="s0:UpdateBranchSoapIn"/>
            <output message="s0:UpdateBranchSoapOut"/>
        </operation>
        <operation name="UpdateMember">
            <input message="s0:UpdateMemberSoapIn"/>
            <output message="s0:UpdateMemberSoapOut"/>
        </operation>
        <operation name="UpdateSerialSubscription">
            <input message="s0:UpdateSerialSubscriptionSoapIn"/>
            <output message="s0:UpdateSerialSubscriptionSoapOut"/>
        </operation>
        <operation name="WebOpacTailoringParameters">
            <input message="s0:WebOpacTailoringParametersSoapIn"/>
            <output message="s0:WebOpacTailoringParametersSoapOut"/>
        </operation>
        <operation name="WebTransactionAdd">
            <input message="s0:WebTransactionAddSoapIn"/>
            <output message="s0:WebTransactionAddSoapOut"/>
        </operation>
    </portType>
    <binding name="LibraryAPISoap" type="s0:LibraryAPISoap">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
        <operation name="AddArticle">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.AddArticle" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="AddHolding">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.AddHolding" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="AuditLog">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.AuditLog" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="Branch">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.Branch" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="Budget">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.Budget" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="CheckIn">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.CheckIn" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="CheckInIssues">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.CheckInIssues" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="CheckOut">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.CheckOut" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="DeleteReservation">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.DeleteReservation" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetItemChangeList">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetItemChangeList" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetItemDetails">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetItemDetails" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetItemsDue">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetItemsDue" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberChangeList">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetMemberChangeList" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberDetails">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetMemberDetails" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberOutstanding">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetMemberOutstanding" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberStatement">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetMemberStatement" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberTransactions">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetMemberTransactions" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetSerialSubscription">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetSerialSubscription" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetTitleDetails">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.GetTitleDetails" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="Invoice">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.Invoice" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="InvoiceEntry">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.InvoiceEntry" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="ItemStatusUpdate">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.ItemStatusUpdate" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="LoanHistory">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.LoanHistory" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="OrderEntry">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.OrderEntry" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="OrderInformation">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.OrderInformation" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="OrderLineInformation">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.OrderLineInformation" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="OrderStatus">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.OrderStatus" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="PayAnyFee">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.PayAnyFee" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="PayFee">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.PayFee" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="PurgeItemChangeList">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.PurgeItemChangeList" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="PurgeItemChangeListUpto">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.PurgeItemChangeListUpto" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="PurgeMemberChangeList">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.PurgeMemberChangeList" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="PurgeMemberChangeListUpto">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.PurgeMemberChangeListUpto" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="RaiseCharge">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.RaiseCharge" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="RecordEntry">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.RecordEntry" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="RenewItem">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.RenewItem" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="ReserveHistory">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.ReserveHistory" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="ReserveItem">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.ReserveItem" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="SerialSubscriptions">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.SerialSubscriptions" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="SerialSubscriptionsForRSN">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.SerialSubscriptionsForRSN" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="SetMemberDetail">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.SetMemberDetail" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="Supplier">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.Supplier" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="UpdateBranch">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.UpdateBranch" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="UpdateMember">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.UpdateMember" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="UpdateSerialSubscription">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.UpdateSerialSubscription" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="WebOpacTailoringParameters">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.WebOpacTailoringParameters" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="WebTransactionAdd">
            <soap:operation soapAction="http://libero.com.au/LiberoWebServices.LibraryAPI.WebTransactionAdd" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>
    <service name="LibraryAPI">
        <port name="LibraryAPISoap" binding="s0:LibraryAPISoap">
            <soap:address location="http://eurobodalla.libero.com.au/libero/LiberoWebServices.LibraryAPI.cls"/>
        </port>
    </service>
</definitions>