server.servlet.context-path=/lucy

management.endpoints.web.exposure.include=*

spring.application.name=lucy-api

spring.jpa.open-in-view=false

# k8s Environment
work-orders-service.url=k8s-prod.peterpal.com.au/work-orders
customer-invoices-service.url=k8s-prod.peterpal.com.au/customer-invoices
k8s-username=jira
k8s-password=peterpaljira

# Lucy API
lucyapi.datasource.platform=postgres
lucyapi.datasource.url=*****************************************************
lucyapi.datasource.username=lucy
lucyapi.datasource.password=compact
lucyapi.datasource.hikari.connection-test-query="SELECT 1"
lucyapi.datasource.driverClassName=org.postgresql.Driver
lucyapi.jpa.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
lucyapi.jpa.hibernate.ddl-auto=validate

# Lucy Cataloguing
cataloguing.datasource.platform=postgres
cataloguing.datasource.url=*************************************************************
cataloguing.datasource.username=lucy
cataloguing.datasource.password=compact
cataloguing.datasource.hikari.connection-test-query="SELECT 1"
cataloguing.datasource.driverClassName=org.postgresql.Driver
cataloguing.jpa.hibernate.ddl-auto=none
cataloguing.jpa.hibernate.dialect=org.hibernate.dialect.SQLServerDialect

# Flyway
spring.flyway.enabled=true
spring.flyway.locations=classpath:/db/migration/lucyapi/
spring.flyway.user=lucy
spring.flyway.password=compact
spring.flyway.url=*****************************************************

# UAT Feeds for Lucy 4 Java EE 7 EJBs
# See also jboss-ejb-client-test.properties
naming.url=http-remoting://cw-prod-feeds:9580

# Artemis
spring.artemis.mode=native
spring.artemis.host=artemis
spring.artemis.port=61616
spring.artemis.user=artemis
spring.artemis.password=simetraehcapa

# Lucy 4
spring.datasource.platform=sqlserver
spring.datasource.url=*************************************************
spring.datasource.username=lucy
spring.datasource.password=compact
spring.jpa.hibernate.dialect=org.hibernate.dialect.SQLServerDialect
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.driverClassName=net.sourceforge.jtds.jdbc.Driver
spring.jpa.hibernate.ddl-auto = none

## MULTIPART properties
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=800MB
spring.servlet.multipart.max-request-size=215MB

# All batch file exports will be stored in this directory
file.export-dir=storage

# Keycloak properties
keycloak.realm = ppls-dev
keycloak.resource = lucy-api
keycloak.auth-server-url = http://keycloak.k8s-prod.peterpal.local/auth
keycloak.ssl-required = external
keycloak.bearer-only = true
keycloak.cors = true
keycloak.credentials.secret = 8c03fa97-4eb9-419f-a7af-a34f0ad01b71
ppls.keycloak.client-id=lucy-ui

# Switch on/off security
security.enabled=false

# Suppress Spring Sleuth Header messages in logs
logging.level.org.springframework.integration.jms.DefaultJmsHeaderMapper=ERROR

retry.spydus-edi.maxAttempts=10
retry.spydus-edi.maxDelay=60000

customer-invoices-edi-service.url=http://k8s-prod.peterpal.com.au/customer-invoices-edi
customer-order-responses-edi-service.url=http://k8s-prod.peterpal.com.au/customer-order-responses-edi
invoice-spydus-edi.url=http://spydus.peterpal.local
response-spydus-edi.url=http://spydus.peterpal.local

jira.invoice-spydus-edi.webhook.url=https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf2-9041-7d91-8bad-615539844970
jira.invoice-spydus-edi.webhook.secret=2c240873b06ce9ba4cc96bce4276355bf8eac9fa
jira.webhook.url=https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018bd076-11db-7dc8-bc84-f83f0354219c
jira.webhook.secret=be28d1ef0b2d205ab0d02b216c74fdf387dc75c4
jira.deleteOnOrder.webhook.url=https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c192f-563e-7213-b08e-c12f608e2058
jira.deleteOnOrder.webhook.secret=824e49236c74cd904ea7daf8553c738d7ad6fdbb
jira.response-spydus-edi.webhook.url=https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf3-085e-7108-a840-b6af2e0b58b8
jira.response-spydus-edi.webhook.secret=633c677e553f1b153b02b5785957b60218eeb5fb
jira.check-work-order-and-barcode.webhook.url=https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf0-a872-7f1d-9353-fd06e8ee6221
jira.check-work-order-and-barcode.webhook.secret=7c71555f67fbacda43e8c90822432c117aa44c4e
jira.customer-invoice-edi-retrieval.webhook.url=https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf1-8413-7c7d-8e6d-9c4d3de9f164
jira.customer-invoice-edi-retrieval.webhook.secret=ea0ba31eb4edad0aaa427065689523b76d5a02c7
jira.initialization-orchestration.webhook.url=https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf0-a872-7f1d-9353-fd06e8ee6221}
jira.initialization-orchestration.webhook.secret=7c71555f67fbacda43e8c90822432c117aa44c4e
spydus.irn.date-formats=d/M/yyyy,yyyy-M-d
marc.barcode-line-prefix=995,949,945
