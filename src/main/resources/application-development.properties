logging.level.au.com.peterpal.lucy.api.cataloguing=DEBUG

management.endpoints.web.exposure.include=*

spring.application.name=lucy-api

# UAT Feeds for Lucy 4 Java EE 7 EJBs
# See also jboss-ejb-client-test.properties
naming.url=http-remoting://cw-uat-feeds:9580

# k8s Test Environment
work-orders-service.url=k8s-test.peterpal.com.au/work-orders
customer-invoices-service.url=k8s-test.peterpal.com.au/customer-invoices
k8s-username=developer
k8s-password=developer

# Local Artemis
spring.artemis.host=artemis
spring.artemis.port=61616
spring.artemis.user=artemis
spring.artemis.password=simetraehcapa

# Lucy Cataloguing UAT
cataloguing.datasource.platform=sqlserver
cataloguing.datasource.url=******************************************************************
cataloguing.datasource.username=lucy
cataloguing.datasource.password=compact
cataloguing.datasource.hikari.connection-test-query="SELECT 1"
cataloguing.datasource.driverClassName=net.sourceforge.jtds.jdbc.Driver
cataloguing.jpa.hibernate.ddl-auto=none
cataloguing.jpa.hibernate.dialect=org.hibernate.dialect.SQLServerDialect

# Lucy 4 Clarke
spring.datasource.platform=sqlserver
spring.datasource.url=********************************************************
spring.datasource.username=lucy
spring.datasource.password=compact
spring.jpa.hibernate.dialect=org.hibernate.dialect.SQLServerDialect

## MULTIPART properties
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=800MB
spring.servlet.multipart.max-request-size=215MB

# All batch file exports will be stored in this directory
file.export-dir=storage

# Keycloak properties
keycloak.realm = ppls-dev
keycloak.resource = lucy-api
keycloak.auth-server-url = http://keycloak.k8s-test.peterpal.local/auth
keycloak.ssl-required = external
keycloak.bearer-only = true
keycloak.cors = true
keycloak.credentials.secret = d9d31185-7021-486c-a732-59da0c544061
ppls.keycloak.client-id=lucy-ui

# Switch on/off security
security.enabled=false

# Suppress Spring Sleuth Header messages in logs
logging.level.org.springframework.integration.jms.DefaultJmsHeaderMapper=ERROR

customer-invoices-edi-service.url=http://k8s-test.peterpal.com.au/customer-invoices-edi
customer-order-responses-edi-service.url=http://k8s-test.peterpal.com.au/customer-order-responses-edi
invoice-spydus-edi.url=http://spydus-test.peterpal.local
response-spydus-edi.url=http://spydus-test.peterpal.local

spydus-edi-orchestrator.skip-spydus-edi-step-in-test-env=true
