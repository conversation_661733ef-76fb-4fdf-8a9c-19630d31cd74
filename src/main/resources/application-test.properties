# Same as setup for internal-services-test
management.endpoints.web.exposure.include=*

spring.application.name=lucy-api

# UAT Feeds for Lucy 4 Java EE 7 EJBs
# See also jboss-ejb-client-test.properties
naming.url=http-remoting://cw-uat-feeds.peterpal.local:9580

# k8s Test Environment
work-orders-service.url=k8s-test.peterpal.local/work-orders
customer-invoices-service.url=k8s-test.peterpal.local/customer-invoices
k8s-username=developer
k8s-password=developer

# JPA DEBUG
logging.level.com.querydsl.sql=DEBUG
logging.level.com.querydsl=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Lucy API UAT
#lucyapi.datasource.platform=postgres
#lucyapi.datasource.url=****************************************************
#lucyapi.datasource.username=lucy
#lucyapi.datasource.password=compact
#lucyapi.datasource.hikari.connection-test-query="select 1"
#lucyapi.datasource.driverClassName=org.postgresql.Driver
#lucyapi.jpa.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
#lucyapi.jpa.hibernate.ddl-auto=validate
#upload-bib-records.schedule.cron=*/15 * * * * ?
lucyapi.datasource.platform=postgres
#lucyapi.datasource.url=****************************************************
#lucyapi.datasource.username=lucy
#lucyapi.datasource.password=compact
lucyapi.datasource.url=****************************************
lucyapi.datasource.username=postgres
lucyapi.datasource.password=postgres
lucyapi.datasource.hikari.connection-test-query="select 1"
lucyapi.datasource.driverClassName=org.postgresql.Driver
lucyapi.jpa.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
lucyapi.jpa.hibernate.ddl-auto=validate

# Lucy Cataloguing UAT
cataloguing.datasource.platform=postgres
cataloguing.datasource.url=jdbc:postgresql://************:5433/lucycataloguing
cataloguing.datasource.username=lucy
cataloguing.datasource.password=compact
cataloguing.datasource.hikari.connection-test-query="SELECT 1"
cataloguing.datasource.driverClassName=org.postgresql.Driver
cataloguing.jpa.hibernate.ddl-auto=none
cataloguing.jpa.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
# Flyway
spring.flyway.enabled=true
spring.flyway.locations=classpath:/db/migration/lucyapi/
spring.flyway.user=postgres
spring.flyway.password=postgres
spring.flyway.url=****************************************

# Local Artemis
spring.artemis.host=localhost
spring.artemis.port=61616
spring.artemis.user=artemis
spring.artemis.password=simetraehcapa

# Lucy 4 Clarke
spring.datasource.platform=sqlserver
spring.datasource.url=********************************************************
spring.datasource.username=lucy
spring.datasource.password=compact
spring.jpa.hibernate.dialect=org.hibernate.dialect.SQLServerDialect

## MULTIPART properties
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=800MB
spring.servlet.multipart.max-request-size=215MB

# All batch file exports will be stored in this directory
file.export-dir=storage

# Switch on/off security
security.enabled=true

# Keycloak properties
keycloak.realm = ppls-dev
keycloak.resource = lucy-api
keycloak.auth-server-url = http://keycloak.k8s-test.peterpal.local/auth
keycloak.ssl-required = external
keycloak.bearer-only = true
keycloak.cors = true
keycloak.credentials.secret = d9d31185-7021-486c-a732-59da0c544061
ppls.keycloak.client-id=lucy-ui

# Suppress Spring Sleuth Header messages in logs
logging.level.org.springframework.integration.jms.DefaultJmsHeaderMapper=ERROR

customer-invoices-edi-service.url=http://k8s-test.peterpal.local/customer-invoices-edi
customer-order-responses-edi-service.url=http://k8s-test.peterpal.local/customer-order-responses-edi
invoice-spydus-edi.url=http://spydus-test.peterpal.local
response-spydus-edi.url=http://spydus-test.peterpal.local

spydus-edi-orchestrator.skip-spydus-edi-step-in-test-env=true
#check-invoice-posted.schedule.cron= */1 * * * * ?
