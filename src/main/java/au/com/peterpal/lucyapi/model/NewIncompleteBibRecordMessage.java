package au.com.peterpal.lucyapi.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class NewIncompleteBibRecordMessage {
    @NotNull
    private String previousWorkOrderNumber;
    @NotNull
    private String previousOrderNumber;
    @NotNull
    private String fundCode;
}
