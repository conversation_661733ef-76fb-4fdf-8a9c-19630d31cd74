package au.com.peterpal.lucyapi.model;

import lombok.Getter;

@Getter
public enum CataloguingState {
  NEW("NEW", "New", "Initial state"),
  RETRIEVED("RETRIEVED", "Retrieved", "The record has been retrieved"),
  OOR("OOR", "OOR", "New on-order record"),
  ITEM("ITEM", "Add Item", "Add an on-order item to an existing record"),
  ISBN("ISBN", "Add ISBN", "Add ISBN to an existing record"),
  CHECK("CHECK", "Check", "Record needs extra check"),
  EXPORTED("EXPORTED", "Exported", "Record is exported"),
  FINAL("FINAL", "Final", "End state of on-order record");

  private final String code;
  private final String description;
  private final String notes;

  CataloguingState(String code, String description, String notes) {
    this.code = code;
    this.description = description;
    this.notes = notes;
  }

  public String toString() {
    return this.code;
  }
}
