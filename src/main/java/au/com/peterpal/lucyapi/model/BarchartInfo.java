package au.com.peterpal.lucyapi.model;

import static java.util.stream.Collectors.groupingBy;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import lucy.fulfillment.codes.CataloguingAction;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@ToString
@Log4j2
public class BarchartInfo {

  private CataloguingState state;
  private int count;

  public static List<BarchartInfo> from(List<TitleOrderActionInfo> actionInfo) {
    Map<CataloguingState, List<BarchartInfo>> result = Optional.ofNullable(actionInfo)
        .map(List::stream)
        .orElseGet(Stream::empty)
        .map(
          info -> {
            BarchartInfo bi = null;
            if (CataloguingAction.NONE.equals(info.getAction()) ||
                CataloguingAction.COMPLETE.equals(info.getAction())) {
              log.warn(() -> String.format("Title order action is NONE or COMPLETE. Ignoring record %s", info));
            } else {
              bi = BarchartInfo.of(ActionStateMap.fromAction(info.getAction()), info.getCount());
            }
            return bi;
        })
        .filter(Objects::nonNull)
        .collect(Collectors.groupingBy(BarchartInfo::getState));

    return result
        .values()
        .stream()
        .map(e -> {
          if (e.size() == 1) {
            return e.get(0);
          } else {
            return BarchartInfo.of(e.get(0).getState(),
                e.stream()
                .map(BarchartInfo::getCount)
                .mapToInt(Integer::intValue)
                .sum()
            );
          }
        })
        .collect(Collectors.toList());
  }
}
