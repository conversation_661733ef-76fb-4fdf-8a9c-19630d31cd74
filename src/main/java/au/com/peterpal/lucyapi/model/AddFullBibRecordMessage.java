package au.com.peterpal.lucyapi.model;

import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class AddFullBibRecordMessage {
    @NonNull
    @NotNull
    private UUID invoiceGroupId;
    @NonNull
    @NotNull
    private UUID invoiceId;
    @NotNull
    private String workOrderNumber;
}
