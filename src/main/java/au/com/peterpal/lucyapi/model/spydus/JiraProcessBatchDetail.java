package au.com.peterpal.lucyapi.model.spydus;

import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@Builder
public class JiraProcessBatchDetail {
    private boolean succeed;
    private String irn;
    private String title;
    private String message;

    public static JiraProcessBatchDetail from(ProcessBatchResponse response){
        return JiraProcessBatchDetail.builder()
            .succeed(response.isSucceed())
            .irn(response.getIrn())
            .title(response.getTitle())
            .message(response.getMessage())
            .build();
    }

    public static List<JiraProcessBatchDetail> from(List<ProcessBatchResponse> responseList){
        return Optional.ofNullable(responseList)
            .map(Collection::stream)
            .orElse(Stream.empty())
            .map(JiraProcessBatchDetail::from)
            .collect(Collectors.toList());
    }
}
