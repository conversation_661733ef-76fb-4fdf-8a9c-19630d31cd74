package au.com.peterpal.lucyapi.model;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.Printer;
import lombok.*;

import java.util.UUID;

@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
public class CreatePrinterRequest {
  private String host;
  private String label;

  public Printer toPrinter() {
    return Printer.builder()
        .id(UUID.randomUUID())
        .host(host)
        .label(label)
        .build();
  }
}
