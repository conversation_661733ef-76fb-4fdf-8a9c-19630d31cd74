package au.com.peterpal.lucyapi.model;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@ToString
public class RemoveBarcodeMessage {
    @NotNull
    private String workOrderNumber;
    @NotEmpty
    private List<String> barcodes;
}
