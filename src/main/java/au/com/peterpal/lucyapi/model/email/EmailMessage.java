package au.com.peterpal.lucyapi.model.email;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class EmailMessage {
    private Map<String, Object> substitutionData;
    private String subject;
    private EmailAddress from;
    private List<EmailAddress> to;
    private List<EmailAddress> cc;
    private List<EmailAddress> bcc;
    private Content content;
}

