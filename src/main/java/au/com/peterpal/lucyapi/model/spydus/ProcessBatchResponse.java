package au.com.peterpal.lucyapi.model.spydus;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ProcessBatchResponse {
    @JsonProperty("Succeed")
    private boolean succeed;

    @JsonProperty("IsDeleted")
    private boolean isDeleted;

    @JsonProperty("Irn")
    private String irn;

    @JsonProperty("RecordId")
    private int recordId;

    @JsonProperty("StatusType")
    private int statusType;

    @JsonProperty("Message")
    private String message;

    @JsonProperty("LoadType")
    private int loadType;

    @JsonProperty("Title")
    private String title;
}
