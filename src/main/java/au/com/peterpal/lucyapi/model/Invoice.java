package au.com.peterpal.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Builder
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Invoice {
  @NonNull
  @JsonProperty
  String invoiceId;

  @NonNull
  @JsonProperty
  String invoiceNumber;
}
