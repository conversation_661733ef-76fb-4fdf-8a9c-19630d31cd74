package au.com.peterpal.lucyapi.model;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;

@Builder
@Value
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
public class SupplyErrorAdvicePrintMessage {
  @NonNull @NotEmpty String printJobId;
  @NonNull @NotEmpty String printer;
  LocalDate printedDate;
  String title;
  String author;
  String isbnReceived;
  String isbnOrdered;
  String supplierName;
  Integer quantity;
  String errorType;
  String invoiceNumber;
  String purchaseOrderNumber;
  Integer purchaseOrderLineNumber;
}
