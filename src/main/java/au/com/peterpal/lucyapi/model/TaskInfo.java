package au.com.peterpal.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Builder
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskInfo {

    @NonNull
    @JsonProperty
    private String taskId;

    @NonNull
    @JsonProperty
    private TaskStatus status;

    @NotNull
    @JsonProperty
    private TaskType type;

    @NotEmpty
    @NonNull
    @JsonProperty
    private String taskNumber;

    @NonNull
    @JsonProperty
    private Integer quantity;

    @NonNull
    @NotNull
    @JsonProperty
    private ServiceItemType serviceType;

    @NonNull
    @NotNull
    @JsonProperty
    private String serviceName;

    @NonNull
    @NotEmpty
    @JsonProperty
    private String workOrderId;

    @Builder.Default
    @JsonProperty
    private boolean nextWorkOrderOnDone = false;

    @Builder.Default
    @JsonProperty
    private List<SubtaskInfo> subtasks = Lists.newArrayList();

    @NonNull
    @NotEmpty
    @JsonProperty
    private String workOrderProfileType;

    @JsonProperty
    private Department department;
}
