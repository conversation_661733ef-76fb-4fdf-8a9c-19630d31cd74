package au.com.peterpal.lucyapi.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Wither;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Wither
public class AddCopiesResponse {
  @Builder.Default
  private List<Map.Entry<String, String>> barcodesAndDestinationsAdded = new ArrayList<>();

  @Builder.Default private Map<Integer, List<String>> printableLabelMap = new HashMap<>();

  private Integer allocationTemplatePk;
}
