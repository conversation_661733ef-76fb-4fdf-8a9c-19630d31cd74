package au.com.peterpal.lucyapi.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Value;

import java.time.LocalDate;

@Builder
@Value
@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
public class WorkOrderSlipMessage {

  String printer;
  String workOrderNumber;
  String printedDate;
  String titleOrderNumber;
  LocalDate titleOrderDate;
  String title;
  String author;
  String isbnReceived;
  String isbnOrdered;
  String customerReference;
  String customerCode;
  boolean partSupply;
  Integer receivedTotal;
  Integer orderedTotal;
  String orderType;
  String fundCode;
  String deliveryInstruction;
}
