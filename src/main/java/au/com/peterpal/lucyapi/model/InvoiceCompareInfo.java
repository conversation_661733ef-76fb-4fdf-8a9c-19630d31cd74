package au.com.peterpal.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
public class InvoiceCompareInfo {

  @JsonProperty("invNumber")
  private String documentNumber;
  private String reference;
  private String description;
  private Integer quantity;

  @NotNull
  @JsonFormat(shape=JsonFormat.Shape.STRING)
  private BigDecimal amountTax;

  @NotNull
  @JsonProperty("amountGross")
  @JsonFormat(shape=JsonFormat.Shape.STRING)
  private BigDecimal amountNet;
}
