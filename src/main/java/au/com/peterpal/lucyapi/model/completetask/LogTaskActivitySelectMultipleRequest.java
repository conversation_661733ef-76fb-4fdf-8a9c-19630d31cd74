package au.com.peterpal.lucyapi.model.completetask;

import lombok.*;
import lombok.experimental.Wither;
import org.springframework.lang.Nullable;

import java.util.List;


@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@Wither
public class LogTaskActivitySelectMultipleRequest {

  @NonNull
  private String workOrderId;

  @NonNull
  private String taskId;

  @NonNull
  private List<DoSubtask> doSubtasks;

  @Nullable
  private Boolean printWorkOrderSlip;

  private String printer;
}
