package au.com.peterpal.lucyapi.model;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Builder
public class BibRecord {

  private int bibRecordId;

  @NonNull
  @NotNull
  private String type;

  @NonNull
  @NotNull
  private String status;
}
