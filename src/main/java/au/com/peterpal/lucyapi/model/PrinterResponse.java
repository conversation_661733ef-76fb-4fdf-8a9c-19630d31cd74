package au.com.peterpal.lucyapi.model;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.Printer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Wither;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Wither
public class PrinterResponse {
  private UUID id;
  private String host;
  private String label;

  public static PrinterResponse from(Printer printer){
    return PrinterResponse.builder()
        .id(printer.getId())
        .host(printer.getHost())
        .label(printer.getLabel())
        .build();
  }
}
