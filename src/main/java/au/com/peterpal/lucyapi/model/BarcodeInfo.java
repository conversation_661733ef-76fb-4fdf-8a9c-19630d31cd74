package au.com.peterpal.lucyapi.model;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Builder
@ToString
public class BarcodeInfo {

  @NotEmpty
  private String barcode;

  @NotEmpty
  private String branchCode;

  private String branchName;
}
