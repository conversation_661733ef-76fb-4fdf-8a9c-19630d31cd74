package au.com.peterpal.lucyapi.model.completetask;

import lombok.*;
import lombok.experimental.Wither;
import org.springframework.lang.Nullable;

@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@Wither
public class LogTaskActivitySelectOneRequest {

  @NonNull
  private String workOrderId;

  @NonNull
  private String taskId;

  @NonNull
  private String completedSubtaskId;

  @NonNull
  private Integer quantityCompleted;

  @Nullable
  private Boolean printWorkOrderSlip;

  private String printer;
}
