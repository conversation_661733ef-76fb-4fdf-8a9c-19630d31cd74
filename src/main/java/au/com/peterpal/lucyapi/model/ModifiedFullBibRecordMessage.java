package au.com.peterpal.lucyapi.model;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.*;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor
@Builder
@ToString
public class ModifiedFullBibRecordMessage {

  @NonNull @NotNull
  private String customerCode;
  @NonNull @NotNull private UUID invoiceGroupId;
  @NotEmpty List<Invoice> invoices;

  @Data
  @NoArgsConstructor(force = true)
  @AllArgsConstructor(staticName = "of")
  @Builder
  public static class Invoice {
    @NotNull private UUID invoiceId;
    @NotEmpty List<WorkOrder> workOrders;
  }

  @Data
  @NoArgsConstructor(force = true)
  @AllArgsConstructor(staticName = "of")
  @Builder
  public static class WorkOrder {

    @NotNull private String workOrderNumber;
    @NotEmpty private Set<String> invoicedBarcode;
  }
}
