package au.com.peterpal.lucyapi.model;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;
import lucy.fulfillment.codes.CataloguingAction;

public class ActionStateMap {

  private static final Map<CataloguingAction, CataloguingState> mapActionToState;
  private static final Map<CataloguingState, CataloguingAction> mapStateToAction;

  static {
    mapActionToState = new EnumMap<>(CataloguingAction.class);
    mapActionToState.put(CataloguingAction.RETRIEVE, CataloguingState.NEW);
    mapActionToState.put(CataloguingAction.MATCH, CataloguingState.RETRIEVED);
    mapActionToState.put(CataloguingAction.OO_NEW, CataloguingState.OOR);
    mapActionToState.put(CataloguingAction.OO_ADD_ITEM, CataloguingState.ITEM);
    mapActionToState.put(CataloguingAction.OO_ADD_ISBN, CataloguingState.ISBN);
    mapActionToState.put(CataloguingAction.CHECK, CataloguingState.CHECK);
    mapActionToState.put(CataloguingAction.OO_MANUAL, CataloguingState.CHECK);
    mapActionToState.put(CataloguingAction.ACCESSION, CataloguingState.EXPORTED);
    mapActionToState.put(CataloguingAction.CATALOGUE, CataloguingState.EXPORTED);
    mapActionToState.put(CataloguingAction.ORIGINAL_CATALOGUE, CataloguingState.EXPORTED);
    mapActionToState.put(CataloguingAction.LOAD, CataloguingState.EXPORTED);
    mapActionToState.put(CataloguingAction.COMPLETE, CataloguingState.FINAL);

    mapStateToAction = new EnumMap<>(CataloguingState.class);
    mapStateToAction.put(CataloguingState.NEW, CataloguingAction.RETRIEVE);
    mapStateToAction.put(CataloguingState.RETRIEVED, CataloguingAction.MATCH);
    mapStateToAction.put(CataloguingState.OOR, CataloguingAction.OO_NEW);
    mapStateToAction.put(CataloguingState.ITEM, CataloguingAction.OO_ADD_ITEM);
    mapStateToAction.put(CataloguingState.ISBN, CataloguingAction.OO_ADD_ISBN);
    mapStateToAction.put(CataloguingState.CHECK, CataloguingAction.CHECK);
    mapStateToAction.put(CataloguingState.EXPORTED, CataloguingAction.CATALOGUE);
    mapStateToAction.put(CataloguingState.FINAL, CataloguingAction.COMPLETE);
  }

  public static Map<CataloguingAction, CataloguingState> actionToState() {
    return mapActionToState;
  }

  public static Map<CataloguingState, CataloguingAction> stateToAction() { return mapStateToAction; }

  public static CataloguingState fromAction(CataloguingAction action) {
    return Optional.ofNullable(action).isPresent() ? ActionStateMap.actionToState().get(action) : null;
  }

  public static CataloguingAction toAction(CataloguingState state) {
    return Optional.ofNullable(state).isPresent() ? ActionStateMap.stateToAction().get(state) : null;
  }

  private ActionStateMap() {
    throw new IllegalStateException("Utility class");
  }
}
