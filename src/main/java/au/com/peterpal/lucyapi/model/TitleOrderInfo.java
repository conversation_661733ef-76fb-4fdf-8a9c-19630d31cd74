package au.com.peterpal.lucyapi.model;

import au.com.peterpal.common.rest.validation.BusinessException;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.codes.OrganisationIdentifierTypeCode;
import lucy.catalogue.codes.ProductIdentifierTypeCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.catalogue.entity.ProductIdentifier;
import lucy.fulfillment.codes.CataloguingAction;
import lucy.fulfillment.codes.TitleOrderStatus;
import lucy.fulfillment.entity.ClosedTitleOrder;
import lucy.fulfillment.entity.OpenTitleOrder;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
@Value
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@Log4j2
public class TitleOrderInfo {

  private CataloguingAction nextAction;

  private CataloguingState state;

  private String status;

  private String orderNumber;

  private String customerCode;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate orderDate;

  private String sourceCode;

  private Integer qty;

  private String productIdValue;

  private String title;

  private String subtitle;

  private String author;

  private String series;

  private String edition;

  private String publisherName;

  private String media;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate publicationDate;

  private String customerReference;

  private String fundCode;

  private String collectionCode;

  private String itemTypeCode;

  private String categoryCode1;

  private String categoryCode2;

  private String genre;

  private String dewey;

  private String cutter;

  private String callNumber;

  private String spineLabelPrefix;

  private String spineLabel;

  private String cataloguingReference;

  private String barCodeNumber;

  private String deliveryInstructions;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate lastRetrievedClientRecords;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate lastExportedDate;

  private String lastExportedBy;

  private Integer onOrderBibRecordBatchId;

  public static Optional<TitleOrderInfo> from(OpenTitleOrder openTitleOrder) {
    return Optional
        .ofNullable(openTitleOrder)
        .map(TitleOrderInfo::buildHelper);
  }

  public static Optional<TitleOrderInfo> from(ClosedTitleOrder closedTitleOrder) {
    return Optional
        .ofNullable(closedTitleOrder)
        .map(TitleOrderInfo::buildHelper);
  }

  public static List<TitleOrderInfo> from(List<OpenTitleOrder> openTitleOrders) {
    return Optional.ofNullable(openTitleOrders)
        .map(List::stream)
        .orElseGet(Stream::empty)
        .map(
            openTitleOrder -> {
              TitleOrderInfo result = null;
              if (CataloguingAction.NONE.equals(openTitleOrder.getCataloguingAction())) {
                log.warn(() -> String.format("Next title order action is NONE. Ignoring record %s", openTitleOrder));
              } else {
                result = buildHelper(openTitleOrder);
            }
              return result;
            })
        .collect(Collectors.toList());
  }

  public OpenTitleOrder to() {
    OrganisationIdentifier customerId =
        new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS,
            customerCode == null || customerCode.isEmpty() ? "DEFAULT" : customerCode);
    if (productIdValue == null || productIdValue.isEmpty()) {
      throw new BusinessException("productIdValue must not be null or empty");
    }

    ProductIdentifier productId = new ProductIdentifier(ProductIdentifierTypeCode.ISBN13, productIdValue);
    OpenTitleOrder titleOrder = new OpenTitleOrder();
    titleOrder.setCustomerId(customerId);
    titleOrder.setOrderDate(Date.from(orderDate.atStartOfDay()
        .atZone(ZoneId.systemDefault())
        .toInstant())
    );
    titleOrder.setStatus(status == null ? TitleOrderStatus.NEW : TitleOrderStatus.valueOf(status.toUpperCase()));
    titleOrder.setSourceCode(sourceCode);
    titleOrder.setProductId(productId);
    titleOrder.setTitle(title);
    titleOrder.setSubtitle(subtitle);
    titleOrder.setEdition(edition);
    titleOrder.setSeries(series);
    titleOrder.setAuthor(author);
    titleOrder.setPublisherName(publisherName);
    if (publicationDate != null) {
      titleOrder.setPublicationDate(Date.from(publicationDate.atStartOfDay()
        .atZone(ZoneId.systemDefault())
        .toInstant()));
    }
    titleOrder.setMedia(media);
    titleOrder.setQtyOrdered(qty == null ? 0 : qty);
    titleOrder.setCustomerReference(customerReference);
    titleOrder.setCataloguingReference(cataloguingReference);
    titleOrder.setBarCodeNumber(barCodeNumber);
    titleOrder.setFundCode(fundCode);
    titleOrder.setItemTypeCode(itemTypeCode);
    titleOrder.setCollectionCode(collectionCode);
    titleOrder.setCategoryCode1(categoryCode1);
    titleOrder.setCategoryCode2(categoryCode2);
    titleOrder.setGenre(genre);
    titleOrder.setDewey(dewey);
    titleOrder.setCutter(cutter);
    titleOrder.setCallNumber(callNumber);
    titleOrder.setSpineLabel(spineLabel);
    titleOrder.setSpineLabelPrefix(spineLabelPrefix);
    titleOrder.setDeliveryInstructions(deliveryInstructions);
    return titleOrder;
  }

  private static LocalDate toLocalDate(Date date) {
    LocalDate result = null;
    if (date != null) {
      Date d = new Date(date.getTime());
      result = d.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
    return result;
  }

  private static TitleOrderInfo buildHelper(OpenTitleOrder openTitleOrder) {
    return TitleOrderInfo.builder()
        .nextAction(openTitleOrder.getCataloguingAction())
        .state(ActionStateMap.fromAction(openTitleOrder.getCataloguingAction()))
        .orderNumber(openTitleOrder.getOrderId().getTransactionIdValue())
        .customerCode(openTitleOrder.getCustomerId().getOrganisationIdValue())
        .orderDate(toLocalDate(openTitleOrder.getOrderDate()))
        .sourceCode(openTitleOrder.getSourceCode())
        .qty(openTitleOrder.getQtyOrdered())
        .productIdValue(
            openTitleOrder.getProductId() == null
                ? null
                : openTitleOrder.getProductId().getProductIdentifierValue())
        .title(openTitleOrder.getTitle())
        .subtitle(openTitleOrder.getSubtitle())
        .author(openTitleOrder.getAuthor())
        .series(openTitleOrder.getSeries())
        .edition(openTitleOrder.getEdition())
        .publisherName(openTitleOrder.getPublisherName())
        .media(openTitleOrder.getMedia())
        .publicationDate(toLocalDate(openTitleOrder.getPublicationDate()))
        .customerReference(openTitleOrder.getCustomerReference())
        .fundCode(openTitleOrder.getFundCode())
        .collectionCode(openTitleOrder.getCollectionCode())
        .itemTypeCode(openTitleOrder.getItemTypeCode())
        .categoryCode1(openTitleOrder.getCategoryCode1())
        .categoryCode2(openTitleOrder.getCategoryCode2())
        .genre(openTitleOrder.getGenre())
        .dewey(openTitleOrder.getDewey())
        .cutter(openTitleOrder.getCutter())
        .callNumber(openTitleOrder.getCallNumber())
        .spineLabelPrefix(openTitleOrder.getSpineLabelPrefix())
        .spineLabel(openTitleOrder.getSpineLabel())
        .cataloguingReference(openTitleOrder.getCataloguingReference())
        .barCodeNumber(openTitleOrder.getBarCodeNumber())
        .deliveryInstructions(openTitleOrder.getDeliveryInstructions())
        .lastRetrievedClientRecords(
            toLocalDate(openTitleOrder.getLastRetrievedClientRecords()))
        .lastExportedDate(toLocalDate(openTitleOrder.getLastExportedDate()))
        .lastExportedBy(openTitleOrder.getLastExportedBy())
        .onOrderBibRecordBatchId(openTitleOrder.getOnOrderBibRecordBatchId())
        .status(openTitleOrder.getStatus() == null ? null : openTitleOrder.getStatus().getCode())
        .build();
  }

  private static TitleOrderInfo buildHelper(ClosedTitleOrder closedTitleOrder) {
    return TitleOrderInfo.builder()
        .nextAction(closedTitleOrder.getCataloguingAction())
        .state(ActionStateMap.fromAction(closedTitleOrder.getCataloguingAction()))
        .orderNumber(closedTitleOrder.getOrderId().getTransactionIdValue())
        .customerCode(closedTitleOrder.getCustomerId().getOrganisationIdValue())
        .orderDate(toLocalDate(closedTitleOrder.getOrderDate()))
        .sourceCode(closedTitleOrder.getSourceCode())
        .qty(closedTitleOrder.getQtyOrdered())
        .productIdValue(
            closedTitleOrder.getProductId() == null
                ? null
                : closedTitleOrder.getProductId().getProductIdentifierValue())
        .title(closedTitleOrder.getTitle())
        .subtitle(closedTitleOrder.getSubtitle())
        .author(closedTitleOrder.getAuthor())
        .series(closedTitleOrder.getSeries())
        .edition(closedTitleOrder.getEdition())
        .publisherName(closedTitleOrder.getPublisherName())
        .media(closedTitleOrder.getMedia())
        .publicationDate(toLocalDate(closedTitleOrder.getPublicationDate()))
        .customerReference(closedTitleOrder.getCustomerReference())
        .fundCode(closedTitleOrder.getFundCode())
        .collectionCode(closedTitleOrder.getCollectionCode())
        .itemTypeCode(closedTitleOrder.getItemTypeCode())
        .categoryCode1(closedTitleOrder.getCategoryCode1())
        .categoryCode2(closedTitleOrder.getCategoryCode2())
        .genre(closedTitleOrder.getGenre())
        .dewey(closedTitleOrder.getDewey())
        .cutter(closedTitleOrder.getCutter())
        .callNumber(closedTitleOrder.getCallNumber())
        .spineLabelPrefix(closedTitleOrder.getSpineLabelPrefix())
        .spineLabel(closedTitleOrder.getSpineLabel())
        .cataloguingReference(closedTitleOrder.getCataloguingReference())
        .barCodeNumber(closedTitleOrder.getBarCodeNumber())
        .deliveryInstructions(closedTitleOrder.getDeliveryInstructions())
        .lastRetrievedClientRecords(toLocalDate(closedTitleOrder.getLastRetrievedClientRecords()))
        .lastExportedDate(toLocalDate(closedTitleOrder.getLastExportedDate()))
        .lastExportedBy(closedTitleOrder.getLastExportedBy())
        .onOrderBibRecordBatchId(closedTitleOrder.getOnOrderBibRecordBatchId())
        .status(
            closedTitleOrder.getStatus() == null ? null : closedTitleOrder.getStatus().getCode())
        .build();
  }
}
