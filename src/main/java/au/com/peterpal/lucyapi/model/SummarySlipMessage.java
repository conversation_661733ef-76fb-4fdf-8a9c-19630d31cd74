package au.com.peterpal.lucyapi.model;

import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class SummarySlipMessage {
    @NonNull
    private String workOrderNumber;
    @NonNull
    private String printer;
    @NonNull
    private List<Task> tasks;

    @Data
    @NoArgsConstructor(force = true)
    @AllArgsConstructor(staticName = "of")
    @Builder
    @ToString
    public static class Task {

        @NonNull
        private List<Subtask> subtasks;
        @NonNull
        private String serviceName;
    }

    @Data
    @NoArgsConstructor(force = true)
    @AllArgsConstructor(staticName = "of")
    @Builder
    @ToString
    public static class Subtask {

        @NotNull
        private String subtaskId;
        @NonNull
        private SubtaskStatus status;
        @NotNull
        private String serviceComponentName;
        @NotNull
        private String serviceComponentDescription;
    }

}
