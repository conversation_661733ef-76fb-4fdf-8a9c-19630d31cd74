package au.com.peterpal.lucyapi.model;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.Printer;
import lombok.*;
import lombok.experimental.Wither;

import java.util.Optional;
import java.util.UUID;

@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@Wither
public class UpdatePrinterRequest {
  private UUID id;
  private String host;
  private String label;

  public Printer toPrinter(Printer printer) {
    return Printer.builder()
        .id(printer.getId())
        .host(Optional.ofNullable(host).orElse(printer.getHost()))
        .label(Optional.ofNullable(label).orElse(printer.getLabel()))
        .build();
  }
}
