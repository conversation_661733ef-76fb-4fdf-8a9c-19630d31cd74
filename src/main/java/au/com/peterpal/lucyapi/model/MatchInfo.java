package au.com.peterpal.lucyapi.model;

import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lucy.catalogue.entity.ProductIdentifier;
import lucy.cataloguing.codes.CustomerRecordMatchStatus;
import lucy.cataloguing.entity.BibRecord;
import lucy.cataloguing.entity.BibTemplate;
import lucy.cataloguing.util.TitleOrderMatch;
import lucy.fulfillment.entity.OpenTitleOrder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class MatchInfo {
  private TitleOrderInfo titleOrderInfo;

  private String productIdentifier;

  private Integer authorityBibRecordId;

  private Integer productDescriptorId;

  private Integer matchBibRecord;

  private Integer templateId;

  @Builder.Default
  private Integer idMatchThreshhold = 100;

  private Integer matchingScore;

  private String message;

  @Builder.Default
  private CustomerRecordMatchStatus status = CustomerRecordMatchStatus.UNMATCHED;

  private String statusString;

  public static MatchInfo from(OpenTitleOrder order, TitleOrderMatch titleOrderMatch, BibTemplate bibTemplate, String message) {
    MatchInfo mi =
        MatchInfo.builder()
            .titleOrderInfo(TitleOrderInfo.from(order).orElseGet(null))
            .message(message)
            .build();

    Optional.ofNullable(titleOrderMatch)
        .ifPresent(match -> {
          mi.setProductIdentifier(getProductIdentifierValue(order.getProductId()));
          mi.setAuthorityBibRecordId(getBibRecordId(match.getAuthorityBibRecord()));
          mi.setProductDescriptorId(getBibRecordId(match.getProductDescriptorBibRecord()));
          mi.setMatchBibRecord(getBibRecordId(match.getCustomerBibRecord()));
          mi.setTemplateId(getBibTemplateId(bibTemplate));
          mi.setIdMatchThreshhold(match.getIdMatchThreshhold());
          mi.setMatchingScore(match.getMatchingScore());
          mi.setStatus(match.getStatus());
          mi.setStatusString(String.format("%s - %s",match.getStatus().getCode(), match.getStatus().getDescription()));
        });
    return mi;
  }

  private static Integer getBibRecordId(BibRecord record) {
    return Optional.ofNullable(record)
        .map(br -> br.getPk())
        .orElse(null);
  }

  private static Integer getBibTemplateId(BibTemplate bibTemplate) {
    return Optional.ofNullable(bibTemplate)
      .map(template -> template.getPk())
      .orElse(null);
  }

  private static String getProductIdentifierValue(ProductIdentifier identifier) {
    return Optional.ofNullable(identifier)
        .map(product -> product.getProductIdentifierValue())
        .orElse("");
  }
}
