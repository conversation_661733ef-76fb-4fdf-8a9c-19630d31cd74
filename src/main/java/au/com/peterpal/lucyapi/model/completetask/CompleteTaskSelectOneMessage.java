package au.com.peterpal.lucyapi.model.completetask;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Wither;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Wither
@Builder
public class CompleteTaskSelectOneMessage {
    @NotNull
    private String currentUser;
    @NotNull
    private LogTaskActivitySelectOneRequest logTaskActivitySelectOne;
}
