package au.com.peterpal.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Builder
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkOrderBarcodeResponse {
  @NonNull @JsonProperty private String workOrderNumber;

  @JsonProperty private String barcode;

  @NonNull @JsonProperty private String invoiceNumber;
}
