package au.com.peterpal.lucyapi.model;

import java.util.EnumMap;
import java.util.Map;

public enum BibRecordType {
  NORMAL,
  ONORDER,
  HOLDING,
  COPY,
  PRODUCT,
  AUTH,
  SUBJECT,
  NAME,
  TITLE,
  NAM<PERSON>ITLE,
  TEMPLATE;

  private static final Map<BibRecordType, lucy.cataloguing.codes.BibRecordType> toLucyMap;

  private static final Map<lucy.cataloguing.codes.BibRecordType, BibRecordType> fromLucyMap;

  static {
    toLucyMap = new EnumMap<>(BibRecordType.class);
    toLucyMap.put(BibRecordType.NORMAL, lucy.cataloguing.codes.BibRecordType.NORMAL);
    toLucyMap.put(BibRecordType.ONORDER, lucy.cataloguing.codes.BibRecordType.ONORDER);
    toLucyMap.put(BibRecordType.HOLDING, lucy.cataloguing.codes.BibRecordType.HOLDING);
    toLucyMap.put(BibRecordType.COPY, lucy.cataloguing.codes.BibRecordType.COPY);
    toLucyMap.put(BibRecordType.PRODUCT, lucy.cataloguing.codes.BibRecordType.PRODUCT);
    toLucyMap.put(BibRecordType.AUTH, lucy.cataloguing.codes.BibRecordType.AUTH);
    toLucyMap.put(BibRecordType.SUBJECT, lucy.cataloguing.codes.BibRecordType.SUBJECT);
    toLucyMap.put(BibRecordType.NAME, lucy.cataloguing.codes.BibRecordType.NAME);
    toLucyMap.put(BibRecordType.TITLE, lucy.cataloguing.codes.BibRecordType.TITLE);
    toLucyMap.put(BibRecordType.NAMETITLE, lucy.cataloguing.codes.BibRecordType.NAMETITLE);
    toLucyMap.put(BibRecordType.TEMPLATE, lucy.cataloguing.codes.BibRecordType.TEMPLATE);

    fromLucyMap = new EnumMap<>(lucy.cataloguing.codes.BibRecordType.class);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.NORMAL, BibRecordType.NORMAL);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.ONORDER, BibRecordType.ONORDER);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.HOLDING, BibRecordType.HOLDING);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.COPY, BibRecordType.COPY);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.PRODUCT, BibRecordType.PRODUCT);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.AUTH, BibRecordType.AUTH);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.SUBJECT, BibRecordType.SUBJECT);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.NAME, BibRecordType.NAME);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.TITLE, BibRecordType.TITLE);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.NAMETITLE, BibRecordType.NAMETITLE);
    fromLucyMap.put(lucy.cataloguing.codes.BibRecordType.TEMPLATE, BibRecordType.TEMPLATE);
  }

  public lucy.cataloguing.codes.BibRecordType toLucy() {
    return toLucyMap.get(this);
  }

  public static BibRecordType from(lucy.cataloguing.codes.BibRecordType lucyType) {
    return fromLucyMap.get(lucyType);
  }

}
