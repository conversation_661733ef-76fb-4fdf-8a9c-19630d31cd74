package au.com.peterpal.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

enum SubtaskStatus {
    TODO, IN_PROGRESS, DONE, SKIPPED, COMPLETE, CANC<PERSON>LED
}

enum FeeType {
    PER_ORDER, PER_WORK_ORDER, PER_WORK_ORDER_UNIT, PER_UNIT
}


@Builder
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SubtaskInfo {

    @NonNull
    @NotEmpty
    @JsonProperty
    private String subtaskId;

    @NonNull
    @NotEmpty
    @JsonProperty
    private String taskId;

    @NonNull
    @JsonProperty
    private SubtaskStatus status;

    @NonNull
    @JsonProperty
    private Integer quantity;

    @NonNull
    @NotNull
    @JsonProperty
    private Boolean serviceComponentDefaultVal;

    @NonNull
    @NotNull
    @JsonProperty
    private Boolean mandatory;

    @NonNull
    @NotNull
    @JsonProperty
    private String serviceComponentName;

    @JsonProperty
    private String serviceComponentDescription;

    @JsonProperty
    private FeeType feeType;

    @JsonProperty
    private String feeCode;
}
