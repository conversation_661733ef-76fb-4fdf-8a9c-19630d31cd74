package au.com.peterpal.lucyapi.model;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@ToString
public class PrintLabelMessage {
    @NotNull
    private Integer allocationsTemplateId;
    @NotEmpty
    private Map<Integer, List<String>> pairOfCopyIdAndLabelNames;

}
