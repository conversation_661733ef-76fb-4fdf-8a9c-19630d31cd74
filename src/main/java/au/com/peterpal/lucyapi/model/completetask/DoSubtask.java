package au.com.peterpal.lucyapi.model.completetask;

import au.com.peterpal.lucyapi.model.SubtaskInfo;
import lombok.*;
import lombok.experimental.Wither;

@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@Wither
public class DoSubtask {

  @NonNull
  private String subtaskId;

  @NonNull
  private Integer quantity;

  public static DoSubtask from(SubtaskInfo subtaskInfo){
    return DoSubtask.builder()
        .subtaskId(subtaskInfo.getSubtaskId())
        .quantity(subtaskInfo.getQuantity())
        .build();
  }
}
