package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
@Embeddable
public class CollectionTypeByFundKey implements Serializable {

  @Column(name = "customer_code")
  private String customerCode;

  private String fund;
}
