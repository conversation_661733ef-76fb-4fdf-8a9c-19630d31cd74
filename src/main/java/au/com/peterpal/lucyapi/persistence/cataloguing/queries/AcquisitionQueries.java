package au.com.peterpal.lucyapi.persistence.cataloguing.queries;

public class AcquisitionQueries {
    public static final String FIND_ACQUISITION_PK_BY_WORK_ORDER_NUMBER =
        "select acquisition_pk  from lucycataloguing.public.receipt r2 where orderassignid =:workOrderNumber";

    public static final String DELETE_COPIES_BY_ACQUISITION_PK =
        "delete from lucycataloguing.public.copy where acquisition_pk in (:acquisitionPks)";

    public static final String DELETE_ALLOCATION_BY_ACQUISITION_PK =
        "delete from lucycataloguing.public.allocation where acquisition_pk in (:acquisitionPks)";

    public static final String DELETE_RECEIPTS_BY_ACQUISITION_PK =
        "delete from lucycataloguing.public.receipt where acquisition_pk in (:acquisitionPks)";

    public static final String DELETE_ACQUISITION_BY_PK =
        "delete from lucycataloguing.public.acquisition where pk in (:acquisitionPks)";
}
