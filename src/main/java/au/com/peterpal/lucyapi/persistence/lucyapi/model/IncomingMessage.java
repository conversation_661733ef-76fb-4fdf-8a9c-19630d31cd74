package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@RequiredArgsConstructor(staticName = "of")
@Builder
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "incoming_message")
public class IncomingMessage {
  @Id
  @Column(updatable = false)
  @NonNull
  private String id;

  @Column(name = "message_source")
  private String messageSource;

  @Column(name = "message_type")
  private String messageType;

  @Column(name = "queue_name")
  private String queueName;

  @NotEmpty
  @JsonView(Views.Detail.class)
  private String body;

  @NotEmpty private String hash;

  @Enumerated(EnumType.STRING)
  @Column(length = 30)
  private IncomingMessageStatus status;

  @NotNull
  @Column(name = "retry_count")
  private Integer retryCount;

  @NotNull
  @Column(name = "received_date_time")
  private LocalDateTime receivedDateTime;

  @NotNull
  @Column(name = "last_modified_date_time")
  private LocalDateTime lastModifiedDateTime;
}
