package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;
import java.util.UUID;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "spydus_edi_orchestration_completed_steps")
public class SpydusEdiOrchestrationCompletedStep {

  @NonNull
  @NotNull
  @Id
  @Column(columnDefinition = "uuid", updatable = false)
  private UUID id;

  @ManyToOne
  @JoinColumn(name = "spydus_edi_orchestration_id", nullable = false)
  private SpydusEdiOrchestration spydusEdiOrchestration;

  @Column(name = "completed_steps", nullable = false)
  private String completedStep;

  @Column(name = "completed_date")
  @Builder.Default
  private LocalDateTime completedDate = LocalDateTime.now();

  public static SpydusEdiOrchestrationCompletedStep from(
      SpydusEdiOrchestration spydusEdiOrchestration, String completedStep) {
    return SpydusEdiOrchestrationCompletedStep.builder()
        .id(UUID.randomUUID())
        .spydusEdiOrchestration(spydusEdiOrchestration)
        .completedStep(completedStep)
        .completedDate(LocalDateTime.now())
        .build();
  }
}
