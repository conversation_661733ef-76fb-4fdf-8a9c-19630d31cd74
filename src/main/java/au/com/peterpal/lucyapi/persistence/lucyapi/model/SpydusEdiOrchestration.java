package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import au.com.peterpal.lucyapi.core.service.orchestration.dto.WorkflowStepStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.*;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(
    name = "spydus_edi_orchestration")
public class SpydusEdiOrchestration {

    @NonNull @NotNull
    @Id
    @Column(columnDefinition = "uuid", updatable = false)
    private UUID id;
    @NonNull @NotNull
    @Column(name = "invoice_group_id")
    private UUID invoiceGroupId;
    @NonNull @NotNull
    @Column(name = "customer_code")
    private String customerCode;

    @OneToMany(mappedBy = "spydusEdiOrchestration", cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
    @Builder.Default
    private List<SpydusEdiOrchestrationCompletedStep> completedSteps = new ArrayList<>();
    @Column(name = "retry_count")
    private Integer retryCount;

    @NotEmpty
    @JsonView(Views.Detail.class)
    @Column(name = "body_message")
    @NotNull
    private String bodyMessage;

    @Enumerated(EnumType.STRING)
    @Column(name = "work_flow_step_status")
    @NotNull
    private WorkflowStepStatus workflowStepStatus;

    @Column(name = "all_invoice_posted")
    private boolean allInvoicePosted = false;
    @Column(name = "created_date")
    @Builder.Default
    private LocalDateTime createdDate = LocalDateTime.now();

}
