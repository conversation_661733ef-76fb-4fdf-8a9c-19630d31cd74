package au.com.peterpal.lucyapi.persistence.cataloguing;

import au.com.peterpal.lucyapi.persistence.cataloguing.model.BatchBarcodeNumber;
import au.com.peterpal.lucyapi.persistence.cataloguing.model.BatchProductIdValue;
import au.com.peterpal.lucyapi.persistence.cataloguing.queries.BatchQueries;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class BatchJdbcTemplateRepository {
    private final NamedParameterJdbcTemplate cataloguingJdbcTemplate;

    public BatchJdbcTemplateRepository(NamedParameterJdbcTemplate cataloguingJdbcTemplate) {
        this.cataloguingJdbcTemplate = cataloguingJdbcTemplate;
    }

    public List<BatchProductIdValue> getProductIdValueByBatchId(Integer batchId){
        SqlParameterSource parameters = new MapSqlParameterSource("batchId", batchId);

        return cataloguingJdbcTemplate.query(BatchQueries.GET_PRODUCT_ID_VALUE_BY_BATCH_ID,
            parameters,
            (rs, rowNum) -> BatchProductIdValue.builder()
                .batchId(rs.getInt("pk"))
                .productIdValue(rs.getString("prodidvalue"))
                .customerCode(rs.getString("customer_code"))
                .build());
    }
}
