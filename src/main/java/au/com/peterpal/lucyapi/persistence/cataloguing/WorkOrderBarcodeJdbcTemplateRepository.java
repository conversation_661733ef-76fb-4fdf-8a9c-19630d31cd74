package au.com.peterpal.lucyapi.persistence.cataloguing;

import au.com.peterpal.lucyapi.model.WorkOrderBarcode;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class WorkOrderBarcodeJdbcTemplateRepository {

  private final NamedParameterJdbcTemplate cataloguingJdbcTemplate;
  @Value("${marc.barcode-line-prefix:995,949,945}")
  private String barcodeLinePrefix;

  public List<WorkOrderBarcode> findBarcodeByBatchIds(Set<Integer> batchIds) {
    SqlParameterSource parameters = new MapSqlParameterSource("batchIds", batchIds);

    String tagConditions = Arrays.stream(barcodeLinePrefix.split(","))
        .map(tag -> "SUBSTRING(br.marctext2 FROM '(" + tag + "   \\$.+?\\n)$') like '%'||c.barcodenumber||'%'")
        .collect(Collectors.joining(" or "));

    String query =
        "SELECT r.orderassignid as work_order_number, c.barcodenumber as barcode, brb.pk as batch_id FROM bibrecord br\n"
            + "INNER JOIN bibrecordbatch_bibrecords brb_br on br.pk = brb_br.bibrecord_pk\n"
            + "INNER JOIN bibrecordbatch brb on brb_br.bibrecordbatch_pk = brb.pk\n"
            + "INNER JOIN public.bibcoll_suppbibrecords bs on br.pk = bs.bibrecord_pk\n"
            + "INNER JOIN bibcoll bc on bs.bibcoll_pk = bc.pk\n"
            + "INNER JOIN holding h on bc.pk = h.bibcoll_pk\n"
            + "INNER JOIN acquisition a on h.pk = a.holding_pk\n"
            + "INNER JOIN receipt r on a.pk = r.acquisition_pk\n"
            + "INNER JOIN copy c on a.pk = c.acquisition_pk\n"
            + "WHERE brb.pk IN (:batchIds)\n"
            + "   AND (" + tagConditions + ") ;";

    return cataloguingJdbcTemplate.query(
        query,
        parameters,
        (resultSet, i) ->
            WorkOrderBarcode.builder()
                .workOrderNumber(resultSet.getString("work_order_number"))
                .barcode(resultSet.getString("barcode"))
                .batchId(resultSet.getInt("batch_id"))
                .build());
  }
}
