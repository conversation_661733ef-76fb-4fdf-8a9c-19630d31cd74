package au.com.peterpal.lucyapi.persistence.cataloguing.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import lombok.Data;
import lucy.cataloguing.codes.BibRecordBatchStatus;
import lucy.cataloguing.codes.BibRecordType;

@Data
@Entity
@Table(name = "BIBRECORDBATCH")
public class BiBRecordBatchInfo {

  @Id
  @Column(name = "pk")
  private Integer id;

  @Column(name = "customerIdValue")
  private String customerCode;

  @Column(name = "customerIdType")
  private String customerType;

  @Column
  private String description;

  @Enumerated(EnumType.STRING)
  @Column(name = "type")
  private BibRecordType bibType;

  @Enumerated(EnumType.STRING)
  @Column
  private BibRecordBatchStatus status;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  @Column(name = "dateLastModified")
  private LocalDateTime dateModified;

  @Column(name = "lastModifiedBy")
  private String modifiedBy;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "bibrecordbatch_pk")
  private List<BatchBib> bibRecords = new ArrayList<>();
}
