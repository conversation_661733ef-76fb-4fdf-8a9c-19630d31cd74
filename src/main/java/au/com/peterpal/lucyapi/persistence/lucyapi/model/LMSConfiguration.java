package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "lms_configuration")
public class LMSConfiguration {
    @Id
    @Column(name = "customer_code")
    private String customerCode;
    private String url;
    @Column(name = "lms_type")
    @Enumerated(EnumType.STRING)
    private LMSType lmsType;
    private String username;
    private String password;
    private String location;
    private String sublocation;
    @Column(name = "load_control")
    private String loadControl;
    @Column(name = "delete_oor")
    private boolean deleteOOR;
    @Column(name = "automatically_close_batches")
    private boolean automaticallyCloseBatches;
    @Column(name = "print_branch_slips")
    private boolean printBranchSlips;
    @Column(name = "batch_schedule")
    @Enumerated(EnumType.STRING)
    private BatchScheduleType batchSchedule;
    @Column(name = "on_order_holding_text")
    private String onOrderHoldingText;
    @Column(name = "version")
    private String version;
    @Column(name = "public_key")
    private String publicKey;
    @Column(name = "spydus_edi")
    private Boolean spydusEDI = false;
    @Column(name = "batch_location")
    private String batchLocation;
    @Column(name = "spydus_one_time_login_secret")
    private String spydusOneTimeLoginSecret;
}
