package au.com.peterpal.lucyapi.persistence.lucyapi;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.LMSConfiguration;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.LMSType;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.QLMSConfiguration;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Log4j2
@Repository
public class LMSConfigurationRepositoryCustom {

  private EntityManager em;

  public LMSConfigurationRepositoryCustom(@Qualifier("lucyAPI") EntityManager em) {
    this.em = em;
  }

  public List<LMSConfiguration> findByCustomerCode(String customerCode) {
    JPAQueryFactory queryFactory = new JPAQueryFactory(em);
    QLMSConfiguration lmsConfiguration = QLMSConfiguration.lMSConfiguration;

    BooleanBuilder condition = new BooleanBuilder();
    condition.and(lmsConfiguration.customerCode.eq(customerCode));

    return queryFactory
        .selectFrom(lmsConfiguration)
        .where(condition)
        .fetch();
  }
}
