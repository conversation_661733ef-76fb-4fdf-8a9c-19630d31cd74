package au.com.peterpal.lucyapi.persistence.lucy;

import au.com.peterpal.lucyapi.model.TitleOrderActionInfo;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import lombok.extern.log4j.Log4j2;
import lucy.fulfillment.codes.CataloguingAction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class TitleOrderActionInfoRepository {

  private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

  @PersistenceContext
  @Qualifier("lucyEM")
  private EntityManager em;

  public List<TitleOrderActionInfo> getTitleOrderActionInfo(String customer, LocalDate dateFrom, LocalDate dateTo) {

    if (dateTo == null) {
      dateTo = LocalDate.now();
    }

    if (dateFrom == null) {
      dateFrom = dateTo.minusYears(10);
    }

    if (dateFrom.isAfter(dateTo)) {
      throw new IllegalStateException(String.format("dateFrom %s should be before dateTo %s", dateFrom, dateTo));
    }

    StringBuilder query = new StringBuilder("SELECT o.cataloguingAction, count(*) FROM dbo.TitleOrd o");
    query.append(" WHERE o.standingOrderFlag = 0 and o.customerCode='").append(customer).append("'");

    if (dateFrom != null && dateTo != null) {
      query.append(" and o.orderDate between '");
      query.append(dateFrom.format(formatter));
      query.append("' and '");
      query.append(dateTo.format(formatter)).append("'");
    }
    query.append(" GROUP by o.cataloguingAction");

    log.debug(() -> String.format("Executing query %s", query));
    return convert(em.createNativeQuery(query.toString()).getResultList());
  }

  private List<TitleOrderActionInfo> convert(List<Object[]> queryResult) {
    return queryResult.stream()
        .map(row -> TitleOrderActionInfo.of(
                row[0] == null ? CataloguingAction.RETRIEVE : this.mapAction(row[0]),
                (Integer)row[1]
        ))
        .filter(x -> x.getAction() != null)
        .collect(Collectors.toList());
  }

  private CataloguingAction mapAction(Object value) {
    if (!(value instanceof String)) {
      log.warn(() -> String.format("Expecting string value for Cataloguing action. Found %s", value == null ? "null" : value.getClass().getName()));
      return null;
    }

    CataloguingAction result = null;
    try {
      result = CataloguingAction.valueOf((String)value);
    } catch (Exception ex) {
      log.warn(() -> String.format("String %s does not represent valid CataloguingAction.", (String)value));
    }
    return result;
  }
}
