package au.com.peterpal.lucyapi.persistence.lucyapi;

import javax.persistence.EntityManager;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

@Log4j2

@Repository
public class CollectionTypeByFundRepositoryImpl implements CollectionTypeByFundRepositoryCustom {
  private EntityManager em;

  public CollectionTypeByFundRepositoryImpl(@Qualifier("lucyAPI") EntityManager em) {
    this.em = em;
  }
}
