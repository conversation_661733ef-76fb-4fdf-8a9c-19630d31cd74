package au.com.peterpal.lucyapi.persistence.lucyapi;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.IncomingMessage;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.IncomingMessageStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IncomingMessageRepository
    extends JpaRepository<IncomingMessage, String> {

  List<IncomingMessage> findByStatus(IncomingMessageStatus status);
}
