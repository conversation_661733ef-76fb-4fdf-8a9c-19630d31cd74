package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Wither;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Wither
@Table(name = "genre_labels",
    uniqueConstraints = {@UniqueConstraint(name = "genre_labels_un", columnNames = {"customer_code", "holding_field", "holding_value"})})
public class GenreLabel {

    private static final String FIELD_SEPARATOR = ",";
    private static final String HOLDING_VALUE_FIELD_SEPARATOR = "|";

    @NonNull
    @NotNull
    @Id
    @Column(columnDefinition = "uuid", updatable = false)
    private UUID id;
    @NonNull
    @NotNull
    @Column(name = "customer_code")
    private String customerCode;

    @NonNull
    @NotNull
    @Column(name = "holding_field")
    private String holdingField;
    @NonNull
    @NotNull
    @Column(name = "holding_value")
    private String holdingValue;
    @NonNull
    @NotNull
    @Column(name = "image")
    private String image;

  public String[] getHoldingFieldSplit() {
    return StringUtils.split(getHoldingField(), FIELD_SEPARATOR);
  }

  public String[] getHoldingValueSplit() {
    return StringUtils.split(getHoldingValue(), HOLDING_VALUE_FIELD_SEPARATOR);
  }

  public boolean isValid() {
    return getHoldingFieldSplit().length == getHoldingValueSplit().length;
  }
}
