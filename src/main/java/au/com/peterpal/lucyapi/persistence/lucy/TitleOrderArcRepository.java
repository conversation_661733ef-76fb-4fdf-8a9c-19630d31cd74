package au.com.peterpal.lucyapi.persistence.lucy;

import static org.springframework.util.Assert.notNull;

import au.com.peterpal.lucyapi.model.TitleOrderInfo;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

@Log4j2
@Repository
public class TitleOrderArcRepository {
    @PersistenceContext
    @Qualifier("lucyEM")
    private EntityManager em;

    public TitleOrderInfo findTitleOrderArcByOrderAssignId(String orderAssignId){
        notNull(orderAssignId, "orderAssignId must not be null");
    String query =
        "SELECT t.customerCode, t.fundCode, t.isbn, t.titleOrderNum\n"
            + "FROM dbo.TitleOrdArc t\n"
            + "         INNER JOIN dbo.ItemRcptAssign ira on t.titleOrderNum = ira.titleOrderNum\n"
            + "WHERE ira.orderAssignId = :orderAssignId";

        log.debug(() -> String.format("Executing query %s", query));
        Query q = em.createNativeQuery(query);
        q.setParameter("orderAssignId", orderAssignId);
        List<Object[]> resultSet = q.getResultList();
        return  resultSet.stream()
            .map(row -> TitleOrderInfo.builder()
                .customerCode((String)row[0])
                .fundCode((String)row[1])
                .productIdValue((String)row[2])
                .orderNumber((String)row[3])
                .build())
            .collect(Collectors.toList()).get(0);
    }
}
