package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SpydusLoginRequest {
    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("Password")
    private String password;

    @JsonProperty("NewPassword")
    private String newPassword;

    @JsonProperty("ConfPassword")
    private String confPassword;

    @JsonProperty("SpydusSSO")
    private String spydusSSO;

    @JsonProperty("SpydusSSOUTC")
    private String spydusSSOUTC;

    @JsonProperty("isForced")
    private boolean isForced;

    @JsonProperty("AuthKey")
    private String authKey;

    @JsonProperty("Location")
    private String location;

    @JsonProperty("SubLocation")
    private String subLocation;
}
