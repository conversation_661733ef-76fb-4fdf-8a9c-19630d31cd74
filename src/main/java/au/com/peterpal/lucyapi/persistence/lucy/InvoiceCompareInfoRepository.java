package au.com.peterpal.lucyapi.persistence.lucy;

import static org.springframework.util.Assert.notEmpty;
import static org.springframework.util.Assert.notNull;

import au.com.peterpal.lucyapi.model.InvoiceCompareInfo;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class InvoiceCompareInfoRepository {

  @PersistenceContext
  @Qualifier("lucyEM")
  private EntityManager em;

  public List<InvoiceCompareInfo> findInvoiceCompareInfo(List<String> invoiceIdList) {
    notNull(invoiceIdList, "invoiceIdList must not be null");
    notEmpty(invoiceIdList, "invoiceIdList must not be empty");

    String query = "SELECT " +
            "i.documentNum as documentNumber, " +
            "il.reference as reference, " +
            "il.description as description, " +
            "il.qty as quantity, " +
            "il.amountTax as amountTax, " +
            "il.amountNet as amountNet " +
        "FROM " +
            "dbo.vw_PostedInvoiceLines il " +
        "JOIN dbo.vw_PostedInvoices i on i.trxNum = il.trxNum " +
        "WHERE i.documentNum in (" + convert(invoiceIdList) + ")";

    log.debug(() -> String.format("Executing query %s", query));
    Query q = em.createNativeQuery(query);
    List<Object[]> resultSet = q.getResultList();
    return  resultSet.stream()
        .map(row -> {
            InvoiceCompareInfo ici =
            new InvoiceCompareInfo(
                (String)row[0],
                (String)row[1],
                (String)row[2],
                Integer.valueOf((int)Math.round((Double)row[3])),
                BigDecimal.valueOf((Double)row[4]),
                BigDecimal.valueOf((Double)row[5]));
            ici.setAmountTax(ici.getAmountTax().setScale(2, BigDecimal.ROUND_HALF_UP));
            ici.setAmountNet(ici.getAmountNet().setScale(2, BigDecimal.ROUND_HALF_UP));
            return ici;
        })
        .collect(Collectors.toList());
  }

  private String convert(List<String> invoiceIdList) {
    return String.join(",",
                        invoiceIdList.stream()
                            .map(s -> "'" + s + "'")
                            .collect(Collectors.toList()));
  }
}
