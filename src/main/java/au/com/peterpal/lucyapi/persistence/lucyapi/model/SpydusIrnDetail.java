package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import au.com.peterpal.lucyapi.utils.DateFormatUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class SpydusIrnDetail {
  private String setId;
  private int total;
  private int lastIrn;
  private boolean more;
  private List<Result> result;

  @JsonIgnoreProperties(ignoreUnknown = true)
  @Data
  @ToString
  public static class Result {
    private String inst;
    private boolean isDirty;
    private String irn;
    private String text;
    private String type;
    private String dateAcquired;

    public LocalDate getParsedDateAcquired(List<String> formatPatterns) {
      return DateFormatUtil.parseDate(this.dateAcquired, formatPatterns);
    }
  }
}
