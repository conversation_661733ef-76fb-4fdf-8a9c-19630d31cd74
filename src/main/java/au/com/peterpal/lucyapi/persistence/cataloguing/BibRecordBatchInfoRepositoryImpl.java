package au.com.peterpal.lucyapi.persistence.cataloguing;

import static org.springframework.util.StringUtils.hasText;

import au.com.peterpal.lucyapi.persistence.cataloguing.model.BiBRecordBatchInfo;
import au.com.peterpal.lucyapi.persistence.cataloguing.model.QBiBRecordBatchInfo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import java.util.List;
import javax.persistence.EntityManager;
import lucy.cataloguing.codes.BibRecordBatchStatus;
import lucy.cataloguing.codes.BibRecordType;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

@Repository
public class BibRecordBatchInfoRepositoryImpl implements BibRecordBatchInfoRepositoryCustom {

  private final EntityManager em;

  public BibRecordBatchInfoRepositoryImpl(@Qualifier("cataloguingEM") EntityManager em) {
    this.em = em;
  }

  @Override
  public List<BiBRecordBatchInfo> findBibRecordBatches(String customerCode, String customerType,
      BibRecordType bibType, BibRecordBatchStatus status) {
    JPAQueryFactory queryFactory = new JPAQueryFactory(em);
    QBiBRecordBatchInfo batchInfo = QBiBRecordBatchInfo.biBRecordBatchInfo;

    BooleanBuilder condition = new BooleanBuilder();
    if (hasText(customerCode) && hasText(customerType)) {
      condition.and(batchInfo.customerCode.eq(customerCode));
      condition.and(batchInfo.customerType.eq(customerType));
    }
    if (bibType != null) {
      condition.and(batchInfo.bibType.eq(bibType));
    }
    if (status != null) {
      condition.and(batchInfo.status.eq(status));
    }
    return queryFactory
        .selectFrom(batchInfo)
        .where(condition)
        .fetch();
  }
}
