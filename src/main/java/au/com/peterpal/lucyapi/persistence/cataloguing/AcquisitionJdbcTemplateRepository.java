package au.com.peterpal.lucyapi.persistence.cataloguing;

import au.com.peterpal.lucyapi.persistence.cataloguing.queries.AcquisitionQueries;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

@Repository
public class AcquisitionJdbcTemplateRepository {

    private final TransactionTemplate cataloguingTransactionTemplate;
    private final NamedParameterJdbcTemplate cataloguingJdbcTemplate;

    public AcquisitionJdbcTemplateRepository(TransactionTemplate cataloguingTransactionTemplate,
                                             NamedParameterJdbcTemplate cataloguingJdbcTemplate) {
        this.cataloguingTransactionTemplate = cataloguingTransactionTemplate;
        this.cataloguingJdbcTemplate = cataloguingJdbcTemplate;
    }
    public void removeAcquisitions(List<Integer> acquisitionPks) {
        cataloguingTransactionTemplate.execute(status -> {
            SqlParameterSource parameters = new MapSqlParameterSource("acquisitionPks", acquisitionPks);

            cataloguingJdbcTemplate.update(AcquisitionQueries.DELETE_COPIES_BY_ACQUISITION_PK, parameters);
            cataloguingJdbcTemplate.update(AcquisitionQueries.DELETE_ALLOCATION_BY_ACQUISITION_PK, parameters);
            cataloguingJdbcTemplate.update(AcquisitionQueries.DELETE_RECEIPTS_BY_ACQUISITION_PK, parameters);
            cataloguingJdbcTemplate.update(AcquisitionQueries.DELETE_ACQUISITION_BY_PK, parameters);
            return null;
        });

    }

    public List<Integer> findAcquisitionPkByWorkOrderNumber(String workOrderNumber) {
        SqlParameterSource parameters = new MapSqlParameterSource("workOrderNumber", workOrderNumber);

        return cataloguingJdbcTemplate.query(AcquisitionQueries.FIND_ACQUISITION_PK_BY_WORK_ORDER_NUMBER, parameters,
            (resultSet, i) -> resultSet.getInt("ACQUISITION_PK"));
    }

}
