package au.com.peterpal.lucyapi.persistence.cataloguing;

import au.com.peterpal.lucyapi.persistence.cataloguing.model.BiBRecordBatchInfo;
import java.util.List;
import lucy.cataloguing.codes.BibRecordBatchStatus;
import lucy.cataloguing.codes.BibRecordType;

public interface BibRecordBatchInfoRepositoryCustom {

  List<BiBRecordBatchInfo> findBibRecordBatches(String customerCode, String customerType, BibRecordType bibType, BibRecordBatchStatus status);
}
