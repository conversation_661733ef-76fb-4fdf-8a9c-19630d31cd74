package au.com.peterpal.lucyapi.persistence.lucy;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.utils.StringAffirm;
import java.util.Map;
import javax.sql.DataSource;
import lombok.extern.log4j.Log4j2;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class LucyHelperRepository {

  private static final String SP_PROCESS_TO = "sp_ProcessProfileTitleOrder";
  private static final String PO_MSG_CODE_INVBO = "INV/BO";
  private static final String PO_PRIORITY_NORMAL = "NORMAL";
  private static final String PN_TITLE_ORDER = "titleOrderNum";
  private static final String PN_PO_MSG_CODE = "poMessageCode";
  private static final String PN_PO_PRIORITY_CODE = "poPriorityCode";
  public static final String PN_ERROR_CODE = "errorCode";
  public static final int EC_MORE_THAN_ONE_TO = 13201;
  public static final int EC_NULL_SUPPLIER_CODE = 13202;
  public static final int EC_ADD_PO_ERROR = 13203;
  public static final int EC_UPDATE_TO_ERROR = 13204;
  public static final String EM_NULL_SUPPLIER_CODE = "errorCode: %d - supplierCode is NULL for TO number %s";
  public static final String EM_MORE_THAN_ONE_TO = "errorCode: %d - selected more than one TO for number %s";
  public static final String EM_ADD_PO_ERROR = "errorCode: %d - error executing sp_AddMessageToPOHdr/sp_InsertPOLineFromTitleOrder/sp_IncrementPOLine for TO number %s";
  public static final String EM_UPDATE_TO_ERROR = "errorCode: %d - error updating TO number %s";

  private final DataSource lucyDataSource;

  public LucyHelperRepository(DataSource lucyDataSource) {
    this.lucyDataSource = lucyDataSource;
  }

  public void processTitleOrder(String titleOrderNumber) {
    if (!StringAffirm.of(titleOrderNumber).hasText()) {
      log.warn(() -> "Skipping title order because it is null or empty");
      return;
    }
    String ton = titleOrderNumber.trim();
    if (ton.contains(" ")) {
      log.warn(() -> String.format("Skipping '%s', because contains space character", ton));
      return;
    }
    log.info(() -> String.format("Processing TO number %s", ton));

    SimpleJdbcCall simpleJdbcCall = newSimpleJdbcCall();
    simpleJdbcCall.withProcedureName(SP_PROCESS_TO);

    MapSqlParameterSource sqlParameterSource = new MapSqlParameterSource()
      .addValue(PN_TITLE_ORDER, ton)
      .addValue(PN_PO_MSG_CODE, PO_MSG_CODE_INVBO)
      .addValue(PN_PO_PRIORITY_CODE, PO_PRIORITY_NORMAL);

    Map<String, Object> out = simpleJdbcCall.execute(sqlParameterSource);
    handleResponse(out.get(PN_ERROR_CODE), ton);
  }

  public SimpleJdbcCall newSimpleJdbcCall() {
    return new SimpleJdbcCall(lucyDataSource);
  }

  private void handleResponse(Object errorCode, String titleOrderNumber) {
    if (errorCode instanceof Integer) {
      Integer err = (Integer)errorCode;
      switch (err.intValue()) {
        case 0: break;

        case EC_NULL_SUPPLIER_CODE:
          throw new BusinessException(String.format(EM_NULL_SUPPLIER_CODE, EC_NULL_SUPPLIER_CODE, titleOrderNumber));

        case EC_MORE_THAN_ONE_TO:
          throw new BusinessException(String.format(EM_MORE_THAN_ONE_TO, EC_MORE_THAN_ONE_TO, titleOrderNumber));

        case EC_ADD_PO_ERROR:
          throw new BusinessException(String.format(EM_ADD_PO_ERROR, EC_ADD_PO_ERROR, titleOrderNumber));

        case EC_UPDATE_TO_ERROR:
          throw new BusinessException(String.format(EM_UPDATE_TO_ERROR, EC_UPDATE_TO_ERROR, titleOrderNumber));

        default:
          throw new BusinessException((String.format("Unknown error %d", err.intValue())));
      }
    } else {
      throw new BusinessException("Invalid state: error code must be integer.");
    }
  }
}
