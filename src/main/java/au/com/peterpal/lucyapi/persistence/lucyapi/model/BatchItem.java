package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Wither;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Wither
@Table(name = "batch_items",
    uniqueConstraints = {@UniqueConstraint(name = "batch_items_un", columnNames = {"work_order_number", "item_barcode"})})
public class BatchItem {
    @NonNull
    @NotNull
    @Id
    @Column(columnDefinition = "uuid", updatable = false)
    private UUID id;
    @NonNull
    @NotNull
    @Column(name = "batch_id")
    private Integer batchId;
    @NonNull
    @NotNull
    @Column(name = "work_order_number")
    private String workOrderNumber;
    @NonNull
    @NotNull
    @Column(name = "item_barcode")
    private String itemBarcode;

    @Column(name = "full_bib_records_created")
    private boolean fullBibRecordsCreated;

    @Column(name = "invoice_group_id")
    private UUID invoiceGroupId;
    @Column(name = "invoice_id")
    private UUID invoiceId;
}
