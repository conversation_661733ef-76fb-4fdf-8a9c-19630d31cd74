package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "collection_type_by_fund")
@IdClass(CollectionTypeByFundKey.class)
public class CollectionTypeByFund {

    @Id
    @Column(name = "customer_code")
    private String customerCode;

    @Id
    private String fund;

    private String collection;

    @Column(name = "reduce_quantity_to_one")
    private boolean reduceQuantityToOne;

    @Column(name = "reduce_labels_to_one")
    private boolean reduceLabelsToOne;

    @Column(name = "print_labels")
    private boolean printLabels;
}
