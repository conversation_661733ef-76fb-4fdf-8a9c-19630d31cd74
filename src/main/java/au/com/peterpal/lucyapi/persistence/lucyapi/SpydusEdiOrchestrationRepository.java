package au.com.peterpal.lucyapi.persistence.lucyapi;

import au.com.peterpal.lucyapi.core.service.orchestration.dto.WorkflowStepStatus;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface SpydusEdiOrchestrationRepository
    extends JpaRepository<SpydusEdiOrchestration, UUID> {
  List<SpydusEdiOrchestration> findAllByAllInvoicePostedIsFalse();

  List<SpydusEdiOrchestration> findByInvoiceGroupIdAndWorkflowStepStatus(
      UUID invoiceGroupId, WorkflowStepStatus inProgress);

  List<SpydusEdiOrchestration> findByInvoiceGroupIdAndWorkflowStepStatusIn(
      UUID invoiceGroupId, List<WorkflowStepStatus> statuses);
}
