package au.com.peterpal.lucyapi.persistence.lucyapi;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.CollectionTypeByFund;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.CollectionTypeByFundKey;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CollectionTypeByFundRepository extends JpaRepository<CollectionTypeByFund, CollectionTypeByFundKey>,
    CollectionTypeByFundRepositoryCustom {
}
