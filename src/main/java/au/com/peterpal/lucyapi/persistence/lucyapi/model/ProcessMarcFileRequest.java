package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ProcessMarcFileRequest {
    @JsonProperty(value = "IsBulkEdit")
    private boolean isBulkEdit;
    @JsonProperty(value = "LoadControl")
    private String loadControl;
    @JsonProperty(value = "LoadType")
    private int loadType;
    @JsonProperty(value = "SessionId")
    private String sessionId;
    @JsonProperty(value = "RecordIds")
    private List<String> recordIds;
    @JsonProperty(value = "MarcSpecId")
    private String marcSpecId;
}
