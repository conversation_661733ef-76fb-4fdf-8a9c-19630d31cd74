package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BatchMarcsBriefRequest {
    @JsonProperty(value = "SelectedLoadMarcs")
    private String selectedLoadMarcs;
    @JsonProperty(value = "Start")
    private Integer start;
    @JsonProperty(value = "AllTheRest")
    private Boolean allTheRest;
    @JsonProperty(value = "NumberOfRecords")
    private Integer numberOfRecords;
    @JsonProperty(value = "Filename")
    private String filename;
    @JsonProperty(value = "PageSize")
    private Integer pageSize;
    @JsonProperty(value = "SessionId")
    private String sessionId;
}
