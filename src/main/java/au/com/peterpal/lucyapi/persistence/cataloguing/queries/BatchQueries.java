package au.com.peterpal.lucyapi.persistence.cataloguing.queries;

public class BatchQueries {

    public static final String GET_PRODUCT_ID_VALUE_BY_BATCH_ID = "SELECT brb.pk," +
        " brb.customeridvalue as customer_code, h.prodidvalue FROM bibrecordbatch brb\n" +
        "INNER JOIN bibrecordbatch_bibrecords brb_br on brb.pk = brb_br.bibrecordbatch_pk\n" +
        "INNER JOIN bibrecord br on brb_br.bibrecord_pk = br.pk\n" +
        "INNER JOIN public.bibcoll_suppbibrecords bs on br.pk = bs.bibrecord_pk\n" +
        "INNER JOIN bibcoll bc on bs.bibcoll_pk = bc.pk\n" +
        "INNER JOIN holding h on bc.pk = h.bibcoll_pk\n" +
        "where brb.pk=:batchId";
}
