package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Wither;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Wither
@Table(
    name = "printer",
    uniqueConstraints = {@UniqueConstraint(name = "label_idx", columnNames = "label")})
public class Printer {
  @NonNull @NotNull
  @Id
  @Column(columnDefinition = "uuid", updatable = false)
  private UUID id;
  @NonNull @NotNull private String host;
  @NonNull @NotNull private String label;
}
