package au.com.peterpal.lucyapi.persistence.lucyapi.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Wither;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Wither
@Table(name = "separate_batch_by_holding",
    uniqueConstraints = {@UniqueConstraint(name = "separate_batch_by_holding_un", columnNames = {"customer_code", "holding_field", "holding_value"})})
public class SeparateBatchByHolding {
  @NonNull @NotNull
  @Id
  @Column(columnDefinition = "uuid", updatable = false)
  private UUID id;
  @NonNull @NotNull
  @Column(name = "customer_code")
  private String customerCode;
  @NonNull @NotNull
  @Column(name = "batch_code")
  private String batchCode;
  @NonNull @NotNull
  @Column(name = "holding_field")
  private String holdingField;
  @NonNull @NotNull
  @Column(name = "holding_value")
  private String holdingValue;
  private String location;
  @Column(name = "print_on_branch_slip")
  private Boolean printOnBranchSlip = false;
}
