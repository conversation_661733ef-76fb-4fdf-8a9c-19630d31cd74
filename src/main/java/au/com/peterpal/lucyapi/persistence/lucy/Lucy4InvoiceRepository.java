package au.com.peterpal.lucyapi.persistence.lucy;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Log4j2
@Repository
public class Lucy4InvoiceRepository {

    @PersistenceContext
    @Qualifier("lucyEM")
    private EntityManager em;

    public Set<String> checkInvoicePosted(Set<String> lucy4InvoiceNos) {
        String query = "select distinct documentNum from dbo.ChgTrx" +
            " WHERE documentNum in :lucy4InvoiceNos";

        log.debug(() -> String.format("Executing query %s", query));
        Query q = em.createNativeQuery(query);
        q.setParameter("lucy4InvoiceNos", lucy4InvoiceNos);
        List<String> resultSet = q.getResultList();
        return new HashSet<>(resultSet);
    }
}
