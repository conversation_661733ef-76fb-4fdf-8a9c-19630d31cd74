package au.com.peterpal.lucyapi.persistence.lucyapi;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.SeparateBatchByHolding;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface SeparateBatchByHoldingRepository extends JpaRepository<SeparateBatchByHolding, UUID> {
    List<SeparateBatchByHolding> findAllByCustomerCode(String customerCode);

    List<SeparateBatchByHolding> findAllByCustomerCodeAndPrintOnBranchSlip(String customerCode, boolean printOnBranchSlip);
}
