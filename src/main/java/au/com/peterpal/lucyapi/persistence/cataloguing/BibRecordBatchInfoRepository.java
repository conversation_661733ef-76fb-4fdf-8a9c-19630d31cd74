package au.com.peterpal.lucyapi.persistence.cataloguing;

import au.com.peterpal.lucyapi.persistence.cataloguing.model.BiBRecordBatchInfo;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BibRecordBatchInfoRepository extends
    JpaRepository<BiBRecordBatchInfo, Integer>,  BibRecordBatchInfoRepositoryCustom {

  List<BiBRecordBatchInfo> findByCustomerCodeAndCustomerType(String customerCode, String customerType);
}
