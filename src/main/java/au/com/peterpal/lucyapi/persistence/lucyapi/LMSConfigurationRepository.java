package au.com.peterpal.lucyapi.persistence.lucyapi;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.LMSConfiguration;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.LMSType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LMSConfigurationRepository extends JpaRepository<LMSConfiguration, String> {
    List<LMSConfiguration> findAllByLmsTypeAndAutomaticallyCloseBatchesIsTrue(LMSType spydus);

    Optional<LMSConfiguration> findByCustomerCodeAndLmsType(String customerCode, LMSType lmsType);

    Optional<LMSConfiguration> findByCustomerCode(String customerCode);
}
