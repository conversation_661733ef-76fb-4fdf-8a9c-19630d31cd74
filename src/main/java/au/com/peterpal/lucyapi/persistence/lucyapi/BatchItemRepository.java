package au.com.peterpal.lucyapi.persistence.lucyapi;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.BatchItem;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;

public interface BatchItemRepository extends JpaRepository<BatchItem, UUID> {
  List<BatchItem> findAllByWorkOrderNumber(String workOrderNumber);

  List<BatchItem> findAllByInvoiceIdIn(Set<UUID> invoiceIds);

  List<BatchItem> findAllByWorkOrderNumberIn(Set<String> workOrders);

  @Modifying
  @Query("DELETE FROM BatchItem b WHERE b.itemBarcode = :barcode AND b.invoiceId IN :invoiceIds")
  @Transactional
  void deleteByItemBarcodeAndInvoiceIdIn(
      @Param("barcode") String barcode, @Param("invoiceIds") Set<UUID> invoiceIds);
}
