package au.com.peterpal.lucyapi.persistence.cataloguing.model;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "copy")
@Data
public class Copy {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "pk")
    private Integer pk;

    @Version
    @Column(name = "optlock")
    private Integer optLock;

    @Column(name = "dateentered", nullable = false)
    private Date dateEntered;

    @Column(name = "enteredby")
    private String enteredBy;

    @Column(name = "entryrecordsourcetype")
    private String entryRecordSourceType;

    @Column(name = "entryrecordsourcename")
    private String entryRecordSourceName;

    @Column(name = "entrydatadate")
    private Date entryDataDate;

    @Column(name = "datelastmodified", nullable = false)
    private Date dateLastModified;

    @Column(name = "lastmodifiedby")
    private String lastModifiedBy;

    @Column(name = "lastrecordsourcetype")
    private String lastRecordSourceType;

    @Column(name = "lastrecordsourcename")
    private String lastRecordSourceName;

    @Column(name = "lastdatadate")
    private Date lastDataDate;

    @Column(name = "copyindex")
    private Integer copyIndex;

    @Column(name = "barcodenumber")
    private String barcodeNumber;

    @Column(name = "rfidnumber")
    private String rfidNumber;

    @Column(name = "branchcode")
    private String branchCode;

    @Column(name = "cutter")
    private String cutter;

    @Column(name = "callnumber")
    private String callNumber;

    @Column(name = "spinelabel")
    private String spineLabel;

    @Column(name = "genre")
    private String genre;

    @Column(name = "itemtypecode")
    private String itemTypeCode;

    @Column(name = "collectioncode")
    private String collectionCode;

    @Column(name = "categorycode1")
    private String categoryCode1;

    @Column(name = "categorycode2")
    private String categoryCode2;

    @Column(name = "notes")
    private String notes;

    @Column(name = "dewey")
    private String dewey;

    @Column(name = "spinelabelprefix")
    private String spineLabelPrefix;

    @Column(name = "entryrecordsourcereference")
    private String entryRecordSourceReference;

    @Column(name = "lastrecordsourcereference")
    private String lastRecordSourceReference;

    @Column(name = "acquisition_pk")
    private Integer acquisitionPk;

}

