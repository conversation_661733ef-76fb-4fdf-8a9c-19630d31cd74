package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.model.NewIncompleteBibRecordMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.jms.dsl.Jms;

import javax.jms.ConnectionFactory;
import javax.jms.Session;


@Log4j2
@Configuration
@EnableIntegration
public class AddNewIncompleteBibRecordListenerConfig {

    private final ConnectionFactory connectionFactory;
    private final HoldingService holdingService;
    private final String addNewIncompleteBibRecordQueueName;

    public AddNewIncompleteBibRecordListenerConfig(
        ConnectionFactory connectionFactory,
        HoldingService holdingService,
        @Value("${accessioning.add-new-incomplete-bib-record.queue.name:work-orders:lucy-api.add-new-incomplete-bib-record}")
            String addNewIncompleteBibRecordQueueName) {
        this.connectionFactory = connectionFactory;
        this.holdingService = holdingService;
        this.addNewIncompleteBibRecordQueueName = addNewIncompleteBibRecordQueueName;
    }

    @Bean
    public IntegrationFlow addNewIncompleteBibRecordEventsJmsListener() {
        return IntegrationFlows.from(
                Jms.messageDrivenChannelAdapter(this.connectionFactory)
                    .destination(addNewIncompleteBibRecordQueueName)
                    .configureListenerContainer(
                        spec ->
                            spec.sessionTransacted(true)
                                .sessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE)))
            .enrichHeaders(
                headerEnricherSpec ->
                    headerEnricherSpec.headerFunction(
                        IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID,
                        message ->
                            IncomingMessageProcessor.generateIncomingMessageId(message.getPayload()),
                        true))
            .wireTap(IncomingMessageLoggingControlFlow.LOG_INCOMING_MESSAGE_CHANNEL)
            .log(m -> "Received via JMS: " + m)
            .handle(NewIncompleteBibRecordMessage.class, (p, m) -> holdingService.addNewIncompleteBibRecord(p))
            .log(message -> "handled: " + message)
            .get();
    }

}
