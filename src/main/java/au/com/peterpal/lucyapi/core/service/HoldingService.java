package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.batch.control.BatchService;
import au.com.peterpal.lucyapi.model.*;
import au.com.peterpal.lucyapi.persistence.lucy.TitleOrderArcRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.CollectionTypeByFundRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.GenreLabelRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.LMSConfigurationRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.SeparateBatchByHoldingRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.*;
import au.com.peterpal.lucyapi.utils.AllocationSelectingUtil;
import com.google.common.collect.Lists;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import javax.validation.constraints.NotNull;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.codes.CurrencyCode;
import lucy.catalogue.codes.OrganisationIdentifierTypeCode;
import lucy.catalogue.codes.ProductIdentifierTypeCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.catalogue.entity.ProductIdentifier;
import lucy.cataloguing.CataloguingException;
import lucy.cataloguing.DuplicateBarcodeException;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.codes.AllocationStatus;
import lucy.cataloguing.codes.BibCollectionType;
import lucy.cataloguing.codes.BibRecordStatus;
import lucy.cataloguing.entity.*;
import lucy.cataloguing.entity.BibRecord;
import lucy.common.NotFoundException;
import lucy.fulfillment.codes.TitleOrderAllocationStatus;
import lucy.fulfillment.entity.ItemReceiptAssignment;
import lucy.fulfillment.entity.OpenTitleOrder;
import lucy.fulfillment.entity.OpenTitleOrderAllocation;
import lucy.marc.MarcException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@AllArgsConstructor
public class HoldingService {

    private RemoteCataloguing remoteCataloguing;
  private TitleOrderService titleOrderService;
  private final BibRecordService bibRecordService;
  private final BatchService batchService;
  private final BibTemplateService bibTemplateService;

  private final CollectionTypeByFundRepository collectionTypeByFundRepository;
  private final SeparateBatchByHoldingRepository separateBatchByHoldingRepository;
  private final LMSConfigurationRepository lmsConfigurationRepository;
  private final MessageChannel printLabelChannel;
  private final BatchItemService batchItemService;

  private final GenreLabelRepository genreLabelRepository;

  private final TitleOrderArcRepository titleOrderArcRepository;
  private final WorkOrderService workOrderService;

  public Holding addHolding(
      OrganisationIdentifier customerId, BibCollectionType bibCollectionType, Holding holding) {
    try {
      return remoteCataloguing.addHolding(customerId, bibCollectionType, holding);
    } catch (NotFoundException e) {
      String message = String.format("Could not add holding with pk %s", holding.getPk());
      log.error(message, e);
      throw new ResourceNotFoundException(message);
    }
  }

  public Holding addHolding(String customerCode, BibCollectionType bibCollectionType, Holding holding) {
    OrganisationIdentifier organisationId = new OrganisationIdentifier();
    organisationId.setOrganisationIdType(OrganisationIdentifierTypeCode.PPLS);
    organisationId.setOrganisationIdValue(customerCode);
    return addHolding(organisationId, bibCollectionType, holding);
  }

  public List<Holding> getHoldings(OrganisationIdentifier customerId, ProductIdentifier productId) {
    return remoteCataloguing.getHoldings(customerId, productId);
  }

  public List<Holding> getHoldings(String customerCode, String productIdValue) {
    OrganisationIdentifier organisationId = new OrganisationIdentifier();
    organisationId.setOrganisationIdType(OrganisationIdentifierTypeCode.PPLS);
    organisationId.setOrganisationIdValue(customerCode);

    return tryToGetHolding(productIdValue, organisationId);
  }

  private List<Holding> tryToGetHolding(String productIdValue, OrganisationIdentifier organisationId) {
    List<Holding> holdings = getHoldings(productIdValue, organisationId, ProductIdentifierTypeCode.EAN);
    if (holdings.isEmpty() && productIdValue != null && productIdValue.length() < 13) {
      holdings = getHoldings(String.format("0%s", productIdValue), organisationId, ProductIdentifierTypeCode.EAN);
    }
    if (holdings.isEmpty()) {
      holdings = getHoldings(productIdValue, organisationId, ProductIdentifierTypeCode.PROP);
    }
    if (holdings.isEmpty() && productIdValue != null && productIdValue.length() < 13) {
      holdings = getHoldings(String.format("0%s", productIdValue), organisationId, ProductIdentifierTypeCode.PROP);
    }
    return holdings;
  }

  private List<Holding> getHoldings(String productIdValue,
                                    OrganisationIdentifier organisationId,
                                    ProductIdentifierTypeCode type) {
    ProductIdentifier productId = new ProductIdentifier();
    productId.setProductIdentifierType(type);
    productId.setProductIdentifierValue(productIdValue);
    return getHoldings(organisationId, productId);
  }

  private String getCollectionType(String customerCode, String fundCode) {
    CollectionTypeByFundKey collectionTypeByFundKey = CollectionTypeByFundKey.of(customerCode, fundCode);
    Optional<CollectionTypeByFund> collectionTypeByFund = collectionTypeByFundRepository.findById(collectionTypeByFundKey);

    // if a different collection type applies then use it otherwise default to print_std
    return collectionTypeByFund.map(ctf -> ctf.getCollection().toLowerCase()).orElse("print_std");
  }

  private boolean isReduceQuantityToOne(String customerCode, String fundCode) {
    CollectionTypeByFundKey collectionTypeByFundKey = CollectionTypeByFundKey.of(customerCode, fundCode);

    return collectionTypeByFundRepository.findById(collectionTypeByFundKey).map(
        CollectionTypeByFund::isReduceQuantityToOne).orElseGet(() -> false);
  }


  public Holding getHolding(String workOrderNumber) {
    TitleOrderInfo titleOrder = titleOrderService.getTitleOrderOpenOrClose(workOrderNumber);
    // not try to correct collection type for printBranchSlips or printSummarySlip
    return getHolding(workOrderNumber, titleOrder, titleOrder.getFundCode(), false, false);
  }

  public Holding getHolding(
      String workOrderNumber,
      TitleOrderInfo titleOrder,
      String fundCode,
      boolean correctCollectionType, boolean searchTitleClosed) {
    String customerCode = titleOrder.getCustomerCode();
    String productIdValue = titleOrder.getProductIdValue();

    List<Holding> printStdHoldings =
        getPrintStdHoldings(
            titleOrder,
            customerCode,
            productIdValue,
            getCollectionType(customerCode, fundCode),
            correctCollectionType);

    if (printStdHoldings.isEmpty()) {
      String itemReceiptLineProductId =
          titleOrderService
              .getItemReceiptAssignment(workOrderNumber, searchTitleClosed)
              .getItemReceiptLine()
              .getProductId()
              .getProductIdentifierValue();

      log.info(
          "Not get holding by title order product Id {}, try to get by item receipt line product id {}",
          productIdValue,
          itemReceiptLineProductId);
      printStdHoldings =
          getPrintStdHoldings(
              titleOrder,
              customerCode,
              itemReceiptLineProductId,
              getCollectionType(customerCode, fundCode),
              correctCollectionType);
    }
    if (printStdHoldings.isEmpty()) {
      String errorMessage = String.format("Holding for %s Not Found", workOrderNumber);
      log.error(errorMessage);
      throw new ResourceNotFoundException(errorMessage);
    }
    Holding holding;
    if (printStdHoldings.size() == 1) {
      holding = printStdHoldings.get(0);
    } else {
      String titleOrderOrderNumber = titleOrder.getOrderNumber();
      holding = getHoldingFromMultiple(workOrderNumber, printStdHoldings, titleOrderOrderNumber);
    }
    log.debug("Found holding {} for {}", logHolding(holding), workOrderNumber);
    return holding;
  }

  private static Holding getHoldingFromMultiple(
      String workOrderNumber, List<Holding> printStdHoldings, String titleOrderOrderNumber) {
    return printStdHoldings.stream()
        .filter(
            h ->
                h.getAcquisitions().stream()
                    .anyMatch(
                        a ->
                            a.getOrderId().getTransactionIdValue().equals(titleOrderOrderNumber)
                                && a.getReceipts().stream()
                                    .anyMatch(
                                        r -> r.getOrderAssignmentId().equals(workOrderNumber))))
        .findAny()
        .orElseGet(
            () ->
                printStdHoldings.stream()
                    .max(Comparator.comparing(h -> h.getEntryRecord().getDateModified()))
                    .orElseThrow(
                        () ->
                            new ResourceNotFoundException(
                                String.format("Holding for %s Not Found", workOrderNumber))));
  }

  private String logHolding(Holding holding) {
    return String.format(
        "Pk: %s, Acquisitions: %s, receiptOrderAssignmentIds: %s ",
        holding.getPk(),
        holding.getAcquisitions(),
        holding.getAcquisitions().stream()
            .map(Acquisition::getReceipts)
            .flatMap(List::stream)
            .map(Receipt::getOrderAssignmentId)
            .collect(Collectors.joining(", ")));
  }

  private List<Holding> getPrintStdHoldings(TitleOrderInfo titleOrder,
                                            String customerCode,
                                            String productIdValue,
                                            String collectionType,
                                            boolean correctCollectionType) {
    List<Holding> holdings = getHoldings(customerCode, productIdValue);
    log.debug(() -> String.format("Holdings are: %s", holdings.stream().map(h -> h.getPk() + " " + h.getBibCollection().getCollectionType().getCode()).collect(Collectors.joining(", "))));

    log.debug(() -> String.format("Title Order %s has a customer code of %s and a fund code of %s", titleOrder.getOrderNumber(), titleOrder.getCustomerCode(), titleOrder.getFundCode()));


    List<Holding> printStdHoldings = holdings.stream().filter(h -> h.getBibCollection().getCollectionType().getCode().equals(collectionType)).collect(Collectors.toList());
    if (printStdHoldings.isEmpty() && correctCollectionType) {

      Optional<Holding> holdingHasWrongCollection = holdings.stream().filter(holding ->
              !holding.getAcquisitions().isEmpty()
                  && holding.getAcquisitions().stream()
                  .anyMatch(acquisition -> acquisition.getOrderId().getTransactionIdValue().equals(titleOrder.getOrderNumber())))
          .findFirst();

      if (holdingHasWrongCollection.isPresent()) {

        Holding holding = holdingHasWrongCollection.get();
        return correctCollectionTypeForHolding(collectionType, holding);
      } else {
        List<Holding> eligibleWrongCollections = holdings.stream().filter(holding ->
                holding.getAcquisitions().isEmpty()
                    && holding.getBibCollection().getCollectionType() != BibCollectionType.ONORDER)
            .collect(Collectors.toList());
        if (eligibleWrongCollections.size() == 1) {
          Holding holding = eligibleWrongCollections.get(0);
          return correctCollectionTypeForHolding(collectionType, holding);
        } else {
          log.warn("Cannot correct collection type for holdings without acquisitions that have " +
                  "{} records of eligible holding with wrong collection!",
              eligibleWrongCollections.size());
        }
      }
    }
    log.debug(() -> String.format("Holding is: %s", printStdHoldings.stream().map(h -> h.getPk() + " ")
        .collect(Collectors.joining(", "))));
    return printStdHoldings;
  }

  private ArrayList<Holding> correctCollectionTypeForHolding(String collectionType, Holding holding) {
    CustomerCollection customerCollection = holding.getBibCollection().getCustomerCollection();
    try {
      customerCollection = remoteCataloguing.getCustomerCollection(customerCollection.getPk());
    } catch (NotFoundException e) {
      String message = String.format("Could not get customerCollection with pk %s", customerCollection.getPk());
      log.error(message, e);
      throw new ResourceNotFoundException(message);
    }

    Optional<BibCollection> bibCollectionOptional =
        customerCollection.getBibCollections().values().stream()
            .filter(bc -> bc.getCollectionType().getCode().equals(collectionType))
            .findAny();

    if (bibCollectionOptional.isPresent()) {
      try {
        log.info(
            "Moving holding with pk {} to collection with pk that have collection type {}",
            holding.getPk(),
            collectionType);
        BibCollection newBibCollection = remoteCataloguing.getBibCollection(bibCollectionOptional.get().getPk());
        BibCollection oldBibCollection = remoteCataloguing.getBibCollection(holding.getBibCollection().getPk());

        holding.setBibCollection(newBibCollection);

        oldBibCollection.getHoldings().removeIf(h -> h == null || h.getPk() == holding.getPk());
        newBibCollection.getHoldings().add(holding);


        remoteCataloguing.updateBibCollection(oldBibCollection);
        remoteCataloguing.updateBibCollection(newBibCollection);

        return Lists.newArrayList(holding);
      } catch (NotFoundException e) {
        String message = String.format("Could not get BibCollection with pk %s", bibCollectionOptional.get().getPk());
        log.error(message, e);
        throw new ResourceNotFoundException(message);
      }
    }
    BibCollection bibCollection = holding.getBibCollection();

    log.info("Found holding with pk {} has wrong collection. Updating collection type from {} to {}",
        holding.getPk(), bibCollection.getCollectionType(), collectionType);
    bibCollection.setCollectionType(BibCollectionType.mapCode(collectionType));
    remoteCataloguing.updateBibCollection(bibCollection);
    return Lists.newArrayList(holding);
  }

  public List<Allocation> getAutomaticAllocations(
      Acquisition acquisition, BibTemplate bibTemplate) {
    try {
      return remoteCataloguing.getAutomaticAllocations(acquisition, bibTemplate);
    } catch (MarcException e) {
      String message = "Could not get automatic allocations";
      log.error(message, e);
      throw new BusinessException(message);
    }
  }

  public Map<String, Label> getLabelDefinitions(BibTemplate bibTemplate) throws CataloguingException {
    return remoteCataloguing.getLabelDefinitions(bibTemplate);
  }

  public void updateCopy(Copy copy) throws DuplicateBarcodeException {
    remoteCataloguing.updateCopy(copy);
  }

  public void createFullBibRecordWithStoreBarcodes(String workOrderNumber, UUID invoiceGroupId, UUID invoiceId, boolean removeCancelledBarcodes) {
    TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
    FullBibRecordCreatedResult result = createFullBibRecord(workOrderNumber, removeCancelledBarcodes, titleOrder, false, Sets.newHashSet());
    Set<String> barCodes = result.getAcquisitions().stream()
        .map(Acquisition::getCopies)
        .flatMap(Collection::stream)
        .map(Copy::getBarCodeNumber)
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
    batchItemService.storeBarcodes(workOrderNumber, result.getBatchId(), barCodes, true, invoiceGroupId, invoiceId);
  }

  public boolean isSpydusEdiCustomer(String workOrderNumber){
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    Optional<LMSConfiguration> lmsConfiguration = lmsConfigurationRepository.findById(customerCode);
    return lmsConfiguration.isPresent() && BooleanUtils.isTrue(lmsConfiguration.get().getSpydusEDI());
  }

  public FullBibRecordCreatedResult createFullBibRecord(
      String workOrderNumber,
      boolean removeCancelledBarcodes,
      TitleOrderInfo titleOrder,
      boolean searchTitleClosed,
      Set<Integer> batchIds) {
    // Create Full Bib Record ... (CreateFullBibRecordHandler.java -> CreateFullBibRecordDialog.java)
    // createFullBibRecords(bibCollection, holdings, acquisitions, bibTemplates, baseBibRecord)
    // Select Base Record

    Holding holding = getHolding(workOrderNumber, titleOrder, titleOrder.getFundCode(), true, searchTitleClosed);
    String customerCode = titleOrder.getCustomerCode();
    String titleOrderNumber = titleOrder.getOrderNumber();

    List<BibTemplate> bibTemplates = getBibTemplates(customerCode);
    log.debug(() -> String.format("Bib Templates are: %s",bibTemplates.stream().map(b -> b.getPk()+"").collect(Collectors.joining(", "))));

    // Get Supplied Bib Records
    List<BibRecord> bibRecords = bibRecordService.getSuppliedBibRecords(holding.getBibCollection().getPk());
    log.debug(() -> String.format("Supplied Bib Records are: %s",bibRecords.stream().map(b -> b.getPk()+"").collect(Collectors.joining(", "))));

    // Determine Base Bib Records
    BibRecord baseBibRecord = bibRecordService.getBaseBibRecord(bibRecords);
    log.debug(() -> String.format("Chosen Base Bib Record is: %s",baseBibRecord.getPk()));

    // Handle multiple acquisitions
    List<Acquisition> acquisitions = holding.getAcquisitions().stream()
        // Acquisition is for titleOrderNumber & workOrderNumber
        .filter(a -> a.getOrderId().getTransactionIdValue().equals(titleOrderNumber)
            && a.getReceipts().stream().anyMatch(r -> r.getOrderAssignmentId().equals(workOrderNumber)))
        .collect(Collectors.toList());
    if (acquisitions.isEmpty()) {
      throw new BusinessException("Cannot find any acquisitions");
    }

    // Create Full Bib Record
    List<BibRecord> fullBibRecords = bibRecordService.createFullBibRecord(holding.getBibCollection(),
            Collections.singletonList(holding), acquisitions, bibTemplates, baseBibRecord);
    BibRecord fullBibRecord = fullBibRecords.get(0);

    // Mark Complete (on holding)
    bibRecordService.updateStatus(fullBibRecord, BibRecordStatus.COMPLETE);

    List<String> separateBatchCodes = getSeparateBatchCodes(customerCode, holding);
    Integer batchId;
    if (separateBatchCodes.isEmpty()) {
      // Send to Outbox (Add to a new batch / Add to existing batch) - Lucy Client -
      // SendToOutboxHandler
      batchId = batchService.addToNewOrExistingBatch(customerCode, fullBibRecord, batchIds);
    } else {
      batchId =
          batchService.addToSeparateBatch(
              customerCode, fullBibRecord, separateBatchCodes, batchIds);
    }
    if (removeCancelledBarcodes) {
      Set<String> barCodes =
          acquisitions.stream()
              .map(Acquisition::getCopies)
              .flatMap(Collection::stream)
              .map(Copy::getBarCodeNumber)
              .filter(Objects::nonNull)
              .collect(Collectors.toSet());
      removeCancelledItems(workOrderNumber, fullBibRecord.getPk(), barCodes);
    }
    return FullBibRecordCreatedResult.builder()
        .acquisitions(acquisitions)
        .batchId(batchId)
        .bibRecord(fullBibRecord)
        .build();
  }

  private void removeCancelledItems(String workOrderNumber, int bibRecordPk, Set<String> barCodes) {
    try {
      Set<String> activeBarcodes = workOrderService.getActiveItemBarcodes(workOrderNumber);
      Set<String> cancelledBarcodes =
          barCodes.stream().filter(b -> !activeBarcodes.contains(b)).collect(Collectors.toSet());
      log.info(
          "Found cancelledBarcodes {} for workOrder: {}, bibRecordPk: {}",
          cancelledBarcodes,
          workOrderNumber,
          bibRecordPk);
      modifyBibRecords(cancelledBarcodes, bibRecordPk);
    } catch (Exception e) {
      log.error(
          "Cannot remove cancelled barcodes for workOrder: {}, bibRecordPk: {}",
          workOrderNumber,
          bibRecordPk);
      throw new RuntimeException(e);
    }
  }

  public List<String> getSummarySlipImages(String workOrderNumber, Holding holding) {
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    return genreLabelRepository.findAllByCustomerCode(customerCode).stream()
        .map(genreLabel -> getSummarySlipImages(holding, genreLabel))
        .filter(Optional::isPresent)
        .map(Optional::get)
        .collect(Collectors.toList());
  }

  public boolean hasGenreLabelConfig(String workOrderNumber) {
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    return CollectionUtils.isNotEmpty(genreLabelRepository.findAllByCustomerCode(customerCode));
  }

  private Optional<String> getSummarySlipImages(Holding holding, GenreLabel genreLabel) {
    if (!genreLabel.isValid()) {
      log.warn(
          "The holding field and holding value of genre_label {} should have the same number of elements, separated by a comma.",
          genreLabel);
      return Optional.empty();
    }

    List<String> images = new ArrayList<>();
    String[] holdingFields = genreLabel.getHoldingFieldSplit();
    String[] holdingValues = genreLabel.getHoldingValueSplit();
    for (int i = 0; i < holdingFields.length; i++) {
      filterByHoldingValue(holding, holdingFields[i], holdingValues[i], genreLabel.getImage())
          .ifPresent(images::add);
    }
    return (holdingFields.length == images.size() && new HashSet<>(images).size() == 1)
        ? images.stream().findFirst()
        : Optional.empty();
  }

  private static Optional<String> filterByHoldingValue(
      Holding holding, String holdingField, String configValue, @NotNull String value) {
    try {
      Method getNameMethod =
          holding
              .getClass()
              .getMethod(
                  String.format(
                      "get%s%s",
                      holdingField.substring(0, 1).toUpperCase(), holdingField.substring(1)));
      String actualValue = (String) getNameMethod.invoke(holding);
      if (configValue.equalsIgnoreCase(actualValue)) {
        return Optional.of(value);
      }
    } catch (Exception e) {
      log.warn("Can not get the holding field base on config by holdingField {}", holdingField, e);
    }
    return Optional.empty();
  }

  private List<String> getSeparateBatchCodes(String customerCode, Holding holding) {
    return separateBatchByHoldingRepository.findAllByCustomerCode(customerCode).stream()
        .map(separateBatchByHolding -> getSeparateBatchCodes(holding, separateBatchByHolding))
        .filter(Optional::isPresent)
        .map(Optional::get)
        .collect(Collectors.toList());
  }

  private Optional<String> getSeparateBatchCodes(Holding holding, SeparateBatchByHolding separateBatchByHolding) {
    String holdingField = separateBatchByHolding.getHoldingField();
    String configValue = separateBatchByHolding.getHoldingValue();
    return filterByHoldingValue(holding, holdingField, configValue, separateBatchByHolding.getBatchCode());
  }

  public void addHolding(String workOrderNumber) {
    // AddCustomerHoldingWizardModel.buildHolding(itemReceiptAssignment)
    TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
    Holding holding = getHolding(workOrderNumber, titleOrder, titleOrder.getFundCode(), true, false);
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    if (StringUtils.isEmpty(holding.getCollectionCode()) && isSpydusCustomer(customerCode)) {
      throw new BusinessException("Collection is empty - check with Cataloguer before proceeding further.");
    }
    ItemReceiptAssignment itemReceiptAssignment = titleOrderService.getItemReceiptAssignment(workOrderNumber, false);
    boolean isReduceQuantityToOne = isReduceQuantityToOne(customerCode, titleOrder.getFundCode());

    // AddCustomerHoldingWizardModel.buildAcquisition(itemReceiptAssignment)
    Acquisition acquisition = buildAcquisition(itemReceiptAssignment, holding, isReduceQuantityToOne);

    // AddCustomerHoldingWizardModel.addReceiptToAcquisition(itemReceiptAssignment, acquisition)
    addReceiptToAcquisition(itemReceiptAssignment, acquisition, isReduceQuantityToOne);

    // AddCustomerHoldingWizardModel.addReservationAllocations(reservationAllocations)
    // TODO: Need to get reservations from LMS for this first
    // AddCustomerHoldingWizardModel.createCopies() (for Reservation Allocations)
    //List<Copy> copies = createCopies(acquisition, holding);

    // AddCustomerHoldingWizardModel.autoAllocateAcquisition(acquisition, allocationsTemplate)

    // Get allocations template
    List<BibTemplate> bibTemplates = bibTemplateService.getBibTemplates(customerCode, au.com.peterpal.lucyapi.model.BibCollectionType.PRINT_STD, BibTemplateType.ALLOCATE);

    OpenTitleOrder openTitleOrder = titleOrderService.getOpenTitleOrderWithWorkOrderNumber(workOrderNumber);
    List<OpenTitleOrderAllocation> allocations = filterAllocations(openTitleOrder, holding.getAcquisitions(), titleOrder.getOrderNumber());
    if (CollectionUtils.isNotEmpty(allocations) && !bibTemplates.isEmpty()) {
      int orderQuantity = isReduceQuantityToOne ? 1 : titleOrder.getQty();
      int workOrderQuantity = isReduceQuantityToOne ? 1 : itemReceiptAssignment.getQtyAssigned();
      manuallyAllocateAcquisition(acquisition, allocations, orderQuantity, workOrderQuantity);
      autoAllocateAcquisitionForRemainingQuantity(acquisition, bibTemplates.get(0), orderQuantity - workOrderQuantity);
    } else if (bibTemplates.isEmpty()) {
      int orderQuantity = isReduceQuantityToOne ? 1 : titleOrder.getQty();
      int workOrderQuantity = isReduceQuantityToOne ? 1 : itemReceiptAssignment.getQtyAssigned();
      manuallyAllocateAcquisition(acquisition, allocations, orderQuantity, workOrderQuantity);
    } else {
      log.debug(() -> String.format("Bib Templates are: %s", bibTemplates.stream().map(b -> b.getPk() + "").collect(Collectors.joining(", "))));
      // TODO: What if there is more than one?
      BibTemplate allocationsTemplate = bibTemplates.get(0);
      // Automatically allocate with sequences
      autoAllocateAcquisition(acquisition, allocationsTemplate);
    }

    // AddCustomerHoldingWizardModel.createCopies() - for remaining auto allocations
    createCopies(acquisition, holding);

    // Lucy-javaee7.addHolding(customerId, bibCollectionType, holding)
    addHolding(customerCode, holding.getBibCollection().getCollectionType(), holding);
  }

  private boolean isSpydusCustomer(String customerCode) {
    return lmsConfigurationRepository
            .findByCustomerCode(customerCode)
            .map(LMSConfiguration::getLmsType)
            .filter(lmsType -> LMSType.SPYDUS == lmsType)
            .isPresent();
  }

  private List<OpenTitleOrderAllocation> filterAllocations(OpenTitleOrder openTitleOrder,
                                                           List<Acquisition> acquisitions,
                                                           String orderNumber) {
    Set<String> processedBranchCode = acquisitions
        .stream()
        .filter(acquisition -> acquisition.getOrderId().getTransactionIdValue().equalsIgnoreCase(orderNumber))
        .map(Acquisition::getAllocations)
        .flatMap(List::stream)
        .filter(allocation -> allocation.getStatus() == AllocationStatus.PROCESS)
        .map(Allocation::getBranchCode)
        .collect(Collectors.toSet());

    List<OpenTitleOrderAllocation> allocations = openTitleOrder.getAllocations();
    allocations = allocations.stream().filter(a -> isProcessOrHold(a.getStatus())
            && hasQuantity(a.getQuantity())
            && !processedBranchCode.contains(a.getBranchCode()))
        .collect(Collectors.toList());
    return allocations;
  }

  private boolean hasQuantity(Integer quantity) {
    return quantity !=null && 0 != quantity;
  }

  private boolean isProcessOrHold(TitleOrderAllocationStatus status) {
    return status == null
        || status == TitleOrderAllocationStatus.PROCESS
        || status == TitleOrderAllocationStatus.HOLD;
  }

  public void printLabel(String workOrderNumber, String labelName) {
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    // Get Label Definitions
    List<BibTemplate> bibTemplates = getBibTemplates(customerCode);
    log.info(
        () ->
            String.format(
                "Bib Templates are: %s",
                bibTemplates.stream().map(b -> b.getPk() + "").collect(Collectors.joining(", "))));
    BibTemplate allocationsTemplate = bibTemplates.get(0);

    TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
    Holding holding = getHolding(workOrderNumber, titleOrder, titleOrder.getFundCode(), true, false);
    String titleOrderNumber = titleOrder.getOrderNumber();
    // Handle multiple acquisitions
    List<Acquisition> acquisitions =
        holding.getAcquisitions().stream()
            // Acquisition is for titleOrderNumber & workOrderNumber
            .filter(
                a ->
                    a.getOrderId().getTransactionIdValue().equals(titleOrderNumber)
                        && a.getReceipts().stream()
                            .anyMatch(r -> r.getOrderAssignmentId().equals(workOrderNumber))
                        && isValidAllocations(a.getAllocations()))
            .collect(Collectors.toList());
    if (acquisitions.isEmpty()) {
      throw new BusinessException("Cannot find any acquisitions");
    }

    Acquisition acquisition = acquisitions.get(0);
    List<Copy> copies = acquisition.getCopies();
    Map<String, Label> labels;
    try {
      labels = getLabelDefinitions(allocationsTemplate);
    } catch (CataloguingException exception) {
      throw new BusinessException("Cataloguing Exception: " + exception.getMessage());
    }
    List<Copy> copiesWithBarcodes =
        copies.stream().filter(c -> c.getBarCodeNumber() != null).collect(Collectors.toList());
    Optional<CollectionTypeByFund> collectionTypeByFund =
        getCollectionTypeByFund(workOrderNumber, customerCode);
    copiesWithBarcodes = filterCopies(copiesWithBarcodes, collectionTypeByFund);
    if (copiesWithBarcodes.isEmpty()) {
      throw new BusinessException("No copies with barcodes found");
    }
    Map<Integer, List<String>> labelsCopyMap = new HashMap<>();
    for (Copy c : copiesWithBarcodes) {
      List<Label> printLabels = filterLabels(labels, collectionTypeByFund, labelName);

      labelsCopyMap.put(
          c.getPk(), printLabels.stream().map(Label::getName).collect(Collectors.toList()));
    }
    printLabel(allocationsTemplate.getPk(), labelsCopyMap);
  }

  public void printLabel(
      int allocationsTemplatePk, Map<Integer, List<String>> pairOfCopyIdAndLabelIds) {
    PrintLabelMessage printLabelMessage =
        PrintLabelMessage.builder()
            .allocationsTemplateId(allocationsTemplatePk)
            .pairOfCopyIdAndLabelNames(pairOfCopyIdAndLabelIds)
            .build();
    printLabelChannel.send(
        MessageBuilder.withPayload(printLabelMessage)
            .setHeader("message_type", "print-label")
            .build());
    log.info("Sent print label event {}", printLabelMessage);
  }

  private List<BibTemplate> getBibTemplates(String customerCode) {
    return bibTemplateService.getBibTemplates(
        customerCode,
        au.com.peterpal.lucyapi.model.BibCollectionType.PRINT_STD,
        BibTemplateType.RULES);
  }

  public AddCopiesResponse addCopies(
      String workOrderNumber, List<String> barcodes, String labelName) {
    List<Map.Entry<String,String>> barcodesAndDestinations = new ArrayList<>();
    Map<Integer, List<String>> printableLabelMap = new HashMap<>();
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    // Get Label Definitions
    List<BibTemplate> bibTemplates = getBibTemplates(customerCode);
    log.info(() -> String.format("Bib Templates are: %s",bibTemplates.stream().map(b -> b.getPk()+"").collect(Collectors.joining(", "))));
    BibTemplate allocationsTemplate = bibTemplates.get(0);

    Map<String, Label> labels;
    try {
      labels = getLabelDefinitions(allocationsTemplate);
    } catch (CataloguingException exception) {
      throw new BusinessException("Cataloguing Exception: "+exception.getMessage());
    }

    // Add Barcodes to Copies
    TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
    Holding holding = getHolding(workOrderNumber, titleOrder, titleOrder.getFundCode(), true, false);
    String titleOrderNumber = titleOrder.getOrderNumber();
    // Handle multiple acquisitions
    List<Acquisition> acquisitions = holding.getAcquisitions().stream()
        // Acquisition is for titleOrderNumber & workOrderNumber
        .filter(a -> a.getOrderId().getTransactionIdValue().equals(titleOrderNumber)
          && a.getReceipts().stream().anyMatch(r -> r.getOrderAssignmentId().equals(workOrderNumber))
          && isValidAllocations(a.getAllocations()))
        .collect(Collectors.toList());

    if (acquisitions.size() > 1)
      throw new BusinessException("Add Copies: Cannot determine which allocation to use, candidates are: "+
          acquisitions.stream()
              .map(a -> "Acquisition "+a.getOrderId()+" (Allocations "+a.getAllocations().stream()
                  .map(al -> al.getPk()+"").collect(Collectors.joining(", ")))
              .collect(Collectors.joining(", ")));
    else if (acquisitions.isEmpty())
      throw new BusinessException("Add Copies: Cannot determine which allocation to use, no candidates found for "
          +titleOrderNumber+" with "+barcodes.size()+" empty barcodes");

    Acquisition acquisition = acquisitions.get(0);

    List<Copy> copies = acquisition.getCopies();
    List<Copy> copiesWithoutBarcodes = copies.stream().filter(c -> c.getBarCodeNumber() == null).collect(Collectors.toList());
    Optional<CollectionTypeByFund> collectionTypeByFund = getCollectionTypeByFund(workOrderNumber, customerCode);
    copiesWithoutBarcodes = filterCopies(copiesWithoutBarcodes, collectionTypeByFund);
    if (copiesWithoutBarcodes.size() != barcodes.size())
      throw new BusinessException(String.format("There are %s copies without barcodes but %s barcodes are provided"
              ,copiesWithoutBarcodes.size()
              , barcodes.size()));
    else{
      Iterator<String> barcodeIterator = barcodes.iterator();
      for (Copy c : copiesWithoutBarcodes){
        c.setBarCodeNumber(barcodeIterator.next());
        barcodesAndDestinations.add(new AbstractMap.SimpleEntry<>(c.getBarCodeNumber(), c.getBranchCode()));
        // Save Copy with Barcode
        try {
          updateCopy(c);
        } catch (DuplicateBarcodeException exception) {
          String message =
              String.format(
                  "Barcode %s is duplicated on Work Order %s",
                  c.getBarCodeNumber(), workOrderNumber);
          log.error(message);
          throw new BusinessException(message);
        }
        // Print Labels
        List<Label> printLabels = filterLabels(labels, collectionTypeByFund, labelName);

        printableLabelMap.put(c.getPk(), printLabels.stream().map(Label::getName).collect(Collectors.toList()));
      }
    }

    return AddCopiesResponse.builder()
        .barcodesAndDestinationsAdded(barcodesAndDestinations)
        .printableLabelMap(printableLabelMap)
        .allocationTemplatePk(allocationsTemplate.getPk())
        .build();
  }

  public List<Label> getLabels(String workOrderNumber){
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    // Get Label Definitions
    List<BibTemplate> bibTemplates = getBibTemplates(customerCode);
    log.info(() -> String.format("Bib Templates are: %s",bibTemplates.stream().map(b -> b.getPk()+"").collect(Collectors.joining(", "))));
    BibTemplate allocationsTemplate = bibTemplates.get(0);
    try {
      Map<String, Label> labels = getLabelDefinitions(allocationsTemplate);
      return new ArrayList<>(labels.values());
    } catch (CataloguingException exception) {
      throw new BusinessException("Cataloguing Exception: "+exception.getMessage());
    }
  }

  private boolean isValidAllocations(List<Allocation> allocations) {
    return CollectionUtils.isNotEmpty(allocations) && allocations
        .stream()
        .anyMatch(allocation -> allocation.getStatus() != null);
  }

  private List<Copy> filterCopies(List<Copy> copiesWithoutBarcodes, Optional<CollectionTypeByFund> collectionTypeByFundOptional) {

    if (!collectionTypeByFundOptional.isPresent() || copiesWithoutBarcodes.isEmpty()) {
      return copiesWithoutBarcodes;
    }
    CollectionTypeByFund collectionTypeByFund = collectionTypeByFundOptional.get();
    if (collectionTypeByFund.isReduceQuantityToOne()) {
      return Collections.singletonList(copiesWithoutBarcodes.get(0));
    }
    return copiesWithoutBarcodes;
  }

  private List<Label> filterLabels(
      Map<String, Label> labels,
      Optional<CollectionTypeByFund> collectionTypeByFundOptional,
      String labelName) {
    List<Label> printLabels =
        StringUtils.isNotBlank(labelName)
            ? labels.values().stream()
                .filter(p -> p.getName().equalsIgnoreCase(labelName))
                .collect(Collectors.toList())
            : labels.values().stream().filter(Label::getAutoPrint).collect(Collectors.toList());

    if (!collectionTypeByFundOptional.isPresent() || printLabels.isEmpty()) {
      return printLabels;
    }
    CollectionTypeByFund collectionTypeByFund = collectionTypeByFundOptional.get();
    if (!collectionTypeByFund.isPrintLabels()) {
      return Collections.emptyList();
    }
    if (collectionTypeByFund.isReduceLabelsToOne() && printLabels.size() > 1) {
      return Collections.singletonList(printLabels.get(0));
    }
    return printLabels;
  }

  private Optional<CollectionTypeByFund> getCollectionTypeByFund(String workOrderNumber, String customerCode) {
    TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
    CollectionTypeByFundKey collectionTypeByFundKey = CollectionTypeByFundKey.of(customerCode, titleOrder.getFundCode());
    return collectionTypeByFundRepository.findById(collectionTypeByFundKey);
  }

  private Acquisition buildAcquisition(ItemReceiptAssignment itemReceiptAssignment, Holding holding,
      boolean isReduceQuantityToOne) {
    Acquisition acquisition = new Acquisition();

    holding.getAcquisitions().add(acquisition);

    acquisition.setOrderId(itemReceiptAssignment.getTitleOrder().getOrderId());
    acquisition.setAcquiredPriceCurrency(CurrencyCode.AUD);
    acquisition.setHolding(holding);
    acquisition.setAcquiredPriceAmount(itemReceiptAssignment.getItemReceiptLine().getNetSalePriceAmount());

    int acquiredQuantity = isReduceQuantityToOne ? 1 : itemReceiptAssignment.getQtyAssigned();
    acquisition.setQtyAcquired(acquiredQuantity);

    return acquisition;
  }

  private void addReceiptToAcquisition(ItemReceiptAssignment itemReceiptAssignment, Acquisition acquisition,
      boolean isReduceQuantityToOne) {
    Receipt receipt = new Receipt();
    receipt.setAcquisition(acquisition);
    acquisition.getReceipts().add(receipt);

    receipt.setOrderAssignmentId(itemReceiptAssignment.getOrderAssignmentId());
    receipt.setNotes(itemReceiptAssignment.getDeliveryInstructions());
    receipt.setNetSalePriceCurrency(CurrencyCode.AUD);

    // Handle reduce quantity to one configuration
    int quantityAssigned = isReduceQuantityToOne ? 1 : itemReceiptAssignment.getQtyAssigned();
    receipt.setQtyReceived(quantityAssigned);

    if (itemReceiptAssignment.getItemReceiptLine() != null) {
      receipt.setReceivedDate(itemReceiptAssignment.getItemReceiptLine().getCreatedDate());
      receipt.setNetSalePriceAmount(itemReceiptAssignment.getItemReceiptLine().getNetSalePriceAmount());
    } else {
      receipt.setReceivedDate(new Date());
      receipt.setNetSalePriceAmount(new BigDecimal("0.00"));
    }
  }

  private List<Copy> createCopies(Acquisition acquisition, Holding holding) {
    for (Allocation allocation : acquisition.getAllocations()) {
      if (allocation.getStatus() != AllocationStatus.HOLD) {
        for (int i = 0; i < allocation.getQuantity(); ++i) {
          Copy copy = new Copy();
          copy.setAcquisition(acquisition);
          acquisition.getCopies().add(copy);

          copy.setBranchCode(allocation.getBranchCode());
          copy.setItemTypeCode(allocation.getItemTypeCode());
          copy.setCollectionCode(allocation.getCollectionCode());
          copy.setCategoryCode1(allocation.getCategoryCode1());
          copy.setCategoryCode2(allocation.getCategoryCode2());
          copy.setCallNumber(allocation.getCallNumber());
          copy.setGenre(allocation.getGenre());
          copy.setDewey(allocation.getDewey());
          copy.setCutter(holding.getCutter());
          copy.setSpineLabelPrefix(allocation.getSpineLabelPrefix());
          copy.setSpineLabel(allocation.getSpineLabel());
          copy.setNotes(allocation.getNotes());
        }
      }
    }
    return acquisition.getCopies();
  }

  private void autoAllocateAcquisition(Acquisition acquisition, BibTemplate allocationsTemplate) {
    List<Allocation> automaticAllocations = getAutomaticAllocations(acquisition, allocationsTemplate);

    acquisition.getAllocations().clear();
    acquisition.getAllocations().addAll(automaticAllocations);

    for (Allocation allocation : automaticAllocations) {
      allocation.setAcquisition(acquisition);
    }
  }

  private void manuallyAllocateAcquisition(Acquisition acquisition,
      List<OpenTitleOrderAllocation> allocations, int orderQuantity, int workOrderQuantity) {
    // Based off AddCustomerHoldingWizardModel.addReservationAllocations

    // Check if it has been allocated before
    int allocatedQty = acquisition.getAllocations().stream().map(Allocation::getQuantity).mapToInt(Integer::intValue).sum();
    if (allocatedQty > 0){
      releaseAllocations(acquisition, workOrderQuantity);
    }else{
      buildAllocations(acquisition, allocations, workOrderQuantity);
      if (orderQuantity > workOrderQuantity){
        // set any additional quantity to hold
        holdAllocations(acquisition, workOrderQuantity);
      }
    }
  }

  private void releaseAllocations(Acquisition acquisition, int workOrderQuantity){
    // Filter out the allocations in HOLD
    List<Allocation> allocationsOnHold = acquisition.getAllocations().stream()
        .filter(a -> a.getStatus().equals(AllocationStatus.HOLD))
        .collect(Collectors.toList());

    // Change work order quantity of allocations from HOLD to PROCESS
    int releasedQty = 0;
    for (Allocation allocation:allocationsOnHold){
      if (allocation.getQuantity() + releasedQty <= workOrderQuantity) {
        allocation.setStatus(AllocationStatus.PROCESS);
        releasedQty += allocation.getQuantity();
      }
    }

    if (releasedQty != workOrderQuantity) {
      throw new BusinessException("Could not release allocations to match work order quantity");
    }
  }

  private void holdAllocations(Acquisition acquisition, int workOrderQuantity) {
    List<Allocation> allocations = acquisition.getAllocations();

    Map<String, Integer> mapOfAllocationIdAndQuantity = allocations
        .stream()
        .collect(Collectors.toMap(Allocation::getBranchCode, Allocation::getQuantity,
            (u, v) -> {
              throw new IllegalStateException(String.format("Duplicate key %s", u));
            },
            LinkedHashMap::new));

    List<String> branchCodeToProcess = AllocationSelectingUtil.selectBranchCodeToProcess(mapOfAllocationIdAndQuantity, workOrderQuantity);

    for (Allocation allocation : allocations) {
      if (!branchCodeToProcess.contains(allocation.getBranchCode())){
        allocation.setStatus(AllocationStatus.HOLD);
      }
    }
  }

  private void autoAllocateAcquisitionForRemainingQuantity(Acquisition acquisition, BibTemplate allocationsTemplate, int remainingQuantity) {
    List<Allocation> automaticAllocations = getAutomaticAllocations(acquisition, allocationsTemplate);
    List<Allocation> manualAllocations = acquisition.getAllocations();

    // Use automatic allocations to allocate remaining quantity
    List<Allocation> remainingAllocations = new ArrayList<>();
    int sum = 0;
    Iterator<Allocation> allocationIterator = automaticAllocations.iterator();
    while (sum < remainingQuantity && allocationIterator.hasNext()){
      Allocation allocation = allocationIterator.next();
      allocation.setStatus(AllocationStatus.HOLD);
      if (sum + allocation.getQuantity() <= remainingQuantity){
        remainingAllocations.add(allocation);
      }else{
        // reduce last allocation quantity to remaining quantity
        allocation.setQuantity(remainingQuantity - sum);
        remainingAllocations.add(allocation);
      }

      sum += allocation.getQuantity();
    }

    // Merge allocations with manually created allocations
    List<Allocation> newAllocations = new ArrayList<>();
    for (Allocation allocation : remainingAllocations){
      int quantity = manualAllocations.stream().filter(a -> a.getBranchCode().equals(allocation.getBranchCode()))
          .findFirst().map(Allocation::getQuantity).orElse(0);

      if (quantity > 0)
        // Update quantity if there is an existing manual allocation for this location
        allocation.setQuantity(allocation.getQuantity() + quantity);
      else
        // Add new allocation if there isn't already a location for it
        newAllocations.add(allocation);
    }

    // Update allocations on acquisition
    acquisition.getAllocations().addAll(newAllocations);

    for (Allocation allocation : newAllocations) {
      allocation.setAcquisition(acquisition);
    }
  }

  private void buildAllocations(Acquisition acquisition, List<OpenTitleOrderAllocation> allocations, int workOrderQuantity){
    List<Allocation> manualAllocations = new ArrayList<>();
    Holding holding = acquisition.getHolding();

    for (OpenTitleOrderAllocation allocation:allocations){
      Allocation lucyAllocation = new Allocation();

      // Set branch code and quantity from titleOrdDist in Lucy 4
      lucyAllocation.setBranchCode(allocation.getBranchCode());
      // Handle reducing quantity to one configuration
      int allocationQuantity = workOrderQuantity < allocation.getQuantity() ? workOrderQuantity : allocation.getQuantity();
        lucyAllocation.setQuantity(allocationQuantity);

      // Do unnecessary copying to satisfy model
      lucyAllocation.setItemTypeCode(holding.getItemTypeCode());
      lucyAllocation.setCollectionCode(holding.getCollectionCode());
      lucyAllocation.setCategoryCode1(holding.getCategoryCode1());
      lucyAllocation.setCategoryCode2(holding.getCategoryCode2());
      lucyAllocation.setCallNumber(holding.getCallNumber());
      lucyAllocation.setGenre(holding.getGenre());
      lucyAllocation.setDewey(holding.getDewey());
      lucyAllocation.setCutter(holding.getCutter());
      lucyAllocation.setSpineLabelPrefix(holding.getSpineLabelPrefix());
      lucyAllocation.setSpineLabel(holding.getSpineLabel());
      lucyAllocation.setNotes(holding.getNotes());
      lucyAllocation.setAcquisition(acquisition);
      manualAllocations.add(lucyAllocation);
    }

    // Update allocations on acquisition
    acquisition.getAllocations().clear();
    acquisition.getAllocations().addAll(manualAllocations);

    for (Allocation allocation : manualAllocations) {
      allocation.setAcquisition(acquisition);
    }
  }

  public String getPrintOnBranchSlipBatchCodes(String workOrderNumber) {
    TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
    Holding holding = getHolding(workOrderNumber, titleOrder, titleOrder.getFundCode(), true, false);
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);

    return separateBatchByHoldingRepository.findAllByCustomerCodeAndPrintOnBranchSlip(customerCode, true)
        .stream()
        .map(separateBatchByHolding -> getSeparateBatchCodes(holding, separateBatchByHolding))
        .filter(Optional::isPresent)
        .map(Optional::get)
        .collect(Collectors.joining(","));
  }

  @Transactional
  public ModifiedFullBibRecordMessage addModifiedFullBibRecord(
      ModifiedFullBibRecordMessage modifiedFullBibRecordMessage) {
    log.info("Adding modified full bib record message: {}", modifiedFullBibRecordMessage);

    Set<Integer> batchIds =
        modifiedFullBibRecordMessage.getInvoices().stream()
            .flatMap(
                invoice ->
                    invoice.getWorkOrders().stream()
                        .map(
                            workOrder ->
                                addModifiedBibRecord(
                                        workOrder,
                                        modifiedFullBibRecordMessage.getInvoiceGroupId(),
                                        invoice.getInvoiceId())
                                    .orElse(null)))
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

    log.info(
        "Added modified full bib record message: {} to batchIds {}",
        modifiedFullBibRecordMessage,
        batchIds);
    Set<UUID> invoiceIds =
        modifiedFullBibRecordMessage.getInvoices().stream()
            .map(ModifiedFullBibRecordMessage.Invoice::getInvoiceId)
            .collect(Collectors.toSet());
    Set<Integer> allBatchIds = batchItemService.getAllBatchIds(invoiceIds);
    batchService.close(allBatchIds);
    return modifiedFullBibRecordMessage;
  }

  private Optional<Integer> addModifiedBibRecord(
      ModifiedFullBibRecordMessage.WorkOrder workOrder, @NotNull UUID invoiceGroupId, UUID invoiceId) {
    String workOrderNumber = workOrder.getWorkOrderNumber();
    if (workOrder.getInvoicedBarcode().isEmpty()) {
      log.info("Skip to add modified bib record due to invoiced barcode is empty");
      return Optional.empty();
    }
    log.debug("Adding modified full bib record for workOrderNumber: {}", workOrderNumber);
    try {
      TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber,false);
      FullBibRecordCreatedResult fullBibRecordWithResult = createFullBibRecord(workOrderNumber, true, titleOrder, false, Sets.newHashSet());
      Set<String> fullBarcodes = fullBibRecordWithResult.getAcquisitions().stream()
          .map(Acquisition::getCopies)
          .flatMap(Collection::stream)
          .map(Copy::getBarCodeNumber)
          .filter(Objects::nonNull)
          .collect(Collectors.toSet());

      Set<String> notIncludedBarcodes = fullBarcodes.stream()
          .filter(b -> !workOrder.getInvoicedBarcode().contains(b))
          .collect(Collectors.toSet());
      Set<String> includedBarcodes = fullBarcodes.stream()
          .filter(b -> workOrder.getInvoicedBarcode().contains(b))
          .collect(Collectors.toSet());

      modifyBibRecords(notIncludedBarcodes, fullBibRecordWithResult.getBibRecord().getPk());

      batchItemService.storeBarcodes(
          workOrderNumber,
          fullBibRecordWithResult.getBatchId(),
          includedBarcodes,
          false,
          invoiceGroupId,
          invoiceId);
      log.debug("Added modified full bib record for workOrderNumber: {} with barcodes: {}", workOrderNumber, includedBarcodes);
      return Optional.ofNullable(fullBibRecordWithResult.getBatchId());
    } catch (Exception e) {
      log.error("Cannot add full bib record for workOrderNumber {}", workOrderNumber, e);
      return Optional.empty();
    }
  }

  public BibRecord removeBarCodesLines(BibRecord bibRecord, Set<String> removedBarCodes) {
    String marcText = bibRecord.getMarcText();
    String[] lines = marcText.split("\n");

    StringBuilder result = new StringBuilder();

    for (String line : lines) {
      boolean containsRemovedBarCodes = removedBarCodes.stream().anyMatch(line::contains);
      if (!containsRemovedBarCodes) {
        result.append(line).append("\n");
      } else {
        log.debug("Removed line {}", line);
      }
    }
    try {
      bibRecord.setMarcText(result.toString());
    } catch (MarcException e) {
      log.error("Cannot update marc text for bibRecordId {}", bibRecord.getPk(), e);
    }
    return bibRecord;
  }

  public Set<String> containBarCodesLines(BibRecord bibRecord, Set<String> barCodes) {
    String marcText = bibRecord.getMarcText();
    String[] lines = marcText.split("\n");
    Set<String> barCodesSet = new HashSet<>();
    for (String line : lines) {
      barCodes.stream().filter(line::contains).findAny().ifPresent(barCodesSet::add);
    }
    return barCodes;
  }

  @Transactional
  public AddFullBibRecordMessage addFullBibRecord(AddFullBibRecordMessage addFullBibRecordMessage) {
    String workOrderNumber = addFullBibRecordMessage.getWorkOrderNumber();
    try {
      List<BatchItem> batchItems = batchItemService.findAllByWorkOrderNumber(workOrderNumber);
      if (batchItems.stream().anyMatch(BatchItem::isFullBibRecordsCreated)) {
        log.info(
            "Skip to add full bib record for workOrderNumber {} due to full bib records created",
            workOrderNumber);
        return addFullBibRecordMessage;
      }
      if (batchItems.isEmpty()) {
        createFullBibRecordWithStoreBarcodes(
            workOrderNumber, addFullBibRecordMessage.getInvoiceGroupId(), addFullBibRecordMessage.getInvoiceId(), true);
        log.info("Added full bib records for workOrderNumber {}", workOrderNumber);
      } else {
        TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
        FullBibRecordCreatedResult fullBibRecordWithResult =
            createFullBibRecord(workOrderNumber, true, titleOrder, false, Sets.newHashSet());
        Set<String> fullBarCodes =
            fullBibRecordWithResult.getAcquisitions().stream()
                .map(Acquisition::getCopies)
                .flatMap(Collection::stream)
                .map(Copy::getBarCodeNumber)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> partiallyInvoicedBarcodes =
            batchItems.stream().map(BatchItem::getItemBarcode).collect(Collectors.toSet());

        Set<String> notIncludedBarcodes =
            fullBarCodes.stream()
                .filter(partiallyInvoicedBarcodes::contains)
                .collect(Collectors.toSet());

        Set<String> includedBarcodes =
            fullBarCodes.stream()
                .filter(b -> !partiallyInvoicedBarcodes.contains(b))
                .collect(Collectors.toSet());

        modifyBibRecords(notIncludedBarcodes, fullBibRecordWithResult.getBibRecord().getPk());
        batchItemService.storeBarcodes(
            workOrderNumber,
            fullBibRecordWithResult.getBatchId(),
            includedBarcodes,
            true,
            addFullBibRecordMessage.getInvoiceGroupId(),
            addFullBibRecordMessage.getInvoiceId());

        log.debug(
            "Added modified full bib record for workOrderNumber: {} with barcodes {}",
            workOrderNumber,
            includedBarcodes);
      }

    } catch (Exception e) {
      log.error("Cannot add full bib record for workOrderNumber {}", workOrderNumber, e);
    }

    return addFullBibRecordMessage;
  }

  public void modifyBibRecords(Set<String> notIncludedBarcodes, int bibRecordPk) throws NotFoundException {
    BibRecord bibRecord = remoteCataloguing.getBibRecord(bibRecordPk);
    BibRecord updated = removeBarCodesLines(bibRecord, notIncludedBarcodes);
    remoteCataloguing.updateBibRecord(updated);
  }

  public boolean isPrintOnBranchSlip(String workOrderNumber) {
    TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
    Holding holding = getHolding(workOrderNumber, titleOrder, titleOrder.getFundCode(), true, false);
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);

    return separateBatchByHoldingRepository.findAllByCustomerCodeAndPrintOnBranchSlip(customerCode, true)
        .stream()
        .map(separateBatchByHolding -> getSeparateBatchCodes(holding, separateBatchByHolding))
        .anyMatch(Optional::isPresent);
  }

  public NewIncompleteBibRecordMessage addNewIncompleteBibRecord(NewIncompleteBibRecordMessage message) {
    try {
      String workOrderNumber = message.getPreviousWorkOrderNumber();
      TitleOrderInfo titleOrder = getTitleOrder(workOrderNumber);
      String customerCode = titleOrder.getCustomerCode();

      Holding holding = getHolding(message, workOrderNumber, titleOrder, customerCode);
      List<BibTemplate> bibTemplates = getBibTemplates(customerCode);
      log.debug(() -> String.format("Bib Templates are: %s", bibTemplates.stream().map(b -> b.getPk() + "").collect(Collectors.joining(", "))));

      // Get Supplied Bib Records
      List<BibRecord> bibRecords = bibRecordService.getSuppliedBibRecords(holding.getBibCollection().getPk());
      log.debug(() -> String.format("Supplied Bib Records are: %s", bibRecords.stream().map(b -> b.getPk() + "").collect(Collectors.joining(", "))));

      // Determine Base Bib Records
      BibRecord baseBibRecord = bibRecordService.getBaseBibRecord(bibRecords);
      log.debug(() -> String.format("Chosen Base Bib Record is: %s", baseBibRecord.getPk()));

      // Create Full Bib Record
      List<BibRecord> fullBibRecords = bibRecordService.createFullBibRecord(holding.getBibCollection(),
          Collections.singletonList(holding), Collections.emptyList(), bibTemplates, baseBibRecord);
      BibRecord fullBibRecord = fullBibRecords.get(0);
      bibRecordService.updateStatus(fullBibRecord, BibRecordStatus.INCOMPLETE);

    } catch (Exception e) {
      log.error("Cannot add new incomplete bib record for message: {}", message, e);
    }
    return message;
  }

  private TitleOrderInfo getTitleOrder(String workOrderNumber) {
    try {
      return titleOrderService.getTitleOrder(workOrderNumber, false);
    } catch (Exception e) {
      log.debug("Cannot get Open title order, trying to get closed title order.....");
      return titleOrderArcRepository.findTitleOrderArcByOrderAssignId(workOrderNumber);
    }
  }

  private Holding getHolding(NewIncompleteBibRecordMessage message,
                             String workOrderNumber,
                             TitleOrderInfo titleOrder,
                             String customerCode) {
    Holding holding;
    try {
      holding = getHolding(workOrderNumber, titleOrder, message.getFundCode(), false, false);
    } catch (Exception e) {
      log.info("Cannot find existing holding for workOrder: {}, fundCode: {}. Trying to add new holding", workOrderNumber, message.getFundCode());
      holding = getHolding(workOrderNumber, titleOrder, titleOrder.getFundCode(), true, false);
      String collectionType = getCollectionType(customerCode, message.getFundCode());
      BibCollectionType bibCollectionType = BibCollectionType.mapCode(collectionType);
      holding = addHolding(customerCode, bibCollectionType, holding);
    }
    return holding;
  }
}
