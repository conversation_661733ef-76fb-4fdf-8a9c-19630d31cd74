package au.com.peterpal.lucyapi.core.service;

import static au.com.peterpal.lucyapi.utils.Helper.getCustomerId;
import static au.com.peterpal.lucyapi.utils.Helper.notNull;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.model.BibCollectionType;
import au.com.peterpal.lucyapi.model.BibTemplateType;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.BibTemplate;
import lucy.common.NotFoundException;
import lucy.fulfillment.codes.CataloguingAction;
import lucy.marc.MarcException;
import lucy.marc.customiser.MarcStrLookup;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class BibTemplateService {

  private RemoteCataloguing cataloguing;

  public BibTemplateService(RemoteCataloguing cataloguing) {
    this.cataloguing = cataloguing;
  }

  public BibTemplate getBibTemplate(int templateId) throws NotFoundException {
    return cataloguing.getBibTemplate(templateId);
  }

  /**
   *
   * @param customer
   * @param collectionType
   * @param bibTemplateType
   * @return
   */
  public List<BibTemplate> getBibTemplates(String customer,
      BibCollectionType collectionType, BibTemplateType bibTemplateType) {

    if (bibTemplateType == null && collectionType == null) {
      return cataloguing.getBibTemplates(getCustomerId(customer));
    } else if (bibTemplateType == null) {
      return cataloguing.getBibTemplates(getCustomerId(customer),
          lucy.cataloguing.codes.BibCollectionType.mapCode(collectionType.name()));
    } else {
      lucy.cataloguing.codes.BibCollectionType type = lucy.cataloguing.codes.BibCollectionType.valueOf(collectionType.name());
      lucy.cataloguing.codes.BibTemplateType ttype = lucy.cataloguing.codes.BibTemplateType.valueOf((bibTemplateType.name()));
      return cataloguing.getBibTemplates(getCustomerId(customer), type, ttype);
    }
  }

  /**
   * Given a <class>BibTemplate</class> return a set of <class>CataloguingAction</class>
   *
   * @param template a bib template
   * @return a set of <class>CataloguingAction</class> from the MARC record.
   * @throws BusinessException if <class>MarcException</class> has been encountered
   *
   */
  public Set<CataloguingAction> getCataloguingActions(BibTemplate template) {
    notNull(template, "BibTemplate must not be null");

    Set<CataloguingAction> createForActions = new HashSet<>();
    try {
      MarcStrLookup marcResolver = new MarcStrLookup(template.getBibRecord().getMarcRecord(), "rules");
      Arrays.asList(StringUtils.split(marcResolver.lookup("rules.lookup:099/0:a"), ";")).stream()
          .filter(StringUtils::isNotBlank)
          .forEach(actionStr -> {
            try {
              createForActions.add(CataloguingAction.mapCode(StringUtils.trim(actionStr)));
            } catch (IllegalArgumentException e) {
              String str = "Cataloging action code %s field 099 of bib template with id %d is unrecognised.";
              String msg = String.format(str, actionStr);
              log.warn(() -> msg);
            }
          });
    } catch (MarcException ex) {
      String msg = String.format("Exception getting cataloguing actions from bib template with id %d", template.getPk());
      log.error(() -> msg, ex);
      throw new BusinessException(msg, ex);
    }
    return createForActions;
  }
}
