package au.com.peterpal.lucyapi.core.service.retrieve;

public class Z3950Constants {

  private Z3950Constants() {
    throw new IllegalStateException("Constants class");
  }

  public static final String QUERY_PREFIX = "@attrset bib-1 ";

  public static final String OR_OPERATOR = "@or";

  public static final String AND_OPERATOR = "@and";

  public static final String OR_EXPRESSION = "@or {0} {1}";

  public static final String AND_EXPRESSION = "@and {0} {1}";


  public static final String AUTHOR_QUERY = "@attr 1=1003 @attr 2=3 @attr 3=3 @attr 4=1 {0}";

  public static final String TITLE_QUERY = "@attr 1=4 @attr 2=3 @attr 3=3 @attr 4=6 {0}";

  public static final String SERIES_TITLE_QUERY = "@attr 1=5 @attr 2=3 @attr 3=3 @attr 4=6 {0}";

  public static final String PUBLISHER_QUERY = "@attr 1=1018 @attr 2=3 @attr 3=3 @attr 4=6 {0}";

  public static final String SUBJECT_QUERY = "@attr 1=21 @attr 2=3 @attr 3=3 @attr 4=1 {0}";

  public static final String ISBN_QUERY = "@attr 1=7 @attr 2=3 @attr 4=2 {0}";

  public static final String PUBLISHER_NUM_QUERY = "@attr 1=51 @attr 2=3 @attr 4=1 {0}";

  public static final String KEYWORD_QUERY = "@attr 1=1016 @attr 2=3 @attr 3=3 @attr 4=2 {0}";

  public static final String LOCAL_NUMBER_QUERY = "@attr 1=12 @attr 2=3 @attr 4=2 {0}";
}
