package au.com.peterpal.lucyapi.core.service;

import static au.com.peterpal.lucyapi.core.service.PrinterService.POST_REQUEST_FOOTER;
import static au.com.peterpal.lucyapi.core.service.PrinterService.POST_REQUEST_HEADER;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.model.SummarySlipMessage;
import au.com.peterpal.lucyapi.persistence.lucyapi.LMSConfigurationRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.LMSConfiguration;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.entity.Holding;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.ejb.EJBTransactionRolledbackException;

@Service
@Log4j2
@RequiredArgsConstructor
public class ReceiptPrinterService {

  private final WorkOrderService workOrderService;
  private final TitleOrderService titleOrderService;
  private final LMSConfigurationRepository lmsConfigurationRepository;
  private final HoldingService holdingService;
  private final RestTemplate restTemplate;

  public void print(String printer, String printJobId, String xmlBody) {
    try {
      String postRequest =
          String.format(POST_REQUEST_HEADER, printJobId) + xmlBody + POST_REQUEST_FOOTER;

      log.info(() -> String.format("Post request for receipt printer is: %s", postRequest));
      ResponseEntity<String> response =
          restTemplate.postForEntity(
              String.format("http://%s/cgi-bin/epos/service.cgi", printer),
              postRequest,
              String.class);

      log.info(() -> String.format("Response form receipt printer is: %s", response));
    } catch (Exception e) {
      String message = String.format("Receipt Printer %s cannot be contacted", printer);
      log.error(message, e);
      throw new BusinessException(message);
    }
  }

  public void printBranchSlips(String printer, String workOrderNumber, List<String> barcodes)
      throws IOException {
    if (canPrintBranchSlips(workOrderNumber)) {
      // Print branch slips in order provided in request
      String xmlBody = printBranchSlips(workOrderNumber, barcodes);
      print(printer, "AccessioningBranchSlip", xmlBody);
    } else {
      log.info(
          "Not print the branch slips due to print_branch_slips flag is false for workOrderNumber {}",
          workOrderNumber);
    }
  }

  public void printBranchSlips(
      String printer,
      String workOrderNumber,
      List<String> barcodes,
      List<Map.Entry<String, String>> barcodesAndDestinations) {
    if (canPrintBranchSlips(workOrderNumber)) {
      // Print branch slips in order provided in request
      String xmlBody = printBranchSlips(workOrderNumber, barcodes, barcodesAndDestinations);
      print(printer, "AccessioningBranchSlip", xmlBody);
    } else {
      log.info(
          "Not print the branch slips due to print_branch_slips flag is false for workOrderNumber {}",
          workOrderNumber);
    }
  }

  public void printBranchSlips(String printer, String workOrderNumber)
      throws IOException, URISyntaxException {
    if (canPrintBranchSlips(workOrderNumber)) {
      List<Map.Entry<String, String>> itemBarcodesAndDestinations =
          workOrderService.getItemBarcodesAndDestinations(workOrderNumber);
      // Print branch slips from saved copies
      List<String> barcodes =
          itemBarcodesAndDestinations.stream()
              .map(Map.Entry::getKey)
              .sorted()
              .collect(Collectors.toList());

      String xmlBody = printBranchSlips(workOrderNumber, barcodes);
      print(printer, "AccessioningBranchSlip", xmlBody);
    } else {
      log.info(
          "Not print the branch slips due to print_branch_slips flag is false for workOrderNumber {}",
          workOrderNumber);
    }
  }

  private boolean canPrintBranchSlips(String workOrderNumber) {
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    Optional<LMSConfiguration> lmsConfigurationOptional =
        lmsConfigurationRepository.findById(customerCode);
    if (!lmsConfigurationOptional.isPresent()) {
      log.debug("LmsConfiguration with customerCode {} not found", customerCode);
      return true;
    }
    return lmsConfigurationOptional.get().isPrintBranchSlips()
        || holdingService.isPrintOnBranchSlip(workOrderNumber);
  }

  private String printBranchSlips(
      String workOrderNumber,
      List<String> barcodes,
      List<Map.Entry<String, String>> barcodesAndDestinations) {
    StringBuilder printerlXMLStringBuilder = new StringBuilder();
    String batchCodes = holdingService.getPrintOnBranchSlipBatchCodes(workOrderNumber);

    Holding holding = holdingService.getHolding(workOrderNumber);

    // Go through list of barcodes from request and put print commands in same order
    for (String barcode : barcodes) {
      Optional<String> destination =
          barcodesAndDestinations.stream()
              .filter(i -> i.getKey().equals(barcode))
              .findFirst()
              .map(Map.Entry::getValue);
      if (destination.isPresent()) {
        printerlXMLStringBuilder.append("            <text lang=\"en\" smooth=\"true\"/>\n");
        printerlXMLStringBuilder.append("            <text font=\"font_d\"/>\n");
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Work Order: %s</text><feed />%n",
                workOrderNumber));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Barcode: %s</text><feed />%n",
                StringEscapeUtils.escapeXml(barcode)));
        printerlXMLStringBuilder.append("            <feed line=\"1\" />\n");
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Collection: %s</text><feed />%n",
                holding.getCollectionCode() == null
                    ? ""
                    : StringEscapeUtils.escapeXml(holding.getCollectionCode())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Item Type: %s</text><feed />%n",
                holding.getItemTypeCode() == null
                    ? ""
                    : StringEscapeUtils.escapeXml(holding.getItemTypeCode())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Category 1: %s</text><feed />%n",
                holding.getCategoryCode1() == null
                    ? ""
                    : StringEscapeUtils.escapeXml(holding.getCategoryCode1())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Category 2: %s</text><feed />%n",
                holding.getCategoryCode2() == null
                    ? ""
                    : StringEscapeUtils.escapeXml(holding.getCategoryCode2())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Genre: %s</text><feed />%n",
                holding.getGenre() == null ? "" : StringEscapeUtils.escapeXml(holding.getGenre())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Dewey: %s</text><feed />%n",
                holding.getDewey() == null ? "" : StringEscapeUtils.escapeXml(holding.getDewey())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Cutter: %s</text><feed />%n",
                holding.getCutter() == null
                    ? ""
                    : StringEscapeUtils.escapeXml(holding.getCutter())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Call Number: %s</text><feed />%n",
                holding.getCallNumber() == null
                    ? ""
                    : StringEscapeUtils.escapeXml(holding.getCallNumber())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Spine Label Prefix: %s</text><feed />%n",
                holding.getSpineLabelPrefix() == null
                    ? ""
                    : StringEscapeUtils.escapeXml(holding.getSpineLabelPrefix())));
        printerlXMLStringBuilder.append(
            String.format(
                "            <text align=\"left\" width=\"1\" height=\"1\">Spine Label: %s</text><feed />%n",
                holding.getSpineLabel() == null
                    ? ""
                    : StringEscapeUtils.escapeXml(holding.getSpineLabel())));

        if (StringUtils.isNotBlank(batchCodes)) {
          printerlXMLStringBuilder.append("            <feed line=\"1\" />\n");
          printerlXMLStringBuilder.append(
              String.format(
                  "            <text align=\"center\" width=\"4\" height=\"4\">%s</text><feed />%n",
                  StringEscapeUtils.escapeXml(batchCodes)));
          printerlXMLStringBuilder.append(
              String.format(
                  "            <text align=\"center\" width=\"4\" height=\"4\">%s</text>%n",
                  StringEscapeUtils.escapeXml(destination.get())));
          printerlXMLStringBuilder.append("            <feed />\n");
        } else {
          printerlXMLStringBuilder.append("            <feed line=\"5\" />\n");
          printerlXMLStringBuilder.append(
              String.format(
                  "            <text align=\"center\" width=\"4\" height=\"4\">%s</text>%n",
                  StringEscapeUtils.escapeXml(destination.get())));
          printerlXMLStringBuilder.append("            <feed />\n");
        }
        printerlXMLStringBuilder.append("            <cut type=\"feed\"/>\n");
      } else {
        throw new BusinessException(String.format("Destination Not Found for barcode %s", barcode));
      }
    }

    // Send to printer
    return printerlXMLStringBuilder.toString();
  }

  private String printBranchSlips(String workOrderNumber, List<String> barcodes)
      throws IOException {
    List<Map.Entry<String, String>> barcodesAndDestinations =
        workOrderService.getItemBarcodesAndDestinations(workOrderNumber);

    return printBranchSlips(workOrderNumber, barcodes, barcodesAndDestinations);
  }

  public SummarySlipMessage printAccessioningSummarySlip(SummarySlipMessage message) {
    log.debug("Printing accessioning summary slip {}", message);
    String xmlBody = printSummarySlip(message);
    print(message.getPrinter(), "AccessioningSummarySlip", xmlBody);
    return message;
  }

  private String printSummarySlip(SummarySlipMessage message) {
    StringBuilder printerlXMLStringBuilder = new StringBuilder();
    String workOrderNumber = message.getWorkOrderNumber();

    Holding holding = null;
    List<String> images = null;

    try {
      holding = holdingService.getHolding(workOrderNumber);
      images = holdingService.getSummarySlipImages(workOrderNumber, holding);
    } catch (Exception e) {
      log.warn(
          "Cannot get holding to print summary slip for workOrderNumber: {}", workOrderNumber, e);
    }

    printerlXMLStringBuilder.append("<text align=\"center\"/>\n");
    printerlXMLStringBuilder.append("<text font=\"font_a\"/>\n");
    printerlXMLStringBuilder.append(
        String.format("<text>Accessioning Summary: %s&#10;&#10;</text>%n", workOrderNumber));
    if (holding != null) {
      printerlXMLStringBuilder.append("<text font=\"font_b\"/>\n");
      printerlXMLStringBuilder.append("<text align=\"left\"/>\n");
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Collection: %s</text>%n",
              holding.getCollectionCode() == null
                  ? ""
                  : StringEscapeUtils.escapeXml(holding.getCollectionCode())));
      printerlXMLStringBuilder.append("<text x=\"256\"/>\n");
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Dewey: %s&#10;</text>%n",
              holding.getDewey() == null ? "" : StringEscapeUtils.escapeXml(holding.getDewey())));
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Item Type: %s</text>%n",
              holding.getItemTypeCode() == null
                  ? ""
                  : StringEscapeUtils.escapeXml(holding.getItemTypeCode())));
      printerlXMLStringBuilder.append("<text x=\"256\"/>\n");
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Cutter: %s&#10;</text>%n",
              holding.getCutter() == null ? "" : StringEscapeUtils.escapeXml(holding.getCutter())));
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Category 1: %s</text>%n",
              holding.getCategoryCode1() == null
                  ? ""
                  : StringEscapeUtils.escapeXml(holding.getCategoryCode1())));
      printerlXMLStringBuilder.append("<text x=\"256\"/>\n");
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Call Number: %s&#10;</text>%n",
              holding.getCallNumber() == null
                  ? ""
                  : StringEscapeUtils.escapeXml(holding.getCallNumber())));
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Category 2: %s</text>%n",
              holding.getCategoryCode2() == null
                  ? ""
                  : StringEscapeUtils.escapeXml(holding.getCategoryCode2())));
      printerlXMLStringBuilder.append("<text x=\"256\"/>\n");
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Spine Label Prefix: %s&#10;</text>%n",
              holding.getSpineLabelPrefix() == null
                  ? ""
                  : StringEscapeUtils.escapeXml(holding.getSpineLabelPrefix())));
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Genre: %s</text>%n",
              holding.getGenre() == null ? "" : StringEscapeUtils.escapeXml(holding.getGenre())));
      printerlXMLStringBuilder.append("<text x=\"256\"/>\n");
      printerlXMLStringBuilder.append(
          String.format(
              "<text>Spine Label: %s&#10;&#10;</text>%n",
              holding.getSpineLabel() == null
                  ? ""
                  : StringEscapeUtils.escapeXml(holding.getSpineLabel())));
    }

    message
        .getTasks()
        .forEach(
            task -> {
              printerlXMLStringBuilder.append("<text font=\"font_a\"/>\n");
              printerlXMLStringBuilder.append("<text align=\"center\"/>\n");
              printerlXMLStringBuilder.append(
                  String.format(
                      "<text>%s&#10;&#10;</text>%n",
                      StringEscapeUtils.escapeXml(task.getServiceName())));
              printerlXMLStringBuilder.append("<text align=\"left\"/>\n");
              printerlXMLStringBuilder.append("<text font=\"font_b\"/>\n");

              task.getSubtasks()
                  .forEach(
                      subtask -> {
                        printerlXMLStringBuilder.append(
                            String.format(
                                "<text>[ ] %s: &#10; </text>%n",
                                StringEscapeUtils.escapeXml(subtask.getServiceComponentName())));
                        printerlXMLStringBuilder.append("<text x=\"30\"/>\n");
                        printerlXMLStringBuilder.append(
                            String.format(
                                "<text>%s&#10;&#10;</text>%n",
                                StringEscapeUtils.escapeXml(
                                    subtask.getServiceComponentDescription())));
                      });
            });
    printerlXMLStringBuilder.append("<text align=\"center\"/>\n");
    if (CollectionUtils.isEmpty(images)) {
      try {
        if (holdingService.hasGenreLabelConfig(workOrderNumber)) {
          log.info(
              "Work Order {} has a genre label configuration mismatch with its holding.",
              workOrderNumber);
        } else {
          printerlXMLStringBuilder.append("<text>GENRE LABEL NOT AVAILABLE YET&#10;</text>\n");
        }
      } catch (ResourceNotFoundException | EJBTransactionRolledbackException rex) {
        String errorMessage =
            String.format("Cannot find title order for workOrderNumber: %s", workOrderNumber);
        log.warn(errorMessage, rex);
      } catch (Exception e) {
        String errorMessage =
            String.format(
                "Cannot check genre label config to print summary slip for workOrderNumber: %s",
                workOrderNumber);
        log.warn(errorMessage, e);
        throw new BusinessException(errorMessage);
      }
    } else {
      images.forEach(image -> printerlXMLStringBuilder.append(image));
    }
    printerlXMLStringBuilder.append("<cut type=\"feed\"/>\n");
    return printerlXMLStringBuilder.toString();
  }
}
