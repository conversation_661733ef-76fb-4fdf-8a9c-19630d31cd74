package au.com.peterpal.lucyapi.core.service.retrieve;

import static au.com.peterpal.lucyapi.utils.Helper.notNull;

import java.util.ArrayList;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.entity.RecordSource;
import lucy.fulfillment.entity.OpenTitleOrder;
import org.marc4j.marc.Record;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

@Log4j2
public abstract class CustomerRecordsClient {

  @Value("${customer.client.search.product.enabled:true}")
  private boolean productSearchEnabled;

  @Value("${customer.client.search.title-author.enabled:true}")
  private boolean titleAuthorSearchEnabled;

  @Value("${customer.client.search.local-number.enabled:false}")
  private boolean localNumberSearchEnabled;

  private boolean last = false;

  /**
   * Initialise the search client using config info from sourceInfo.
   */
  protected abstract void init(RecordSource sourceInfo) throws Z3950Exception;

  /**
   * Release the search client
   */
  public abstract void close();

  /**
   * Search for matching records by title and author
   *
   * @param titleOrder
   * @return List<Record> if search succeeded, null if search failed. Runtime exceptions should only
   *     be thrown for * catastrophic errors that should cause all further searches to be abandoned.
   */
  protected abstract List<Record> titleAndAuthorSearch(OpenTitleOrder titleOrder);

  /**
   * Search for matching records by all combinations of product identifier (e.g. 13 and 10-digit
   * ISBN values)
   *
   * @param titleOrder
   * @return List<Record> if search succeeded, null if search failed. Runtime exceptions should only
   *     be thrown for * catastrophic errors that should cause all further searches to be abandoned.
   */
  protected abstract List<Record> productIdSearch(OpenTitleOrder titleOrder);

  /**
   * Search for matching records by local number - local for the target system
   *
   * @param titleOrder
   * @return List<Record> if search succeeded, null if search failed. Runtime exceptions should only
   *     be thrown for * catastrophic errors that should cause all further searches to be abandoned.
   */
  protected abstract List<Record> localNumberSearch(OpenTitleOrder titleOrder);

  public abstract String getType();

  public void retrieve(RecordSource sourceInfo, List<OpenTitleOrder> orders) {

    /*if (doInit(sourceInfo)) {
      for (int i = 0; i < orders.size(); i++) {
        try {
          return retrieveMatchingRecords(orders.get(i));
        } catch (Exception ex) {
          break;
        }
      }
    }*/
  }

  public List<Record> retrieveMatchingRecords(OpenTitleOrder titleOrder) {
    notNull(titleOrder, "Title order must not be null");

    List<Record> records = new ArrayList<>();

    // ProductIdSearch
    if (productSearchEnabled) {
      records.addAll(productIdSearch(titleOrder));
    }

    // TitleAuthorSearch
    if (titleAuthorSearchEnabled) {
      if (StringUtils.hasText(titleOrder.getAuthor())
          || StringUtils.hasText(titleOrder.getPublisherName())) {
        records.addAll(titleAndAuthorSearch(titleOrder));
      } else {
        String msg =
            "Title/author search for order %s for %s was not attempted because there is no author or publisher.";
        log.info(() ->
                String.format(msg, titleOrder.getOrderId().getTransactionIdValue(), titleOrder.getTitle()));
      }
    }

    // LocalNumberSearch
    if (localNumberSearchEnabled) {
      // records.addAll(localNumberSearch(titleOrder));
    }

    return records;
  }

  public boolean doInit(RecordSource sourceInfo) {
    boolean done = false;
    try {
      init(sourceInfo);
      done = true;
    } catch (Exception ex) {
      log.error(() -> String.format("Exception initializing customer records client with parameters %s", sourceInfo));
    }
    return done;
  }

}
