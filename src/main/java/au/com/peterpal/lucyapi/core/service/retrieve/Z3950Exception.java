package au.com.peterpal.lucyapi.core.service.retrieve;

public class Z3950Exception extends Exception {

  private String query;

  public Z3950Exception() {
  }

  public Z3950Exception(String message) {
    super(message);
  }

  public Z3950Exception(Throwable e) {
    super(e);
  }

  public Z3950Exception(String message, Throwable e) {
    super(message, e);
  }

  public Z3950Exception(String message, String query) {
    super(message);
    this.query = query;
  }

  public Z3950Exception(String message, Throwable e, String query) {
    super(message, e);
    this.query = query;
  }

  public String getQuery() {
    return query;
  }

  public void setQuery(String query) {
    this.query = query;
  }

}
