package au.com.peterpal.lucyapi.core.service;

import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.marc4j.MarcJsonWriter;
import org.marc4j.marc.Record;

@Log4j2
public class MarcStreamToJsonReader {

  public static final String MARC = "marc";

  private MarcStreamToJsonReader() {
    throw new IllegalStateException("Utility class");
  }

  public static String convertMarcInJson(Record marcRecord) {
    String jsonResult = null;
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    MarcJsonWriter writer = new MarcJsonWriter(out, MarcJsonWriter.MARC_IN_JSON);
    writer.write(marcRecord);
    try {
      jsonResult = out.toString("UTF-8");
    } catch (UnsupportedEncodingException e) {
      log.error(
          String.format(
              "Error while converting MRC record to json, due to: %s",
              ExceptionUtils.getRootCause(e)));
    }
    writer.close();

    return jsonResult;
  }
}
