package au.com.peterpal.lucyapi.core.service.retrieve;

import java.text.MessageFormat;
import java.util.List;
import java.util.ListIterator;
import org.apache.commons.lang.StringUtils;

public class Z3950Utils {

  private Z3950Utils() {
    throw new IllegalStateException("Utility class");
  }

  public static String createTerm(String query, String value) {
    return MessageFormat.format(query, value);
  }

  public static String createExpression(String operator, String value1, String value2) {
    return MessageFormat.format(operator, value1, value2 );
  }

  public static String quoteValue(String value) {
    return MessageFormat.format("\"{0}\"", value);
  }
  /*
   * Remove or replace characters that are unsupported by the Z39.50 servers
   * that we query.
   * '-' - has to be replaced with a space for Symphony & Spydus
   * '#' - has to be replaced with a space for Library of Congress
   * '&' - has to be replaced with a space for all
   * '?' - has to be removed for Libraries Australia
   * '\'' - has to be removed for Spydus
   * '"' - has to be removed for all
   */
  public static String cleanTitleValue(String value) {
    if (!StringUtils.containsNone(value, ":/")) {
      value = StringUtils.substring(value, 0, StringUtils.indexOfAny(value, ":/"));
    }

    return StringUtils.replaceChars(value, "-#&?'\"", "   ");
  }

  public static String cleanAuthorValue(String value) {
    if (!StringUtils.containsNone(value, ",:/")) {
      value = StringUtils.substring(value, 0, StringUtils.indexOfAny(value, ",:/"));
    }

    return StringUtils.replaceChars(value, "-#&?'\"", "   ");
  }

  public static String createQuery(List<String> terms) {
    StringBuilder query = new StringBuilder(Z3950Constants.QUERY_PREFIX);

    ListIterator<String> termsIterator = terms.listIterator(terms.size());

    while (termsIterator.hasPrevious()) {
      query.append(termsIterator.previous()).append(" ");
    }

    return query.toString();
  }
}
