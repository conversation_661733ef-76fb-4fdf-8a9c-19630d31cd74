package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.model.spydus.JiraCreateTicketRequest;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

@Service
@Log4j2
public class JiraWebhookService {
    private final String uploadBibRecordWebhookUrl;
    private final String deleteOnOrderWebhookUrl;
    private final String deleteOnOrderWebhookSecret;
    private final String uploadBibRecordWebhookSecret;

    public JiraWebhookService(
        @Value("${jira.webhook.url:https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018bd076-11db-7dc8-bc84-f83f0354219c}")
        String uploadBibRecordWebhookUrl,
        @Value("${jira.webhook.secret:be28d1ef0b2d205ab0d02b216c74fdf387dc75c4}")
        String uploadBibRecordWebhookSecret,
        @Value("${jira.deleteOnOrder.webhook.url:https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c192f-563e-7213-b08e-c12f608e2058}")
        String deleteOnOrderWebhookUrl,
        @Value("${jira.deleteOnOrder.webhook.secret:824e49236c74cd904ea7daf8553c738d7ad6fdbb}")
        String deleteOnOrderWebhookSecret) {
        this.uploadBibRecordWebhookUrl = uploadBibRecordWebhookUrl;
        this.deleteOnOrderWebhookUrl = deleteOnOrderWebhookUrl;
        this.deleteOnOrderWebhookSecret = deleteOnOrderWebhookSecret;
        this.uploadBibRecordWebhookSecret = uploadBibRecordWebhookSecret;
    }

    public void createUploadBibRecordJiraTicket(Integer batchId, JiraCreateTicketRequest requestBodyObject) {

        Mono<String> response = WebClient.create().post()
            .uri(uploadBibRecordWebhookUrl)
            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .header("X-Automation-Webhook-Token", uploadBibRecordWebhookSecret)
            .body(BodyInserters.fromObject(requestBodyObject))
            .retrieve()
            .bodyToMono(String.class);

        String responseBody = response.block();

        log.info("Created Jira ticket for batchId {} with response {}", batchId, responseBody);
    }

    public void createDeleteOnOrderJiraTicket(Map<String, Object> jiraMetadata) {
        createJiraTicket(jiraMetadata, deleteOnOrderWebhookUrl, deleteOnOrderWebhookSecret);
    }

    public void createJiraTicket(Map<String, Object> jiraMetadata, String webhookUrl, String secret) {
        Mono<String> response = WebClient.create().post()
            .uri(webhookUrl)
            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .header("X-Automation-Webhook-Token", secret)
            .body(BodyInserters.fromObject(jiraMetadata))
            .retrieve()
            .bodyToMono(String.class);

        String responseBody = response.block();

        log.info("Created jira ticket by webhook url {} for request {} with response {}",
            webhookUrl,  jiraMetadata, responseBody);
    }
}
