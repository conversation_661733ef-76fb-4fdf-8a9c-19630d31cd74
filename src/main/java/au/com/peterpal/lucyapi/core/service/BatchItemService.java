package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.persistence.lucyapi.BatchItemRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.BatchItem;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Log4j2
public class BatchItemService {

  private final BatchItemRepository batchItemRepository;

  public BatchItemService(BatchItemRepository batchItemRepository) {
    this.batchItemRepository = batchItemRepository;
  }

  @Transactional
  public void storeBarcodes(
      String workOrderNumber,
      Integer batchId,
      Set<String> barCodes,
      boolean fullBibRecordsCreated,
      UUID invoiceGroupId,
      UUID invoiceId) {
    if (barCodes.isEmpty()) {
      log.error("barCodes are empty for batchId {}", batchId);
      return;
    }
    List<BatchItem> existingBatchItems =
        batchItemRepository.findAllByWorkOrderNumber(workOrderNumber);
    List<BatchItem> updatingBatchItems =
        barCodes.stream()
            .map(
                barCode ->
                    BatchItem.builder()
                        .id(
                            existingBatchItems.stream()
                                .filter(e -> e.getItemBarcode().equals(barCode))
                                .findFirst()
                                .map(BatchItem::getId)
                                .orElse(UUID.randomUUID()))
                        .workOrderNumber(workOrderNumber)
                        .batchId(batchId)
                        .itemBarcode(barCode)
                        .fullBibRecordsCreated(fullBibRecordsCreated)
                        .invoiceGroupId(invoiceGroupId)
                        .invoiceId(invoiceId)
                        .build())
            .collect(Collectors.toList());

    batchItemRepository.saveAll(updatingBatchItems);
  }

  @Transactional
  public void storeBarcode(
      String workOrderNumber,
      Integer batchId,
      String barCode,
      boolean fullBibRecordsCreated,
      UUID invoiceGroupId,
      UUID invoiceId) {
    List<BatchItem> existingBatchItems =
        batchItemRepository.findAllByWorkOrderNumber(workOrderNumber);

    BatchItem batchItem = BatchItem.builder()
        .id(
            existingBatchItems.stream()
                .filter(e -> e.getItemBarcode().equals(barCode))
                .findFirst()
                .map(BatchItem::getId)
                .orElse(UUID.randomUUID()))
        .workOrderNumber(workOrderNumber)
        .batchId(batchId)
        .itemBarcode(barCode)
        .fullBibRecordsCreated(fullBibRecordsCreated)
        .invoiceGroupId(invoiceGroupId)
        .invoiceId(invoiceId)
        .build();

    batchItemRepository.save(batchItem);
  }

  public List<BatchItem> findAllByWorkOrderNumber(String workOrderNumber) {
    return batchItemRepository.findAllByWorkOrderNumber(workOrderNumber);
  }

  @Transactional
  public void saveAll(List<BatchItem> batchItems) {
    batchItemRepository.saveAll(batchItems);
  }

  public Set<Integer> getAllBatchIds(Set<UUID> invoiceIds) {
    return batchItemRepository.findAllByInvoiceIdIn(invoiceIds).stream()
        .map(BatchItem::getBatchId)
        .collect(Collectors.toSet());
  }

  public List<BatchItem> findAllByWorkOrderNumberIn(Set<String> workOrders) {
    return batchItemRepository.findAllByWorkOrderNumberIn(workOrders);
  }

  @Transactional
  public void deleteByBarcodeAndInvoiceIdIn(String removedBarcode, Set<UUID> invoiceIds) {
    batchItemRepository.deleteByItemBarcodeAndInvoiceIdIn(removedBarcode, invoiceIds);
  }
}
