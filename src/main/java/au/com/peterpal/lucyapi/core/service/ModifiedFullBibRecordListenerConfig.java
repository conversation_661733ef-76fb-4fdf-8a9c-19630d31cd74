package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.model.ModifiedFullBibRecordMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.jms.dsl.Jms;

import javax.jms.ConnectionFactory;
import javax.jms.Session;


@Log4j2
@Configuration
@EnableIntegration
public class ModifiedFullBibRecordListenerConfig {

    private final ConnectionFactory connectionFactory;
    private final HoldingService holdingService;
    private final String modifiedFullBibRecordEvents;

    public ModifiedFullBibRecordListenerConfig(
        ConnectionFactory connectionFactory,
        HoldingService holdingService,
        @Value("${events.modified-full-bib-record.channel:orchestration:lucy-api.modified-full-bib-records-events}")
            String modifiedFullBibRecordEvents) {
        this.connectionFactory = connectionFactory;
        this.holdingService = holdingService;
        this.modifiedFullBibRecordEvents = modifiedFullBibRecordEvents;
    }

    @Bean
    public IntegrationFlow modifiedFullBibRecordEventsJmsListener() {
        return IntegrationFlows.from(
                Jms.messageDrivenChannelAdapter(this.connectionFactory)
                    .destination(modifiedFullBibRecordEvents)
                    .configureListenerContainer(
                        spec ->
                            spec.sessionTransacted(true)
                                .sessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE)))
            .enrichHeaders(
                headerEnricherSpec ->
                    headerEnricherSpec.headerFunction(
                        IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID,
                        message ->
                            IncomingMessageProcessor.generateIncomingMessageId(message.getPayload()),
                        true))
            .wireTap(IncomingMessageLoggingControlFlow.LOG_INCOMING_MESSAGE_CHANNEL)
            .log(m -> "Received via JMS: " + m)
            .handle(ModifiedFullBibRecordMessage.class, (p, m) -> holdingService.addModifiedFullBibRecord(p))
            .log(message -> "handled: " + message)
            .get();
    }

}
