package au.com.peterpal.lucyapi.core.service.spydus;

import static org.springframework.http.HttpStatus.UNAUTHORIZED;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.core.service.JiraWebhookService;
import au.com.peterpal.lucyapi.model.spydus.ProcessBatchResponse;
import au.com.peterpal.lucyapi.persistence.lucyapi.LMSConfigurationRepositoryCustom;
import au.com.peterpal.lucyapi.persistence.lucyapi.SpydusCookieRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.*;
import au.com.peterpal.lucyapi.utils.TOTPUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@Log4j2
@Component
public class SpydusClient {
  private final LMSConfigurationRepositoryCustom lmsConfigurationRepositoryCustom;
  private final Map<String, LMSConfiguration> lmsConfigs;
  private final Map<String, SpydusAuthentication> spydusAuthentications;
  private final JiraWebhookService jiraWebhookService;
  private final SpydusCookieRepository cookieRepository;
  @Value("#{'${spydus.irn.date-formats}'.split(',')}")
  private List<String> dateFormatPatterns;

  public SpydusClient(
      LMSConfigurationRepositoryCustom lmsConfigurationRepositoryCustom,
      JiraWebhookService jiraWebhookService,
      SpydusCookieRepository cookieRepository) {
    this.lmsConfigurationRepositoryCustom = lmsConfigurationRepositoryCustom;
    this.jiraWebhookService = jiraWebhookService;
    this.cookieRepository = cookieRepository;
    lmsConfigs = new HashMap<>();
    spydusAuthentications = new HashMap<>();

    String property = "sun.net.http.allowRestrictedHeaders";
    System.getProperties().setProperty(property, "true");
  }

  public void getBatchMarcsBrief(String customerCode, String sessionId, String filename) {
    log.debug(
        "Getting batch marcs brief for customerCode {}, sessionId {}, filename {}",
        customerCode,
        sessionId,
        filename);
    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    SpydusAuthentication auth =
        spydusAuthentications.getOrDefault(customerCode, new SpydusAuthentication());

    BatchMarcsBriefRequest request =
        BatchMarcsBriefRequest.builder()
            .selectedLoadMarcs("L1")
            .start(1)
            .allTheRest(true)
            .numberOfRecords(1)
            .filename(filename)
            .pageSize(0)
            .sessionId(sessionId)
            .build();

    WebClient.Builder webClientBuilder =
        WebClient.builder()
            .baseUrl(
                String.format("%s/spydus/api/Marc/GetBatchMarcsBrief", lmsConfiguration.getUrl()));
    Optional<SpydusCookie> spydusCookieOptional = cookieRepository.findById(lmsConfiguration.getVersion());
    if (spydusCookieOptional.isPresent()) {
      webClientBuilder
          .defaultHeader("X-XSRF-TOKEN", auth.getXSRFToken())
          .defaultHeader("Cookie", getCookie(auth, spydusCookieOptional.get().getCookie()));
    } else {
      webClientBuilder
          .defaultHeader("CSRFtoken", auth.getCSRFToken())
          .defaultHeader("Cookie", getCookie(auth));
    }
    String responseBody =
        webClientBuilder
            .build()
            .post()
            .contentType(MediaType.APPLICATION_JSON)
            .body(BodyInserters.fromObject(request))
            .retrieve()
            .bodyToMono(String.class)
            .block();
    log.debug(
        "Done for get batch marcs brief for customerCode {}, sessionId {}, filename {} with response {}",
        customerCode,
        sessionId,
        filename,
        responseBody);
  }

  private String getCookie(SpydusAuthentication auth) {
    return String.format(
        "SPYDUS_SESSIONID_443=%s; .ASPXAUTH=%s; ASPLOGININFO_443=%s",
        auth.getSPYDUS_SESSIONID_443_COOKIE(),
        auth.getASPXAUTH_COOKIE(),
        auth.getASPLOGININFO_443_COOKIE());
  }

  private String getCookie(SpydusAuthentication auth, String cookie) {
    return String.format(
        "%s; SPYDUS_SESSIONID_443=%s; ASPLOGININFO_443=%s; XSRF-TOKEN=%s;",
        cookie,
        auth.getSPYDUS_SESSIONID_443_COOKIE(),
        auth.getASPLOGININFO_443_COOKIE(),
        auth.getXSRFToken());
  }

  public void getBatchMarcsBriefWithRetry(String customerCode, String sessionId, String filename) {
    try {
      getBatchMarcsBrief(customerCode, sessionId, filename);
    } catch (WebClientResponseException e) {
      if (e.getStatusCode() == UNAUTHORIZED) {
        // retry when authentication token expired
        log.warn(
            "Can not get batch marc brief for customer code {}, sessionId {} and filename {} with retry......",
            customerCode,
            sessionId,
            filename);
        spydusAuthentications.remove(customerCode);
        getBatchMarcsBrief(customerCode, sessionId, filename);
      } else {
        throw e;
      }
    }
  }

  public int getNumberRecordsWithRetry(String customerCode, String sessionId) {
    try {
      return getNumberRecords(customerCode, sessionId);
    } catch (WebClientResponseException e) {
      if (e.getStatusCode() == UNAUTHORIZED) {
        // retry when authentication token expired
        log.warn(
            "Can not get number of records for customer code {} and sessionId {} with retry......",
            customerCode,
            sessionId);
        spydusAuthentications.remove(customerCode);
        return getNumberRecords(customerCode, sessionId);
      } else {
        throw e;
      }
    }
  }

  private int getNumberRecords(String customerCode, String sessionId) {
    log.debug(
        "Getting no of records for uploaded files with customer {}, sessionId {}",
        customerCode,
        sessionId);

    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    SpydusAuthentication auth =
        spydusAuthentications.getOrDefault(customerCode, new SpydusAuthentication());
    Integer noOfRecords =
        WebClient.builder()
            .baseUrl(
                String.format(
                    "%s/spydus/api/Marc/GetNumRecords?Filename=%s.mrx",
                    lmsConfiguration.getUrl(), sessionId))
            .defaultHeader("CSRFtoken", auth.getCSRFToken())
            .defaultHeader("Cookie", getCookie(auth))
            .build()
            .get()
            .retrieve()
            .bodyToMono(Integer.class)
            .block();
    log.debug(
        "Found {} records for uploaded files with customer {}, sessionId {}",
        noOfRecords,
        customerCode,
        sessionId);
    return noOfRecords != null ? noOfRecords : 0;
  }

  public List<ProcessBatchResponse> processBatchWithRetry(
      String customerCode, String sessionId, int numberOfRecords, int loadType) {
    try {
      return processBatch(customerCode, sessionId, numberOfRecords, loadType);
    } catch (WebClientResponseException e) {
      if (e.getStatusCode() == UNAUTHORIZED) {
        // retry when authentication token expired
        log.warn(
            "Can not process uploaded file for customer code {} and sessionId {} with retry......",
            customerCode,
            sessionId);
        spydusAuthentications.remove(customerCode);
        return processBatch(customerCode, sessionId, numberOfRecords, loadType);
      } else {
        throw e;
      }
    }
  }

  public List<ProcessBatchResponse> processBatch(
      String customerCode, String sessionId, int numberOfRecords, int loadType) {
    log.debug(
        "Processing batch for uploaded files with customer {}, sessionId {}, noOfRecords {}",
        customerCode,
        sessionId,
        numberOfRecords);
    List<ProcessBatchResponse> result = new ArrayList<>();
    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    SpydusAuthentication auth =
        spydusAuthentications.getOrDefault(customerCode, new SpydusAuthentication());
    for (int i = 0; i < numberOfRecords; i = i + 10) {
      int maxOfList = Math.min(i + 9, numberOfRecords - 1);
      List<Integer> recordIds =
          IntStream.rangeClosed(i, maxOfList).boxed().collect(Collectors.toList());
      ProcessBatchResponse[] processBatchResponses =
          processBatch(
              customerCode,
              sessionId,
              lmsConfiguration,
              auth,
              recordIds.stream().map(String::valueOf).collect(Collectors.toList()),
              loadType);
      result.addAll(Lists.newArrayList(processBatchResponses));
    }
    log.debug(
        "Processed batch for uploaded files with customer {}, sessionId {}, noOfRecords {}",
        customerCode,
        sessionId,
        numberOfRecords);
    return result;
  }

  private ProcessBatchResponse[] processBatch(
      String customerCode,
      String sessionId,
      LMSConfiguration lmsConfiguration,
      SpydusAuthentication auth,
      List<String> recordIds,
      int loadType) {
    log.debug(
        "Processing uploaded marc file for customerCode {}, sessionId {}, recordIds {} ",
        customerCode,
        sessionId,
        recordIds);

    ProcessMarcFileRequest request =
        ProcessMarcFileRequest.builder()
            .isBulkEdit(false)
            .loadControl(lmsConfiguration.getLoadControl())
            .loadType(loadType)
            .sessionId(sessionId)
            .recordIds(recordIds)
            .marcSpecId("MARCSPEC")
            .build();

    WebClient.Builder webClientBuilder =
        WebClient.builder()
            .baseUrl(String.format("%s/spydus/api/Marc/PostMarcBatch", lmsConfiguration.getUrl()));
    Optional<SpydusCookie> spydusCookieOptional = cookieRepository.findById(lmsConfiguration.getVersion());
    if (spydusCookieOptional.isPresent()) {
      webClientBuilder
          .defaultHeader("X-XSRF-TOKEN", auth.getXSRFToken())
          .defaultHeader("Cookie", getCookie(auth, spydusCookieOptional.get().getCookie()));
    } else {
      webClientBuilder
          .defaultHeader("CSRFtoken", auth.getCSRFToken())
          .defaultHeader("Cookie", getCookie(auth));
    }
    ProcessBatchResponse[] responseBody =
        webClientBuilder
            .build()
            .post()
            .contentType(MediaType.APPLICATION_JSON)
            .body(BodyInserters.fromObject(request))
            .retrieve()
            .bodyToMono(ProcessBatchResponse[].class)
            .block();

    log.debug(
        "Processed marc file for customerCode {}, sessionId {}, recordIds {} with response {}",
        customerCode,
        sessionId,
        recordIds,
        responseBody);
    return responseBody;
  }

  public String uploadBatchWithRetry(String customerCode, File fileToUpload) {
    try {
      return uploadBatch(customerCode, fileToUpload);
    } catch (WebClientResponseException e) {
      if (e.getStatusCode() == UNAUTHORIZED) {
        // retry when authentication token expired
        log.warn(
            "Can not upload bid records for customer code {} and filename {} with retry......",
            customerCode,
            fileToUpload.getName());
        spydusAuthentications.remove(customerCode);
        return uploadBatch(customerCode, fileToUpload);
      } else {
        throw e;
      }
    }
  }

  private String uploadBatch(String customerCode, File fileToUpload) {
    log.debug(
        "Uploading marc file {} to spydus for customer {}", fileToUpload.getName(), customerCode);

    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    WebClient webClient =
        WebClient.builder()
            .baseUrl(
                String.format("%s/spydus/MarcFileUpload/SaveMarcFile", lmsConfiguration.getUrl()))
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORM_DATA_VALUE)
            .build();

    MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
    parts.add("files", new FileSystemResource(fileToUpload));

    String responseBody =
        webClient
            .method(HttpMethod.POST)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .body(BodyInserters.fromMultipartData(parts))
            .retrieve()
            .bodyToMono(String.class)
            .block();

    log.debug(
        "Uploaded marc file for customerCode {}, filename {} with response {}",
        customerCode,
        fileToUpload.getName(),
        responseBody);
    return responseBody;
  }

  public void deleteOnOrderRecordWithRetry(
      String customerCode, String isbn, Map<String, Object> jiraMetadata) {
    try {
      deleteOnOrderRecord(customerCode, isbn, jiraMetadata);
    } catch (WebClientResponseException e) {
      if (e.getStatusCode() == UNAUTHORIZED) {
        // retry when authentication token expired
        log.warn(
            "Can not delete order records for customer code {} and isbn {} with retry......",
            customerCode,
            isbn);
        spydusAuthentications.remove(customerCode);
        deleteOnOrderRecord(customerCode, isbn, jiraMetadata);
      } else {
        throw e;
      }
    }
  }

  private void deleteOnOrderRecord(
      String customerCode, String isbn, Map<String, Object> jiraMetadata) {
    log.info("Deleting order record for customerCode {}, isbn {}", customerCode, isbn);
    Set<String> irns = getIrns(customerCode, isbn);
    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    Map<String, SpydusIrnDetail.Result> mapOfIrnDetails = new HashMap<>();
    for (String irn : irns) {
      SpydusIrnDetail irnDetails = getIrnDetails(customerCode, irn);
      irnDetails.getResult().stream()
          .filter(r -> isOnOrderHolding(lmsConfiguration, r))
          .forEach(
              result -> {
                if (mapOfIrnDetails.put(result.getIrn(), result) != null) {
                  jiraMetadata.put("customerCode", customerCode);
                  jiraMetadata.put("isbn", isbn);
                  jiraWebhookService.createDeleteOnOrderJiraTicket(jiraMetadata);
                  throw new BusinessException(
                      "There is multiple on-order holdings across multiple records");
                }
              });
    }
    if (mapOfIrnDetails.isEmpty()) {
      log.info("Cannot find any irn for {}", lmsConfiguration.getOnOrderHoldingText());
      return;
    }
    String irn =
        mapOfIrnDetails.values().stream()
            .min(Comparator.comparing(r -> r.getParsedDateAcquired(dateFormatPatterns)))
            .get()
            .getIrn();
    if (StringUtils.startsWithIgnoreCase(lmsConfiguration.getVersion(), "V11")) {
      submitDeleteOnOrderRecordV11(customerCode, Lists.newArrayList(irn));
    } else {
      submitDeleteOnOrderRecord(customerCode, Lists.newArrayList(irn));
    }
  }

  private boolean isOnOrderHolding(LMSConfiguration lmsConfiguration, SpydusIrnDetail.Result r) {
    boolean onOrderHolding =
        StringUtils.isNotBlank(lmsConfiguration.getOnOrderHoldingText())
            && Pattern.compile(lmsConfiguration.getOnOrderHoldingText())
                .matcher(r.getText())
                .matches();
    log.debug(
        "The result of checking text of holding: {} with pattern {} is: {}",
        r.getText(),
        lmsConfiguration.getOnOrderHoldingText(),
        onOrderHolding);
    return onOrderHolding;
  }

  private void submitDeleteOnOrderRecordV11(
      String customerCode, List<String> irns) {
    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    SpydusAuthentication auth =
        spydusAuthentications.getOrDefault(customerCode, new SpydusAuthentication());
    SpydusDeleteItemRequest spydusDeleteItemRequest =
        SpydusDeleteItemRequest.builder().irns(irns).build();
    SpydusCookie spydusCookie = getSpydusCookie(lmsConfiguration.getVersion());
    String deletedItemResponse =
        WebClient.builder()
            .baseUrl(
                String.format(
                    "%s/spydus/api/itemMaintenance/DeleteItems", lmsConfiguration.getUrl()))
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader("X-XSRF-TOKEN", auth.getXSRFToken())
            .defaultHeader("Cookie", getCookie(auth, spydusCookie.getCookie()))
            .build()
            .post()
            .contentType(MediaType.APPLICATION_JSON)
            .body(BodyInserters.fromObject(spydusDeleteItemRequest))
            .retrieve()
            .bodyToMono(String.class)
            .block();

    log.info(
        "deleted order records for customer {} with response {}",
        customerCode,
        deletedItemResponse);
  }

  private SpydusCookie getSpydusCookie(String version) {
    return cookieRepository
        .findById(version)
        .orElseThrow(
            () ->
                new BusinessException(
                    String.format(
                        "Cookie for version %s  not found", version)));
  }

  private void submitDeleteOnOrderRecord(String customerCode, List<String> deletedIrns) {
    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    SpydusAuthentication auth =
        spydusAuthentications.getOrDefault(customerCode, new SpydusAuthentication());
    String body =
        deletedIrns.stream().map(irn -> "irns%5B%5D=" + irn).collect(Collectors.joining("&"));

    String deletedItemResponse =
        WebClient.builder()
            .baseUrl(
                String.format(
                    "%s/spydus/api/itemMaintenance/DeleteItems", lmsConfiguration.getUrl()))
            .defaultHeader("CSRFtoken", auth.getCSRFToken())
            .defaultHeader("Cookie", getCookie(auth))
            .build()
            .post()
            .contentType(MediaType.APPLICATION_FORM_URLENCODED)
            .body(BodyInserters.fromObject(body))
            .retrieve()
            .bodyToMono(String.class)
            .block();

    log.info(
        "deleted order records for customer {} with response {}",
        customerCode,
        deletedItemResponse);
  }

  private SpydusIrnDetail getIrnDetails(String customerCode, String irn) {
    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    SpydusAuthentication auth =
        spydusAuthentications.getOrDefault(customerCode, new SpydusAuthentication());
    SpydusIrnDetail irnDetail =
        WebClient.builder()
            .baseUrl(
                String.format(
                    "%s/spydus/maintenance/LoadItemsByParentIrnViaScan?parentIrn=%s&issIrn=0",
                    lmsConfiguration.getUrl(), irn))
            .defaultHeader("CSRFtoken", auth.getCSRFToken())
            .defaultHeader("Cookie", getCookie(auth))
            .build()
            .get()
            .retrieve()
            .bodyToMono(SpydusIrnDetail.class)
            .block();
    log.debug(
        "Found Irn detail for customer {}, irn {} with response {}", customerCode, irn, irnDetail);
    return irnDetail;
  }

  private Set<String> getIrns(String customerCode, String isbn) {
    log.debug("Getting Irn for customerCode {}, isbn {}", customerCode, isbn);
    LMSConfiguration lmsConfiguration = lmsConfigs.get(customerCode);
    SpydusAuthentication auth =
        spydusAuthentications.getOrDefault(customerCode, new SpydusAuthentication());

    String body =
        "ACN=&BRN=&SBN=+"
            + isbn
            + "&SSN=&ENTRY1_NAME=TI&ENTRY1=&ENTRY1_TYPE=K"
            + "&ENTRY2_NAME=AU&ENTRY2=&ENTRY2_TYPE=K&ENTRY3_NAME=SU&ENTRY3=&ENTRY3_TYPE=K"
            + "&ENTRY5_NAME=DDC&ENTRY5=&PD=&BIBCD=&BIBCUSR=&BIBUD=&BIBUUSR=&NRECS=20"
            + "&SORTS=HBT.SOVR&SEARCH_FORM=%2Fcgi-bin%2Fspydus.exe%2FMSGTRN%2FCAT%2FBIB%2FAPP&_SPQ=1"
            + "&FORM_DESC=Cataloguing+-+Bibliographic+Search&QRY=FORMAT%3A+BIB&ISGLB=1&MODE=CAT&CF=BIB";

    String responseBody =
        WebClient.builder()
            .baseUrl(
                String.format("%s/cgi-bin/spydus.exe/ENQ/CAT/BIBENQ", lmsConfiguration.getUrl()))
            .defaultHeader("Cookie", getCookie(auth))
            .build()
            .post()
            .contentType(MediaType.APPLICATION_FORM_URLENCODED)
            .body(BodyInserters.fromObject(body))
            .retrieve()
            .bodyToMono(String.class)
            .block();
    log.debug(
        "Found Irn for customerCode {}, isbn {} with response {}",
        customerCode,
        isbn,
        responseBody);
    Set<String> irns = parseIrns(responseBody);
    if (irns.isEmpty()) {
      log.error(String.format("Cannot parse IRN from responseBody %s", responseBody));
    } else {
      log.info("Found IRN: {} for for customerCode {}, isbn {}", irns, customerCode, isbn);
    }
    return irns;
  }

  private Set<String> parseIrns(String responseBody) {
    Set<String> irns = parseIrns(responseBody, "IRN=\"(\\d+)\"");
    if (irns.isEmpty()) {
      irns = parseIrns(responseBody, "<article class=\"row brief-result\" id=\"(\\d+)\">");
    }
    return irns;
  }

  private Set<String> parseIrns(String responseBody, String pattern) {
    Set<String> irns = new HashSet<>();

    Pattern r = Pattern.compile(pattern);
    Matcher m = r.matcher(responseBody);
    while (m.find()) {
      irns.add(m.group(1));
    }
    return irns;
  }

  public void loadLMS(String customerCode) {
    if (customerCode == null) throw new BusinessException("Define customer code first");

    List<LMSConfiguration> configList =
        lmsConfigurationRepositoryCustom.findByCustomerCode(customerCode);
    if (configList.size() != 1)
      throw new BusinessException(
          String.format("Customer configuration missing for customer %s", customerCode));
    lmsConfigs.put(customerCode, configList.get(0));
    authenticate(customerCode);
  }

  private void authenticate(String customerCode) {
    LMSConfiguration config = lmsConfigs.get(customerCode);
    SpydusAuthentication auth =
        spydusAuthentications.getOrDefault(config.getCustomerCode(), new SpydusAuthentication());
    auth.setCustomerCode(customerCode);
    if (StringUtils.startsWithIgnoreCase(config.getVersion(), "V10")) {
      login(config, auth);
      preinit(config, auth);
      getBasicToken(config, auth);
      getBearerToken(config, auth);
    } else if (StringUtils.startsWithIgnoreCase(config.getVersion(), "V11")) {
      String cookie = getCookie(auth, getSpydusCookie(config.getVersion()).getCookie());
      loginVer11(config, auth, cookie);
      tokenV11(config, auth, cookie);
      cookie = getCookie(auth, getSpydusCookie(config.getVersion()).getCookie());
      initV11(config, cookie);
      getBasicToken(config, auth);
      getBearerToken(config, auth);
    } else {
      throw new BusinessException(
          String.format("Can not find the version of spydus for customer code %s", customerCode));
    }

    spydusAuthentications.put(config.getCustomerCode(), auth);
  }

  private void loginVer11(LMSConfiguration config, SpydusAuthentication auth, String cookie) {
    String baseUrl = config.getUrl() + "/spydus/Account/Login";

    WebClient webClient =
        WebClient.builder()
            .baseUrl(baseUrl)
            .defaultHeader(HttpHeaders.ACCEPT, "application/json, text/javascript, */*; q=0.01")
            .defaultHeader(HttpHeaders.ACCEPT_LANGUAGE, "en-US,en;q=0.9")
            .defaultHeader(HttpHeaders.CONNECTION, "keep-alive")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, "application/json; charset=UTF-8")
            .defaultHeader(HttpHeaders.ORIGIN, config.getUrl())
            .defaultHeader(HttpHeaders.COOKIE, cookie)
            .defaultHeader(HttpHeaders.REFERER, config.getUrl() + "/spydus")
            .build();

    String encryptedPassword = encrypt(config.getPassword(), config.getPublicKey());
    SpydusLoginRequest loginRequest =
        SpydusLoginRequest.builder()
            .userName(config.getUsername())
            .password(encryptedPassword)
            .isForced(false)
            .location(config.getLocation())
            .authKey(TOTPUtils.generateOneTimePassword(config.getSpydusOneTimeLoginSecret()))
            .subLocation(config.getSublocation())
            .build();

    ClientResponse clientResponse =
        webClient
            .post()
            .contentType(MediaType.APPLICATION_JSON)
            .body(BodyInserters.fromObject(loginRequest))
            .exchange()
            .block();

    List<String> cookies = null;
    if (clientResponse != null) {
      cookies = clientResponse.headers().asHttpHeaders().get("Set-Cookie");
      String responseBody = clientResponse.bodyToMono(String.class).block();

      log.info("Spydus v11 Login Response Body {}", responseBody);
    }

    if (cookies != null) {
      auth.setSPYDUS_SESSIONID_443_COOKIE(getCookie("SPYDUS_SESSIONID_443", cookies));
      auth.setASPLOGININFO_443_COOKIE(getCookie("ASPLOGININFO_443", cookies));
    }
  }

  private String encryptAndUrlEncode(String password, String publicKey) {
    try {
      String encryptedPassword = encryptPassword(password, publicKey);
      return URLEncoder.encode(encryptedPassword, StandardCharsets.UTF_8.toString());
    } catch (Exception e) {
      log.error("Can not encrypt password");
      throw new BusinessException("Can not encrypt password");
    }
  }

  private String encrypt(String password, String publicKey) {
    try {
      return encryptPassword(password, publicKey);
    } catch (Exception e) {
      log.error("Can not encrypt password");
      throw new BusinessException("Can not encrypt password");
    }
  }

  private String encryptPassword(String password, String publicKey)
      throws InvalidKeySpecException,
          NoSuchAlgorithmException,
          NoSuchPaddingException,
          InvalidKeyException,
          IllegalBlockSizeException,
          BadPaddingException {
    Key key =
        KeyFactory.getInstance("RSA")
            .generatePublic(new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey)));
    Cipher cipher = Cipher.getInstance("RSA");
    cipher.init(Cipher.ENCRYPT_MODE, key);
    byte[] encryptedBytes = cipher.doFinal(password.getBytes());
    return Base64.getEncoder().encodeToString(encryptedBytes);
  }

  private void tokenV11(LMSConfiguration config, SpydusAuthentication auth, String cookie) {
    String baseUrl = config.getUrl() + "/spydus/antiforgery/token";
    WebClient webClient =
        WebClient.builder()
            .defaultHeader(HttpHeaders.ACCEPT, "*/*")
            .defaultHeader(HttpHeaders.ACCEPT_LANGUAGE, "en-US,en;q=0.9")
            .defaultHeader(HttpHeaders.AUTHORIZATION, "undefined")
            .defaultHeader(HttpHeaders.CONNECTION, "keep-alive")
            .defaultHeader(HttpHeaders.COOKIE, cookie)
            .defaultHeader(HttpHeaders.REFERER, config.getUrl() + "/spydus")
            .build();

    ClientResponse clientResponse = webClient.get().uri(baseUrl).exchange().block();
    List<String> cookies = null;
    if (clientResponse != null) {
      cookies = clientResponse.headers().asHttpHeaders().get("Set-Cookie");
      String responseBody = clientResponse.bodyToMono(String.class).block();

      log.info("Spydus v11 Token Response Body {}", responseBody);
    }

    if (cookies != null) {
      auth.setXSRFToken(getCookie("XSRF-TOKEN", cookies));
    }
  }

  private void initV11(LMSConfiguration config, String cookie) {
    String baseUrl = config.getUrl() + "/spydus/Home/init";
    WebClient webClient =
        WebClient.builder()
            .defaultHeader(HttpHeaders.ACCEPT, "*/*")
            .defaultHeader(HttpHeaders.ACCEPT_LANGUAGE, "en-US,en;q=0.9")
            .defaultHeader(HttpHeaders.CONNECTION, "keep-alive")
            .defaultHeader(HttpHeaders.CONTENT_LENGTH, "0")
            .defaultHeader(HttpHeaders.COOKIE, cookie)
            .defaultHeader(HttpHeaders.ORIGIN, config.getUrl())
            .defaultHeader(HttpHeaders.REFERER, config.getUrl() + "/spydus")
            .build();

    String response = webClient.post().uri(baseUrl).retrieve().bodyToMono(String.class).block();

    log.info("Spydus v11 Init Response Body {}", response);
  }

  private void login(LMSConfiguration config, SpydusAuthentication auth) {
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.set("Accept", "*/*");
    requestHeaders.set("Origin", config.getUrl());
    requestHeaders.set("Referer", config.getUrl() + "/spydus");
    requestHeaders.set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
    requestHeaders.set("User-Agent", "Apache-HttpClient/4.5.14 (Java/17.0.7)");
    requestHeaders.set("Accept-Encoding", "br,deflate,gzip,x-gzip");
    String encryptedPassword = encryptAndUrlEncode(config.getPassword(), config.getPublicKey());
    String body =
        "UserName="
            + config.getUsername()
            + "&Password="
            + encryptedPassword
            + "&NewPassword=&ConfPassword=&SpydusSSO=&SpydusSSOUTC=&isForced=false&AuthKey=&Location="
            + config.getLocation()
            + "&SubLocation="
            + config.getSublocation();

    RestTemplate restTemplate = new RestTemplate();

    restTemplate.setInterceptors(
        Arrays.asList(
            new ClientHttpRequestInterceptor() {
              @Override
              public ClientHttpResponse intercept(
                  HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
                  throws IOException {
                HttpHeaders headers = request.getHeaders();
                headers.remove(HttpHeaders.ACCEPT_CHARSET);

                return execution.execute(request, body);
              }
            }));

    ResponseEntity<String> response =
        restTemplate.exchange(
            config.getUrl() + "/spydus/Account/Login",
            HttpMethod.POST,
            new HttpEntity<>(body, requestHeaders),
            String.class);

    HttpHeaders responseHeaders = response.getHeaders();
    List<String> cookies = responseHeaders.get("Set-Cookie");

    if (cookies != null) {
      auth.setSPYDUS_SESSIONID_443_COOKIE(getCookie("SPYDUS_SESSIONID_443", cookies));
      auth.setASPLOGININFO_443_COOKIE(getCookie("ASPLOGININFO_443", cookies));
    }
  }

  private void preinit(LMSConfiguration config, SpydusAuthentication auth) {
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.add("Cookie", "ASPLOGININFO_443=" + auth.getASPLOGININFO_443_COOKIE());

    RestTemplate restTemplate = new RestTemplate();
    ResponseEntity<String> response =
        restTemplate.exchange(
            config.getUrl() + "/spydus/Home/preinit",
            HttpMethod.POST,
            new HttpEntity<>(requestHeaders),
            String.class);

    HttpHeaders responseHeaders = response.getHeaders();
    List<String> cookies = responseHeaders.get("Set-Cookie");

    auth.setCSRFToken(response.getBody());

    if (cookies != null) {
      auth.setASPXAUTH_COOKIE(getCookie(".ASPXAUTH", cookies));
    }
  }

  private void getBasicToken(LMSConfiguration config, SpydusAuthentication auth) {
    String cookie =
        WebClient.builder()
            .baseUrl(config.getUrl() + "/spydus/api/CommonAPI/GetBasicTokenForLocal")
            .defaultHeader("Cookie", "ASPLOGININFO_443=" + auth.getASPLOGININFO_443_COOKIE())
            .build()
            .get()
            .retrieve()
            .bodyToMono(String.class)
            .block();
    auth.setBASIC_TOKEN(cookie.replace("\"", ""));
  }

  private void getBearerToken(LMSConfiguration config, SpydusAuthentication auth) {
    try {
      String responseBody = WebClient.builder()
          .baseUrl(config.getUrl())
          .defaultHeader("Authorization", "Basic " + auth.getBASIC_TOKEN())
          .defaultHeader("Accept", "*/*")
          .defaultHeader("Cache-Control", "no-cache")
          .build()
          .post()
          .uri("/api/all/1.0/authorization")
          .retrieve()
          .bodyToMono(String.class)
          .block();

      ObjectMapper objectMapper = new ObjectMapper();
      JsonNode jsonNode = objectMapper.readTree(responseBody);
      auth.setBEARER_TOKEN(jsonNode.get("access_token").asText());

    } catch (Exception e) {
      String message = "Cannot get Bearer token (WebClient)";
      log.error(message, e);
      throw new BusinessException(message);
    }
  }

  private String getCookie(String cookieName, List<String> cookies) {
    return cookies.stream()
        .filter(c -> c.startsWith(cookieName))
        .map(c -> c.substring(c.indexOf('=') + 1, c.indexOf(';')))
        .findFirst()
        .orElse("");
  }
}
