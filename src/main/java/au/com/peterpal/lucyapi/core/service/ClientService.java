package au.com.peterpal.lucyapi.core.service;

import static org.springframework.util.Assert.hasText;
import static org.springframework.util.Assert.notNull;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.codes.OrganisationIdentifierTypeCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.Client;
import lucy.common.NotFoundException;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class ClientService {

  private final RemoteCataloguing cataloguing;

  public ClientService(RemoteCataloguing cataloguing) {
    this.cataloguing = cataloguing;
  }

  /**
   * Get a client by customer code.
   *
   * @param customerCode
   * @return
   * @throws NotFoundException
   */
  public Client getClient(String customerCode) {
    hasText(customerCode, "Customer code must not be null or empty");

    OrganisationIdentifier organisationIdentifier = new OrganisationIdentifier();
    organisationIdentifier.setOrganisationIdType(OrganisationIdentifierTypeCode.PPLS);
    organisationIdentifier.setOrganisationIdValue(customerCode);

    try {
      return cataloguing.getClient(organisationIdentifier);
    } catch (NotFoundException e) {
      String msg = String.format("Client for customerCode %s Not Found", customerCode);
      log.error(msg, e);
      throw new ResourceNotFoundException(msg);
    }
  }

  /**
   * Add a client and return the newly added entity.
   *
   * @param client
   * @return
   */
  public Client addClient(Client client) {
    notNull(client, "Client must not be null");

    cataloguing.addClient(client);

    return getClient(client.getCustomerId().getOrganisationIdValue());
  }
}
