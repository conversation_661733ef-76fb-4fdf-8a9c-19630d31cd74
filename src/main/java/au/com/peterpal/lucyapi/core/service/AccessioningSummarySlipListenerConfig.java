package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.model.SummarySlipMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.jms.dsl.Jms;

import javax.jms.ConnectionFactory;
import javax.jms.Session;


@Log4j2
@Configuration
@EnableIntegration
public class AccessioningSummarySlipListenerConfig {

    private final ConnectionFactory connectionFactory;
    private final ReceiptPrinterService receiptPrinterService;

    private String accessioningSummarySlipQueueName;

    public AccessioningSummarySlipListenerConfig(ConnectionFactory connectionFactory,
                                                 ReceiptPrinterService receiptPrinterService,
                                                 @Value("${accessioning.summary.slip.queue.name:work-orders:lucy-api.accessioning-summary-slip}")
                                                 String accessioningSummarySlipQueueName) {
        this.connectionFactory = connectionFactory;
        this.receiptPrinterService = receiptPrinterService;
        this.accessioningSummarySlipQueueName = accessioningSummarySlipQueueName;
    }

    @Bean
    public IntegrationFlow accessioningSummarySlipJmsListener() {
        return IntegrationFlows.from(
                Jms.messageDrivenChannelAdapter(this.connectionFactory)
                    .destination(accessioningSummarySlipQueueName)
                    .configureListenerContainer(
                        spec ->
                            spec.sessionTransacted(true)
                                .sessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE)))
            .enrichHeaders(
                headerEnricherSpec ->
                    headerEnricherSpec.headerFunction(
                        IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID,
                        message ->
                            IncomingMessageProcessor.generateIncomingMessageId(message.getPayload()),
                        true))
            .wireTap(IncomingMessageLoggingControlFlow.LOG_INCOMING_MESSAGE_CHANNEL)
            .log(m -> "Received via JMS: " + m)
            .handle(SummarySlipMessage.class, (p, m) -> receiptPrinterService.printAccessioningSummarySlip(p))
            .log(message -> "handled: " + message)
            .get();
    }

}
