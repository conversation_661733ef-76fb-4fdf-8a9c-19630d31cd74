package au.com.peterpal.lucyapi.core.service.retrieve;

import static au.com.peterpal.lucyapi.utils.Helper.notBlank;
import static au.com.peterpal.lucyapi.utils.LambdaExceptionWrappers.throwingConsumerWrapper;
import static java.util.stream.Collectors.toList;

import au.com.peterpal.lucyapi.model.ActionStateMap;
import au.com.peterpal.lucyapi.model.CataloguingState;
import au.com.peterpal.lucyapi.utils.Partition;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.codes.OrganisationIdentifierTypeCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.RecordSource;
import lucy.common.NotFoundException;
import lucy.fulfillment.beans.RemoteFulfillment;
import lucy.fulfillment.codes.CataloguingAction;
import lucy.fulfillment.entity.OpenTitleOrder;
import lucy.fulfillment.entity.TransactionIdentifier;
import lucy.marc.MarcException;
import org.marc4j.marc.Record;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class RetrieveService {

  @Value("${title.order.retrieve.block.size:10}")
  private Integer blockSize;

  private ApplicationContext context;

  private RemoteCataloguing remoteCataloguing;
  private RemoteFulfillment remoteFulfillment;

  private Map<String, CustomerRecordsClient> clients = new HashMap<>();

  public RetrieveService(ApplicationContext context,
      RemoteCataloguing remoteCataloguing, RemoteFulfillment remoteFulfillment) {
    this.context = context;
    this.remoteCataloguing = remoteCataloguing;
    this.remoteFulfillment = remoteFulfillment;
  }

  @PostConstruct
  public void init() {
    Collection<CustomerRecordsClient> beans = context.getBeansOfType(CustomerRecordsClient.class).values();
    beans.forEach(client -> clients.put(client.getType(), client));
  }

  public void retrieve(String customerCode, List<String> titleOrderNumbers) throws NotFoundException {
    Optional
        .ofNullable(titleOrderNumbers)
        .filter(list -> !list.isEmpty())
        .orElseThrow(() -> new IllegalArgumentException("Title order list must not be null or empty."));

    RecordSource sourceInfo = getRecordSource(customerCode);
    List<OpenTitleOrder> orders = Optional
                                    .ofNullable(getTitleOrders(titleOrderNumbers))
                                    .orElseThrow(IllegalStateException::new);

    CustomerRecordsClient client = clients.get(sourceInfo.getType());
    if (client != null && client.doInit(sourceInfo)) {
      List<Record> records;
      try {
        for (int i = 0; i  < titleOrderNumbers.size(); i++) {
          log.debug("----------------------------------start retrieving------------------------------");
           records = client.retrieveMatchingRecords(orders.get(i));
          log.debug("----------------------------------end retrieving--------------------------------");
           if (!records.isEmpty()) {
             int size = records.size();
             log.info(() -> String.format("Importing %d", size));
             OrganisationIdentifier cId = new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS, customerCode);
            importRecords(cId, records);
            updateNextCataloguingAction(orders.get(i));
           }
        }
      } finally{
        log.info("Closing client.....");
        client.close();
      }
    } else {
      log.info(() -> String.format("No client bean found for type %s", sourceInfo.getType()));
    }
  }

  public void retrieve(String customerCode) {
    long start = System.currentTimeMillis();

    OrganisationIdentifier org =
        new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS, customerCode);

    List<OpenTitleOrder> orders = remoteFulfillment.getTitleOrders(org, null, null, null);
    log.info(String.format("Total number of orders before filter %d", orders.size()));

    List<String> tos =
        Optional.ofNullable(orders)
            .map(l -> l.stream()
                .filter(to -> isNotExported(to.getCataloguingAction()))
                .map(to -> to.getOrderId().getTransactionIdValue())
                .collect(toList())
            )
            .orElse(Collections.emptyList());
    log.info(String.format("Size of not exported tos is %d", tos.size()));

    try {
      Partition<String> tosPartition = Partition.ofSize(tos, blockSize);
      tosPartition.stream()
          .forEach(throwingConsumerWrapper(l -> {
                log.info(String.format("Retrieving a new block of %d...", l.size()));
                retrieve(customerCode, l);
              })
          );
    } catch(Exception ex) {
      log.warn(() -> "Exception retreieving title orders", ex);
    }
    long end = System.currentTimeMillis();
    log.info(String.format("Total time in millis: %d", end - start));
  }

  private RecordSource getRecordSource(String customerCode) throws NotFoundException {
    notBlank(customerCode, "Customer code must not be null or empty");

    return remoteCataloguing.getClientInternalRecordSource(new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS, customerCode));
  }

  private List<OpenTitleOrder> getTitleOrders(List<String> strList) throws NotFoundException {
    List<OpenTitleOrder> result = new ArrayList<>();
    for (int i = 0; i < strList.size(); i++) {
      String str = strList.get(i);
      try {
        result.add(remoteFulfillment.getTitleOrder(new TransactionIdentifier(str)));
      } catch(NotFoundException ex) {
        log.error(() -> String.format("Could not find title order with number %s", str));
        throw ex;
      }
    }
    return result;
  }

  private boolean importRecords(OrganisationIdentifier customerId, List<Record> records) {
    for (Record record : records) {
      try {
        this.remoteCataloguing.importCustomerBibRecord(record,customerId);
      } catch (MarcException e) {
        log.warn(e.getMessage(), e);
      } catch (NotFoundException e) {
        log.info(e.getMessage(), e);
      }
    }
    return true;
  }

  private boolean updateNextCataloguingAction(OpenTitleOrder titleOrder) {
    boolean result = true;
    try {
      titleOrder.setCataloguingAction(CataloguingAction.MATCH);
      titleOrder.setLastRetrievedClientRecords(new Date());

      remoteFulfillment.updateTitleOrder(titleOrder);
    } catch (Exception ex) {
      result = false;
    }
    return result;
  }

  private boolean isExported(CataloguingAction action) {
    return CataloguingState.EXPORTED.equals(ActionStateMap.fromAction(action));
  }

  private boolean isNotExported(CataloguingAction action) {
    return !isExported(action);
  }
}
