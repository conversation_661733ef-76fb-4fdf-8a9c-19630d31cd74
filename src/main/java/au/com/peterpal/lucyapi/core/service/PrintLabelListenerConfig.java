package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.model.PrintLabelMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.jms.dsl.Jms;

import javax.jms.ConnectionFactory;
import javax.jms.Session;


@Log4j2
@Configuration
@EnableIntegration
public class PrintLabelListenerConfig {

    private final ConnectionFactory connectionFactory;
    private final PrintLabelService printLabelService;
    private final String printLabelEvents;

    public PrintLabelListenerConfig(
        ConnectionFactory connectionFactory,
        PrintLabelService printLabelService,
        @Value("${events.print-label.channel:print-label-events}") String printLabelEvents) {
        this.connectionFactory = connectionFactory;
        this.printLabelService = printLabelService;
        this.printLabelEvents = printLabelEvents;
    }

    @Bean
    public IntegrationFlow printLabelEventsJmsListener() {
        return IntegrationFlows.from(
                Jms.messageDrivenChannelAdapter(this.connectionFactory)
                    .destination(printLabelEvents)
                    .configureListenerContainer(
                        spec ->
                            spec.sessionTransacted(true)
                                .sessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE)))
            .enrichHeaders(
                headerEnricherSpec ->
                    headerEnricherSpec.headerFunction(
                        IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID,
                        message ->
                            IncomingMessageProcessor.generateIncomingMessageId(message.getPayload()),
                        true))
            .wireTap(IncomingMessageLoggingControlFlow.LOG_INCOMING_MESSAGE_CHANNEL)
            .log(m -> "Received via JMS: " + m)
            .handle(PrintLabelMessage.class, (p, m) -> printLabelService.printLabels(p))
            .log(message -> "handled: " + message)
            .get();
    }

}
