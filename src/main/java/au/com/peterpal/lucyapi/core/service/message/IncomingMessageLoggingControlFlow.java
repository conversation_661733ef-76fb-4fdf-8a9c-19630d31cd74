package au.com.peterpal.lucyapi.core.service.message;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.IncomingMessageStatus;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.MessageChannels;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;

import java.util.concurrent.Executors;

@Configuration
@Log4j2
public class IncomingMessageLoggingControlFlow {

  private final IncomingMessageProcessor incomingMessageProcessor;

  public static final String INCOMING_MESSAGE_ID = "incoming_message_id";
  public static final String LOG_INCOMING_MESSAGE_CHANNEL = "incomingMessageChannel";
  public static final String SUCCESS_INCOMING_MESSAGE_CHANNEL = "success-invoice-message-channel";
  public static final String FAILED_INCOMING_MESSAGE_CHANNEL = "failed-invoice-message-channel";

  @Bean
  public MessageChannel incomingMessageChannel() {
    return MessageChannels.executor(Executors.newFixedThreadPool(5)).get();
  }

  public IncomingMessageLoggingControlFlow(IncomingMessageProcessor incomingMessageProcessor) {
    this.incomingMessageProcessor = incomingMessageProcessor;
  }

  @Bean
  public IntegrationFlow logIncomingMessageFlow() {
    return IntegrationFlows.from(incomingMessageChannel())
        .log(m -> String.format("save incoming message: %s", m))
        .handle(m -> incomingMessageProcessor.logIncomingMessage((Message<String>) m))
        .get();
  }

  @Bean
  public IntegrationFlow succeedIncomingMessageFlow() {
    return IntegrationFlows.from(SUCCESS_INCOMING_MESSAGE_CHANNEL)
        .log(m -> String.format("success incoming message : %s", m))
        .handle(
            m ->
                incomingMessageProcessor.updateIncomingMessageStatus(
                    (Message<String>) m, IncomingMessageStatus.SUCCEEDED))
        .get();
  }

  @Bean
  public IntegrationFlow failedIncomingMessageFlow() {
    return IntegrationFlows.from(FAILED_INCOMING_MESSAGE_CHANNEL)
        .log(m -> String.format("failed incoming message: %s", m))
        .handle(
            m ->
                incomingMessageProcessor.updateIncomingMessageStatus(
                    (Message<String>) m, IncomingMessageStatus.FAILED))
        .get();
  }
}
