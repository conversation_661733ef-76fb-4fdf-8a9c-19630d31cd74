package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.model.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import static au.com.peterpal.lucyapi.core.service.PrinterService.POST_REQUEST_FOOTER;
import static au.com.peterpal.lucyapi.core.service.PrinterService.POST_REQUEST_HEADER;

@Service
@RequiredArgsConstructor
@Log4j2
public class SupplyErrorAdvicePrinterService {

  @Value("${supply-error-advice-slip.template.location:workorderslip/supply-error-advice-slip.xml}")
  String templateName;

  @Value("${printer-suffix:.peterpal.local}")
  private String printerSuffix;

  private final RestTemplate restTemplate;

  public void handle(SupplyErrorAdvicePrintMessage message) {
    String printer = String.format("%s%s", message.getPrinter(), printerSuffix);
    try {
      String postRequest =
          String.format(POST_REQUEST_HEADER, message.getPrintJobId())
              + generateSupplyErrorAdviceDocument(message)
              + POST_REQUEST_FOOTER;

      log.debug(() -> String.format("Post request for receipt printer is: %s", postRequest));
      ResponseEntity<String> response =
          restTemplate.postForEntity(
              String.format("http://%s/cgi-bin/epos/service.cgi", printer),
              postRequest,
              String.class);

      log.debug(() -> String.format("Response form receipt printer is: %s", response));
    } catch (RestClientException e) {
      String errorMessage = String.format("Receipt Printer %s cannot be contacted", printer);
      log.error(errorMessage, e);
      throw new BusinessException(errorMessage);
    } catch (Exception e) {
      log.error(e);
      throw new BusinessException(e.getMessage());
    }
  }

  String generateSupplyErrorAdviceDocument(SupplyErrorAdvicePrintMessage message) {
    try {
      String template = readXMLTemplate(templateName);
      Map<String, String> valuesMap = new HashMap<>();
      valuesMap.put(
          "printedDate",
          Optional.ofNullable(message.getPrintedDate())
              .orElse(LocalDate.now())
              .format(DateTimeFormatter.ofPattern("d/M/yyyy")));
      valuesMap.put("title", StringEscapeUtils.escapeXml11(message.getTitle()));
      valuesMap.put("author", StringEscapeUtils.escapeXml11(message.getAuthor()));
      valuesMap.put("isbnReceived", message.getIsbnReceived());
      valuesMap.put("isbnOrdered", message.getIsbnOrdered());
      valuesMap.put("supplierName", StringEscapeUtils.escapeXml11(message.getSupplierName()));
      valuesMap.put("quantity", message.getQuantity().toString());
      valuesMap.put("errorType", StringEscapeUtils.escapeXml11(message.getErrorType()));
      valuesMap.put("invoiceNumber", StringEscapeUtils.escapeXml11(message.getInvoiceNumber()));
      valuesMap.put("purchaseOrderNumber", StringEscapeUtils.escapeXml11(message.getPurchaseOrderNumber()));
      return new StringSubstitutor(valuesMap).replace(template);

    } catch (Exception e) {
      throw new RuntimeException(
          "Error reading or processing the supplier error advice slip template", e);
    }
  }

  String readXMLTemplate(String template) throws IOException {
    ClassPathResource resource = new org.springframework.core.io.ClassPathResource(template);
    String content;
    try (BufferedReader reader =
        new BufferedReader(
            new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
      content = reader.lines().collect(Collectors.joining("\n"));
    }
    return content;
  }
}
