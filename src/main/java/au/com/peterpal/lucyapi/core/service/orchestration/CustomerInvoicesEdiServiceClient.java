package au.com.peterpal.lucyapi.core.service.orchestration;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.RetrieveLucyInvoicesResponse;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.TokenResponse;
import java.util.List;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

@Service
@Log4j2
public class CustomerInvoicesEdiServiceClient {

  @Value("${k8s-username}")
  private String k8sUsername;

  @Value("${k8s-password}")
  private String k8sPassword;

  @Value("${security.enabled:true}")
  private boolean securityEnabled;

  public List<RetrieveLucyInvoicesResponse> retrieveInvoiceByCustomer(
      String customerCode, String baseUrl, String path) {
    log.debug("Retrieving lucy invoices for customerCode: {}, baseUrl: {}", customerCode, baseUrl);
    WebClient.RequestBodySpec requestBodySpec =
        WebClient.builder()
            .baseUrl(baseUrl)
            .build()
            .post()
            .uri(String.format("%s/retrieve-invoices-by-customer-code", path))
            .header("Content-Type", "application/json");
    if (securityEnabled) {
      requestBodySpec =
          requestBodySpec.header("Authorization", "Bearer " + getAccessToken(baseUrl));
    }
    return requestBodySpec
        .body(BodyInserters.fromObject(customerCode))
        .retrieve()
        .bodyToFlux(RetrieveLucyInvoicesResponse.class)
        .collectList()
        .block();
  }

  private String getAccessToken(String baseUrl) {
    String authUri =
        String.format("/api/security/token?username=%s&password=%s", k8sUsername, k8sPassword);
    TokenResponse tokenResponse =
        WebClient.builder()
            .baseUrl(baseUrl)
            .build()
            .get()
            .uri(authUri)
            .retrieve()
            .bodyToMono(TokenResponse.class)
            .block();
    return Optional.ofNullable(tokenResponse)
        .orElseThrow(() -> new BusinessException("Cannot get access token"))
        .getAccessToken();
  }
}
