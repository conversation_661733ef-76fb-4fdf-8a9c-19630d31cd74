package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.model.email.*;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.net.util.Base64;
import org.marc4j.MarcReader;
import org.marc4j.MarcStreamReader;
import org.marc4j.marc.DataField;
import org.marc4j.marc.Record;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@RequiredArgsConstructor
public class EmailMarcRecordsService {

  @Value("${marc-records.email.sender-name:<PERSON>}")
  private String senderName;

  @Value("${marc-records.email.sender-email:<EMAIL>}")
  private String senderEmail;

  private static final String EMAIL_HTML = "email/marc-records-email.html";
  private static final String ATTACHMENT_TYPE = "text/plain";

  private final MessageChannel marcRecordsEmailChannel;

  public void sendEmail(String customerCode, String email, Map<Integer, File> marcFiles) {
    marcFiles.forEach(
        (batchId, file) -> {
          try {
            String htmlTemplate = loadHtmlTemplate();
            Attachment attachment = createAttachment(file);
            EmailMessage emailMessage =
                createEmailMessage(customerCode, batchId, file, htmlTemplate, email, attachment);
            marcRecordsEmailChannel.send(MessageBuilder.withPayload(emailMessage).build());
          } catch (BusinessException e) {
            log.error("Failed to send email for MARC file: {}", file.getName(), e);
          }
        });
  }

  private String loadHtmlTemplate() {
    try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(EMAIL_HTML)) {
      if (inputStream == null) {
        throw new FileNotFoundException("Template not found: " + EMAIL_HTML);
      }
      return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
    } catch (IOException e) {
      String errorMsg = "Failed to load email template: " + EMAIL_HTML;
      log.error(errorMsg, e);
      throw new BusinessException(errorMsg, e);
    }
  }

  private Attachment createAttachment(File file) {
    try {
      byte[] encodedBase64 = Base64.encodeBase64(FileUtils.readFileToByteArray(file));
      return Attachment.builder()
          .data(new String(encodedBase64, StandardCharsets.UTF_8))
          .type(ATTACHMENT_TYPE)
          .name(file.getName())
          .build();
    } catch (IOException e) {
      String errorMsg = String.format("Cannot read file %s", file.getName());
      log.error(errorMsg);
      throw new BusinessException(errorMsg, e);
    }
  }

  private EmailMessage createEmailMessage(
      String customerCode,
      int batchId,
      File file,
      String htmlTemplate,
      String email,
      Attachment attachment) {
    Map<String, Object> substitutionData = new HashMap<>();
    substitutionData.put("customerCode", customerCode);
    List<String> titleAndAuthorLines = getTitleAndAuthorLines(file);
    String subject =
        String.format(
            "MARC records from Peter Pal (Batch #%d - %d records)",
            batchId, titleAndAuthorLines.size());
    String completeHtml =
        htmlTemplate.replace("{{MARC_CONTENT}}", formatMarcContent(titleAndAuthorLines));

    return EmailMessage.builder()
        .from(EmailAddress.of(senderEmail, senderName))
        .to(
            Arrays.stream(email.split(","))
                .map(e -> EmailAddress.of(e, e))
                .collect(Collectors.toList()))
        .subject(subject)
        .content(
            Content.builder()
                .type(ContentType.INLINE_TEMPLATE)
                .htmlTemplate(completeHtml)
                .attachments(Collections.singletonList(attachment))
                .build())
        .cc(Collections.emptyList())
        .substitutionData(substitutionData)
        .bcc(Collections.emptyList())
        .build();
  }

  private List<String> getTitleAndAuthorLines(File file) {
    List<String> titleAuthorLines = new ArrayList<>();
    try (InputStream inputStream = Files.newInputStream(file.toPath())) {
      MarcReader reader = new MarcStreamReader(inputStream);
      while (reader.hasNext()) {
        Record record = reader.next();
        Optional.of(getTitleAndAuthorLine(record)).ifPresent(titleAuthorLines::add);
      }
    } catch (IOException e) {
      log.error("Error reading MARC file: {}", file.getName(), e);
      throw new BusinessException("Failed to process MARC file: " + file.getName(), e);
    }
    return titleAuthorLines;
  }

  private String formatMarcContent(List<String> authorTitleList) {
    StringBuilder content =
        new StringBuilder("<p>The following records are included in the attached file:</p><br>");
    authorTitleList.forEach(title -> content.append("<p>").append(title).append("</p>"));
    return content.toString();
  }

  private String getTitleAndAuthorLine(Record record) {
    DataField dataField = (DataField) record.getVariableField("245");
    return (dataField != null)
        ? dataField.toString().substring(6).replaceAll("\\$[a-z0-9]", "").trim()
        : "";
  }
}
