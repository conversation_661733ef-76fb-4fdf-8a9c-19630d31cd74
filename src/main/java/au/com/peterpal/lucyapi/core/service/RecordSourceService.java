package au.com.peterpal.lucyapi.core.service;

import static lucy.catalogue.codes.OrganisationIdentifierTypeCode.PPLS;

import java.util.List;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.RecordSource;
import lucy.common.NotFoundException;
import org.springframework.stereotype.Service;

@Service
public class RecordSourceService {

  private final RemoteCataloguing cataloguing;

  public RecordSourceService(RemoteCataloguing cataloguing) {
    this.cataloguing = cataloguing;
  }

  public List<RecordSource> getRecordSources() {
    return cataloguing.getRecordSources();
  }

  public RecordSource getRecordSource(int recordSourceId) throws NotFoundException {
    return cataloguing.getRecordSource(recordSourceId);
  }

  public RecordSource getClientInternalRecordSources(String customerCode) throws NotFoundException {
    OrganisationIdentifier organisationIdentifier = new OrganisationIdentifier(PPLS, customerCode);
    return cataloguing.getClientInternalRecordSource(organisationIdentifier);
  }

  public List<RecordSource> getClientExternalRecordSources(String customerCode) {
    OrganisationIdentifier organisationIdentifier = new OrganisationIdentifier(PPLS, customerCode);
    return cataloguing.getClientExternalRecordSources(organisationIdentifier);
  }
}
