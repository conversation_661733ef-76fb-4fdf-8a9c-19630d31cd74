package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.model.PrintLabelMessage;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.CataloguingException;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.BibTemplate;
import lucy.cataloguing.entity.Copy;
import lucy.cataloguing.entity.Label;
import lucy.common.NotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Log4j2
public class PrintLabelService {

    private final BibTemplateService bibTemplateService;
    private final RemoteCataloguing remoteCataloguing;

    public PrintLabelService(BibTemplateService bibTemplateService,
                             RemoteCataloguing remoteCataloguing) {
        this.bibTemplateService = bibTemplateService;
        this.remoteCataloguing = remoteCataloguing;
    }

  public PrintLabelMessage printLabels(PrintLabelMessage message) {
    try {
      BibTemplate bibTemplate =
          bibTemplateService.getBibTemplate(message.getAllocationsTemplateId());
      Map<String, Label> labels = getLabelDefinitions(bibTemplate);
      for (Map.Entry<Integer, List<String>> copyAndLabelNamesEntry :
          message.getPairOfCopyIdAndLabelNames().entrySet()) {
        printLabelForEachCopy(bibTemplate, labels, copyAndLabelNamesEntry);
      }
    } catch (Exception e) {
      String msg = String.format("Not able to print label with message %s", message);
      log.error(msg);
    }
    return message;
  }

    private void printLabelForEachCopy(BibTemplate bibTemplate,
                                       Map<String, Label> labels,
                                       Map.Entry<Integer, List<String>> copyAndLabelNamesEntry)
        throws NotFoundException, CataloguingException {
        Integer copyId = copyAndLabelNamesEntry.getKey();
        Copy copy = remoteCataloguing.getCopy(copyId);
        List<Label> printLabels = labels.values()
            .stream()
            .filter(l -> copyAndLabelNamesEntry.getValue().contains(l.getName()))
            .collect(Collectors.toList());
        log.debug("Found printLabelNames: {} for copyId: {}, bibTemplatePk {}", printLabels.stream().map(Label::getName).collect(Collectors.joining(",")), copyId, bibTemplate.getPk());
        for (Label printLabel : printLabels) {
            remoteCataloguing.printLabel(printLabel, bibTemplate, copy);
        }
    }

    public Map<String, Label> getLabelDefinitions(BibTemplate bibTemplate) throws CataloguingException {
        return remoteCataloguing.getLabelDefinitions(bibTemplate);
    }
}
