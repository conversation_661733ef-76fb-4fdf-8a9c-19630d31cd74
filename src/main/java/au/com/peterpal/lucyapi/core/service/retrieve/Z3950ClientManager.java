package au.com.peterpal.lucyapi.core.service.retrieve;

import static au.com.peterpal.lucyapi.utils.Helper.notNull;
import static org.springframework.util.Assert.hasText;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.codes.ProductIdentifierTypeCode;
import lucy.catalogue.entity.ProductIdentifier;
import lucy.catalogue.util.ProductIdentifierUtils;
import lucy.cataloguing.entity.RecordSource;
import lucy.fulfillment.entity.OpenTitleOrder;
import org.marc4j.marc.Record;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Log4j2
public class Z3950ClientManager extends CustomerRecordsClient {

  private static final String EMPTY = "empty";

  private static final String TITLE_ORDER_NOT_NULL_MSG = "Title order must not be null";

  @Value("${customer.client.z3950.type:Z39.50}")
  private String typeName;

  @Value("${ustomer.client.max.records:1000}")
  private int maxRecords = 1000;

  private JzkitZ3950Client client;

  public Z3950ClientManager(JzkitZ3950Client client) {
    this.client = client;
  }

  public void init(RecordSource sourceInfo) throws Z3950Exception {
    client.init(sourceInfo);
  }

  @Override
  public void close() {
    client.close();
  }

  @Override
  public String getType() {
    return typeName;
  }

  @Override
  protected List<Record> titleAndAuthorSearch(OpenTitleOrder titleOrder) {
    notNull(titleOrder, TITLE_ORDER_NOT_NULL_MSG);

    String title = Z3950Utils.cleanTitleValue(titleOrder.getTitleText());
    String author = Z3950Utils.cleanAuthorValue(titleOrder.getAuthor());
    String publisher = Z3950Utils.cleanAuthorValue(titleOrder.getPublisherName());

    String str = "Title and author search for title order %s, title %s, author %s, publisher %s";
    log.info(() -> String.format(str, titleOrder.getOrderId().getTransactionIdValue(),
        getValue(title), getValue(author), getValue(publisher)));

    String query;
    if (StringUtils.hasText(author)) {
      query = createTitleAuthorQuery(title, author);
    } else {
      query = createTitlePublisherQuery(title, publisher);
    }

    List<Record> searchResults;
    try {
      log.debug(String.format("----- Title Author searching for title order %s ---------", titleOrder.getOrderId().getTransactionIdValue()));
      searchResults = search(query);
    } catch (Z3950Exception ex) {
      log.warn(() -> String.format("Exception while searching for title and author. %s", ex.getMessage()), ex);
      searchResults = new ArrayList<>();
    }
    return searchResults;
  }

  private String getValue(String str) {
    if (str == null) {
      return "null";
    }
    return str.isEmpty() ? EMPTY : str;
  }

  @Override
  protected List<Record> productIdSearch(OpenTitleOrder titleOrder) {
    notNull(titleOrder, TITLE_ORDER_NOT_NULL_MSG);

    if (!isValidProductId(titleOrder)) {
      log.warn("Product id for title order %s is not valid", titleOrder.getOrderId().getTransactionIdValue());
      return Collections.EMPTY_LIST;
    }

    String str = "Product id search for title order %s and product id of %s";
    String prodIdValue = titleOrder.getProductId().getProductIdentifierValue();
    log.info(() -> String.format(str, titleOrder.getOrderId().getTransactionIdValue(), prodIdValue));

    Set<ProductIdentifier> productIds = ProductIdentifierUtils.getAllUniqueProductIds(Arrays
          .asList(titleOrder.getProductId()));

    List<Record> searchResults = new ArrayList<>();
    for (ProductIdentifier searchProductId : productIds) {
      String query = createProductIdQuery(searchProductId);
      try {
        log.debug(String.format("----- Product searching for title order %s ---------", titleOrder.getOrderId().getTransactionIdValue()));
        searchResults.addAll(search(query));
      } catch (Z3950Exception ex) {
        log.warn(() -> String.format("Exception while searching for product id. %s", searchProductId), ex);
      }
    }

    return searchResults;
  }

  private boolean isValidProductId(OpenTitleOrder titleOrder) {
    return titleOrder != null &&
        titleOrder.getProductId() != null &&
        titleOrder.getProductId().getProductIdentifierValue() != null &&
        !titleOrder.getProductId().getProductIdentifierValue().isEmpty();
  }

  @Override
  protected List<Record> localNumberSearch(OpenTitleOrder titleOrder) {
    notNull(titleOrder, TITLE_ORDER_NOT_NULL_MSG);
    notNull(titleOrder.getCataloguingReference(), "Cataloguing reference must not be null");

    String str = "Local number search for title order %s and cataloguing reference %s";
    String catRef = titleOrder.getCataloguingReference();
    if (catRef != null) {
      catRef = catRef.trim();
    }
    String catRefStr = catRef;
    log.info(() -> String.format(str, titleOrder.getOrderId().getTransactionIdValue(), getValue(catRefStr)));
    String query = createLocalNumberQuery(catRef);
    List<Record> searchResults;
    try {
      searchResults = search(query);
    } catch (Z3950Exception ex) {
      log.warn(() -> String.format("Exception while searching local number. %s", titleOrder.getCataloguingReference()), ex);
      searchResults = new ArrayList<>();
    }
    return searchResults;
  }

  private List<Record> search(String query) throws Z3950Exception {
    log.debug(String.format("Search: %s", getValue(query)));

    List<Record> searchResults = new ArrayList<>();

    client.query(query);

    if (client.getStatus() == Z3950Client.COMPLETE) {
      log.debug(String.format("Search returned %d results", client.getResultCount()));

      int i = 0;
      while(client.hasNext() && i < maxRecords) {
        searchResults.add(client.next());
      }
    } else {
      throw new Z3950Exception(String.format("Search failed: %s", query));
    }

    return searchResults;
  }

  private String createProductIdQuery(ProductIdentifier productId) {
    List<String> terms = new ArrayList<>();

    if (productId.getProductIdentifierType().equals(ProductIdentifierTypeCode.EAN)
        || productId.getProductIdentifierType().equals(ProductIdentifierTypeCode.ISBN13)
        || productId.getProductIdentifierType().equals(ProductIdentifierTypeCode.ISBN10)) {
      terms.add(Z3950Utils.createTerm(Z3950Constants.ISBN_QUERY, Z3950Utils.quoteValue(productId.getProductIdentifierValue())));
    } else if (productId.getProductIdentifierType().equals(ProductIdentifierTypeCode.PROP)) {
      terms.add(Z3950Utils.createTerm(Z3950Constants.KEYWORD_QUERY, productId.getProductIdentifierValue()));
    }

    return Z3950Utils.createQuery(terms);
  }

  private String createLocalNumberQuery(String localNumber) {
    List<String> terms = new ArrayList<>();

    if (StringUtils.hasText(localNumber)) {
      terms.add(Z3950Utils.createTerm(Z3950Constants.LOCAL_NUMBER_QUERY, Z3950Utils.quoteValue(localNumber)));
    }

    return Z3950Utils.createQuery(terms);
  }

  private String createTitleAuthorQuery(String title, String author) {
    List<String> terms = new ArrayList<>();

    terms.add(Z3950Utils.createTerm(Z3950Constants.TITLE_QUERY, Z3950Utils.quoteValue(title)));
    if (StringUtils.hasText(author)) {
      terms.add(Z3950Utils.createTerm(Z3950Constants.AUTHOR_QUERY, Z3950Utils.quoteValue(author)));
      terms.add(Z3950Constants.AND_OPERATOR);
    }

    return Z3950Utils.createQuery(terms);
  }

  private String createTitlePublisherQuery(String title, String publisher) {
    List<String> terms = new ArrayList<>();

    terms.add(Z3950Utils.createTerm(Z3950Constants.TITLE_QUERY, Z3950Utils.quoteValue(title)));
    terms.add(Z3950Utils.createTerm(Z3950Constants.PUBLISHER_QUERY, Z3950Utils.quoteValue(publisher)));
    terms.add(Z3950Constants.AND_OPERATOR);

    return Z3950Utils.createQuery(terms);
  }
}
