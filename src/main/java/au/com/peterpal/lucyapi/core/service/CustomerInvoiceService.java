package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.model.Invoice;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
@Log4j2
public class CustomerInvoiceService {

    @Value("${customer-invoices-service.url}")
    private String customerInvoicesServiceURL;

    @Value("${k8s-username}")
    private String k8sUsername;

    @Value("${k8s-password}")
    private String k8sPassword;
    public Invoice findInvoiceByInvoiceNumber(String invoiceNumber) {
        try {

            final String url =
                String.format(
                    "http://%s/api/query/invoices/search?keyword=%s",
                    customerInvoicesServiceURL, invoiceNumber);

            RestTemplate restTemplate = new RestTemplate();
            ObjectMapper objectMapper = new ObjectMapper();

            String response =
                restTemplate
                    .exchange(url, HttpMethod.GET, new HttpEntity<>(createHttpHeaders()), String.class)
                    .getBody();

            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode contentNode = jsonNode.get("content");

            if (contentNode != null && contentNode.isArray() && contentNode.size() > 0) {
                return objectMapper.treeToValue(contentNode.get(0), Invoice.class);
            } else {
                throw new ResourceNotFoundException(String.format("Invoice %s not found", invoiceNumber));
            }
        } catch (IOException ioException) {
            log.warn("Can't get invoice information", ioException);
            throw new BusinessException(String.format("Can't get invoice information for %s", invoiceNumber));
        }
    }

    private HttpHeaders createHttpHeaders() throws IOException {
        final String authURI =
            String.format(
                "http://%s/api/security/token?username=%s&password=%s",
                customerInvoicesServiceURL, k8sUsername, k8sPassword);
        RestTemplate restTemplate = new RestTemplate();
        String result = restTemplate.getForObject(authURI, String.class);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(result);

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(jsonNode.get("access_token").asText());
        headers.setContentType(MediaType.APPLICATION_JSON);

        return headers;
    }
}
