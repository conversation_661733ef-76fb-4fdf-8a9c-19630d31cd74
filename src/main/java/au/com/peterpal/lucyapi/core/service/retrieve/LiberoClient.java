package au.com.peterpal.lucyapi.core.service.retrieve;

import static org.springframework.util.Assert.notNull;

import au.com.libero.ArrayOfTerms;
import au.com.libero.SearchDetails;
import au.com.libero.SearchResultItem;
import au.com.libero.Terms;
import au.com.peterpal.libero.LiberoSearch;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.InvalidISBNException;
import lucy.catalogue.codes.ProductIdentifierTypeCode;
import lucy.catalogue.entity.ProductIdentifier;
import lucy.cataloguing.entity.RecordSource;
import lucy.fulfillment.entity.OpenTitleOrder;
import org.apache.commons.lang.StringUtils;
import org.marc4j.marc.Record;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@NoArgsConstructor
@Log4j2
public class LiberoClient extends CustomerRecordsClient {

  @Value("${customer.client.libero.maxRecords:1000}")
  private int maxRecords;

  @Value("${customer.client.libero.type:Libero}")
  private String typeName;

  private LiberoSearch liberoSearch;
  private RecordSource recordSource;

  public void init(RecordSource recordSource) {
    notNull(recordSource, "RecordSource must not be null");
    try {
      liberoSearch = new LiberoSearch(recordSource.getHost(), recordSource.getDatabase());
      this.recordSource = recordSource;
    } catch (Exception ex) {
      log.warn(() -> "Exception initializing Libero client", ex);
    }
  }

  public void close(){
    // Close the client
  }

  @Override
  public String getType() {
    return typeName;
  }

  @Override
  protected List<Record> titleAndAuthorSearch(OpenTitleOrder titleOrder) {
    List<SearchResultItem> searchResultItems = null;
    List<Terms> titleAuthorSearchTerms = new ArrayList<>();

    titleAuthorSearchTerms.add(buildTerms(
        StringUtils.left(Z3950Utils.cleanTitleValue(titleOrder.getTitle()), 50), "k", "AND"));

    titleAuthorSearchTerms.add(buildTerms(
        StringUtils.left(Z3950Utils.cleanTitleValue(titleOrder.getAuthor()), 50), "kb", null));

    try {
      ArrayOfTerms terms = ArrayOfTerms.Factory.newInstance();
      terms.setTermsArray(titleAuthorSearchTerms.toArray(new Terms[titleAuthorSearchTerms.size()]));
      SearchDetails sd = SearchDetails.Factory.newInstance();
      sd.setTermsList(terms);

      searchResultItems = liberoSearch.search(sd);
    } catch(RemoteException ex) {
      String title = titleOrder.getTitle();
      String author = titleOrder.getAuthor();
      String msg = ex.getMessage();
      log.warn(() -> String.format("Libero search for (title/author) %s / %s failed with message: %s", title, author, msg));
    }

    return retrieveRecords(searchResultItems);
  }

  @Override
  protected List<Record> productIdSearch(OpenTitleOrder titleOrder) {
    List<SearchResultItem> searchResultItems = null;
    ProductIdentifier productId = titleOrder.getProductId();

    if (productId != null) {
      log.info(
          () -> String.format("Searching for EAN/ISBN: %s", productId.getProductIdentifierValue()));

      List<Terms> terms = new ArrayList<>();

      if (ProductIdentifierTypeCode.EAN.equals(productId.getProductIdentifierType())
          || ProductIdentifierTypeCode.ISBN13.equals(productId.getProductIdentifierType())) {
        try {
          terms.add(buildTerms(productId.getProductIdentifierValue(), "i", "OR"));
          terms.add(buildTerms(productId.getISBN10().getProductIdentifierValue(), "i", null));
        } catch (InvalidISBNException e) {
          terms.add(buildTerms(productId.getProductIdentifierValue(), "ku", null));
        }
      } else if (ProductIdentifierTypeCode.ISBN10.equals(productId.getProductIdentifierType())) {
        try {
          terms.add(buildTerms(productId.getProductIdentifierValue(), "i", "OR"));
          terms.add(buildTerms(productId.getISBN13().getProductIdentifierValue(), "i", null));
        } catch (InvalidISBNException e) {
          log.warn(() -> "Product id search exception for type ISBN10", e);
        }
      } else if (productId.getProductIdentifierType().equals(ProductIdentifierTypeCode.PROP)) {
        terms.add(buildTerms(productId.getProductIdentifierValue(), "ku", null));
      }

      try {
        ArrayOfTerms arrTerms = ArrayOfTerms.Factory.newInstance();
        arrTerms.setTermsArray(terms.toArray(new Terms[terms.size()]));
        SearchDetails sd = SearchDetails.Factory.newInstance();
        sd.setTermsList(arrTerms);

        searchResultItems = liberoSearch.search(sd);
      } catch (RemoteException ex) {
        log.warn(() -> String.format("Product id search failed: %s", productId.getProductIdentifierValue()));
      }
    }
    return retrieveRecords(searchResultItems);
  }

  @Override
  protected List<Record> localNumberSearch(OpenTitleOrder titleOrder) {
    return new ArrayList<>();
  }

  private Terms buildTerms(String term, String type, String operator) {
    Terms t = Terms.Factory.newInstance();
    t.setTerm(term);
    t.setType(type);
    t.setOperator(operator);
    return t;
  }

  private List<Record> retrieveRecords(List<SearchResultItem> searchResultItems) {
    List<Record> records = new ArrayList<>();

    if (searchResultItems != null) {
      Iterator<SearchResultItem> resultsIterator = searchResultItems.iterator();
      int i = 0;
      while(i++ < maxRecords && resultsIterator.hasNext()) {
        SearchResultItem searchResultItem = resultsIterator.next();

        // Retrieve the MARC record
        records.add(getTitle(searchResultItem.getRsn()));
      }
    }
    return records;
  }

  private Record getTitle(String rsn) {
    Record record = null;
    try {
      record = liberoSearch.getTitle(rsn);
    } catch (RemoteException e) {
      String msg = String.format("Problem getting title for rsn %s", rsn);
      throw new LiberoException(msg, e);
    }
    return record;
  }
}
