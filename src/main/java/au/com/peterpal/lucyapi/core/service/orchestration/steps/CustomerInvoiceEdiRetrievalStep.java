package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.core.service.orchestration.CustomerInvoicesEdiServiceClient;
import au.com.peterpal.lucyapi.core.service.orchestration.WorkflowException;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.RetrieveLucyInvoicesResponse;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Log4j2
public class CustomerInvoiceEdiRetrievalStep extends SpydusEdiStepTemplate {
  @Value(
      "${jira.customer-invoice-edi-retrieval.webhook.url:"
          + "https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf1-8413-7c7d-8e6d-9c4d3de9f164}")
  private String jiraWebhookUrl;

  @Value(
      "${jira.customer-invoice-edi-retrieval.webhook.secret:"
          + "ea0ba31eb4edad0aaa427065689523b76d5a02c7}")
  private String jiraWebhookSecret;

  @Value("${customer-invoices-edi-service.url}")
  private String customerInvoicesEdiUrl;

  private final CustomerInvoicesEdiServiceClient customerInvoicesEdiServiceClient;

  @Override
  protected ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration) {
    PartiallyInvoicedWorkOrderMessage partiallyInvoicedWorkOrderMessage =
        getPartiallyInvoicedMessage(spydusEdiOrchestration);

    List<RetrieveLucyInvoicesResponse> lucy4InvoicesResponse =
        customerInvoicesEdiServiceClient.retrieveInvoiceByCustomer(
            spydusEdiOrchestration.getCustomerCode(),
            customerInvoicesEdiUrl,
            "/api/admin/invoices");

    log.info("Retrieved lucy invoices: {}", lucy4InvoicesResponse);
    List<String> validLucy4InvoicesFromEdi =
        lucy4InvoicesResponse.stream()
            .map(RetrieveLucyInvoicesResponse::getValidInvoices)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
    List<String> lucy4InvoicesFromCustomerInvoice =
        partiallyInvoicedWorkOrderMessage.getInvoices().stream()
            .map(PartiallyInvoicedWorkOrderMessage.Invoice::getLucy4InvoiceNumber)
            .collect(Collectors.toList());
    if (CollectionUtils.isEqualCollection(
        validLucy4InvoicesFromEdi, lucy4InvoicesFromCustomerInvoice)) {
      return ProcessingResult.of(true, null);
    }

    Collection<String> lucy4InvoicesMissing =
        CollectionUtils.subtract(lucy4InvoicesFromCustomerInvoice, validLucy4InvoicesFromEdi);
    Collection<String> lucy4InvoicesInAdditional =
        CollectionUtils.subtract(validLucy4InvoicesFromEdi, lucy4InvoicesFromCustomerInvoice);
    String errorMessage = String.format(
        "Lucy4 invoices in customer invoice service and customer invoice edi are not matching."
            + " The lucy4 invoices no missing: %s, and lucy4 invoices no in additional: %s",
        lucy4InvoicesMissing, lucy4InvoicesInAdditional);
    log.info(errorMessage);
    throw new WorkflowException(errorMessage);
  }

  @Override
  protected String getJiraWebhookUrl() {
    return jiraWebhookUrl;
  }

  @Override
  protected String getJiraWebhookSecret() {
    return jiraWebhookSecret;
  }
}
