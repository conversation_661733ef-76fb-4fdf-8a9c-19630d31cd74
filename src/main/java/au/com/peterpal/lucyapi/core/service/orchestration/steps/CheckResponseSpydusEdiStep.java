package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.core.service.orchestration.WorkflowException;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.SpydusCheckResponse;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Log4j2
public class CheckResponseSpydusEdiStep extends SpydusEdiStepTemplate {

  @Value(
      "${jira.response-spydus-edi.webhook.url:"
          + "https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf3-085e-7108-a840-b6af2e0b58b8}")
  private String jiraWebhookUrl;
  @Value(
      "${jira.response-spydus-edi.webhook.secret:"
          + "633c677e553f1b153b02b5785957b60218eeb5fb}")
  private String jiraWebhookSecret;

  @Value("${response-spydus-edi.url}")
  private String responseSpydusEdiUrl;

  @Value("${spydus-edi-orchestrator.skip-spydus-edi-step-in-test-env: false}")
  private boolean skipStepInTest;

  @Override
  protected ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration) {

    String customerCode = spydusEdiOrchestration.getCustomerCode();
    PartiallyInvoicedWorkOrderMessage partiallyInvoicedMessage =
        getPartiallyInvoicedMessage(spydusEdiOrchestration);
    if (skipStepInTest) {
      log.info("Skip this step in test environment");
      return ProcessingResult.of(true, null);
    }
    Set<String> lucy4Invoices =
        partiallyInvoicedMessage.getInvoices().stream()
            .map(PartiallyInvoicedWorkOrderMessage.Invoice::getLucy4InvoiceNumber)
            .collect(Collectors.toSet());
    return retrieveInvoicesStatus(customerCode, lucy4Invoices);
  }

  private ProcessingResult retrieveInvoicesStatus(String customerCode, Set<String> lucy4Invoices) {
    String uri = String.format("/SpydusEDI/responses?customer=%s", customerCode);
    if (lucy4Invoices != null && !lucy4Invoices.isEmpty()) {
      String invoicesQuery =
          lucy4Invoices.stream()
              .map(invoice -> "invoice=" + invoice)
              .collect(Collectors.joining("&"));
      uri += "&" + invoicesQuery;
    }
    SpydusCheckResponse invoiceResponse =
        WebClient.builder()
            .baseUrl(responseSpydusEdiUrl)
            .defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .build()
            .method(HttpMethod.GET)
            .uri(uri)
            .retrieve()
            .onStatus(
                HttpStatus::is4xxClientError,
                clientResponse -> {
                  if (clientResponse.statusCode() == HttpStatus.BAD_REQUEST) {
                    return clientResponse
                        .bodyToMono(String.class)
                        .flatMap(
                            errorBody -> {
                              if ("Files Not Found!".equals(errorBody)) {
                                return Mono.error(new WorkflowException("Files Not Found!"));
                              } else {
                                return Mono.error(
                                    new RuntimeException("Client error: " + errorBody));
                              }
                            });
                  } else {
                    return Mono.error(
                        new RuntimeException("Client error: " + clientResponse.statusCode()));
                  }
                })
            .bodyToMono(SpydusCheckResponse.class)
            .block();
    log.debug("Check response spydus response {}", invoiceResponse);
    if ((Objects.requireNonNull(invoiceResponse).getValid() == null
            || invoiceResponse.getValid().isEmpty())
        && (invoiceResponse.getInvalid() == null || invoiceResponse.getInvalid().isEmpty())) {
      throw new WorkflowException("Both valid and invalid fields of response are empty");
    }

    if (invoiceResponse.getInvalid() != null && !invoiceResponse.getInvalid().isEmpty()) {
      return ProcessingResult.of(
          false, "Invalid invoice response for " + invoiceResponse.getInvalid());
    }

    for (String invoice : Objects.requireNonNull(lucy4Invoices)) {
      if (!invoiceResponse.getValid().contains(invoice)) {
        throw new WorkflowException("valid fields of response are not enough");
      }
    }

    return ProcessingResult.of(true, null);
  }

  @Override
  protected String getJiraWebhookUrl() {
    return jiraWebhookUrl;
  }

  @Override
  protected String getJiraWebhookSecret() {
    return jiraWebhookSecret;
  }
}
