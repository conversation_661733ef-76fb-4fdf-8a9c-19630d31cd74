package au.com.peterpal.lucyapi.core.service.orchestration.dto;

import au.com.peterpal.common.rest.validation.BusinessException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@ToString
@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class PartiallyInvoicedWorkOrderMessage {
    @NonNull
    @NotEmpty
    private String customerCode;

    @NonNull
    @NotEmpty
    private UUID invoiceGroupId;

    @NotEmpty
    private List<Invoice> invoices;

    @Data
    @NoArgsConstructor(force = true)
    @AllArgsConstructor(staticName = "of")
    @Builder
    public static class Invoice {
        @NotNull
        private UUID invoiceId;
        @NotNull
        private String invoiceNumber;
        @NotNull
        private String lucy4InvoiceNumber;
        private List<WorkOrder> workOrders;
    }

    @Data
    @NoArgsConstructor(force = true)
    @AllArgsConstructor(staticName = "of")
    @Builder
    public static class WorkOrder {

        @NotNull
        private String workOrderNumber;
        @NotEmpty
        private Set<String> invoicedBarcode;
    }

    public static PartiallyInvoicedWorkOrderMessage from(String bodyMessage){
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(bodyMessage, PartiallyInvoicedWorkOrderMessage.class);
        } catch (IOException e) {
            throw new BusinessException("Cannot parse bodyMessage to PartiallyInvoicedWorkOrderMessage", e);
        }
    }
}
