package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.batch.control.BatchService;
import au.com.peterpal.lucyapi.core.service.BatchItemService;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.model.ModifiedFullBibRecordMessage;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@RequiredArgsConstructor
public class InitializationStep extends SpydusEdiStepTemplate {

  @Value(
      "${jira.initialization-orchestration.webhook.url:"
          + "https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf0-a872-7f1d-9353-fd06e8ee6221}")
  private String jiraWebhookUrl;

  @Value(
      "${jira.initialization-orchestration.webhook.secret:"
          + "7c71555f67fbacda43e8c90822432c117aa44c4e}")
 private String jiraWebhookSecret;

  private final BatchItemService batchItemService;
  private final MessageChannel modifiedFullBibRecordChannel;
  private final BatchService batchService;

  @Override
  protected ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration) {
    PartiallyInvoicedWorkOrderMessage partiallyInvoicedWorkOrderMessage =
        getPartiallyInvoicedMessage(spydusEdiOrchestration);

    List<PartiallyInvoicedWorkOrderMessage.WorkOrder> workOrders =
        partiallyInvoicedWorkOrderMessage.getInvoices().stream()
            .map(PartiallyInvoicedWorkOrderMessage.Invoice::getWorkOrders)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

    UUID invoiceGroupId = partiallyInvoicedWorkOrderMessage.getInvoiceGroupId();

    if (workOrders.isEmpty()) {
      log.info("Skip to add modified full bib record step due to all work orders are invoiced");
      Set<UUID> invoiceIds =
          partiallyInvoicedWorkOrderMessage.getInvoices().stream()
              .map(PartiallyInvoicedWorkOrderMessage.Invoice::getInvoiceId)
              .collect(Collectors.toSet());
      Set<Integer> batchIds = batchItemService.getAllBatchIds(invoiceIds);
      if (batchIds.isEmpty()) {
        log.warn(
            "Cannot get any batchIds in batchItems for invoiceGroup: {}, invoiceIds: {}",
            invoiceGroupId,
            invoiceIds);
      } else {
        batchService.close(batchIds);
      }
    } else {
      ModifiedFullBibRecordMessage message =
          ModifiedFullBibRecordMessage.builder()
              .invoiceGroupId(invoiceGroupId)
              .customerCode(partiallyInvoicedWorkOrderMessage.getCustomerCode())
              .invoices(
                  partiallyInvoicedWorkOrderMessage.getInvoices().stream()
                      .map(
                          invoice ->
                              ModifiedFullBibRecordMessage.Invoice.builder()
                                  .invoiceId(invoice.getInvoiceId())
                                  .workOrders(
                                      invoice.getWorkOrders().stream()
                                          .map(
                                              workOrder ->
                                                  ModifiedFullBibRecordMessage.WorkOrder.of(
                                                      workOrder.getWorkOrderNumber(),
                                                      workOrder.getInvoicedBarcode()))
                                          .collect(Collectors.toList()))
                                  .build())
                      .collect(Collectors.toList()))
              .build();

      modifiedFullBibRecordChannel.send(
          MessageBuilder.withPayload(message)
              .setHeader("message_type", "add-modified-full-bib-record")
              .build());
    }
    return ProcessingResult.of(true, null);
  }

  @Override
  protected String getJiraWebhookUrl() {
    return jiraWebhookUrl;
  }

  @Override
  protected String getJiraWebhookSecret() {
    return jiraWebhookSecret;
  }
}
