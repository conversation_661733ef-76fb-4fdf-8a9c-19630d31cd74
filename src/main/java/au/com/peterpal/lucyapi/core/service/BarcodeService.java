package au.com.peterpal.lucyapi.core.service;

import static org.springframework.util.Assert.hasText;
import static org.springframework.util.Assert.notNull;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.model.BarcodeInfo;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.codes.OrganisationIdentifierTypeCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.Acquisition;
import lucy.cataloguing.entity.Receipt;
import lucy.common.NotFoundException;
import lucy.fulfillment.beans.RemoteFulfillment;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Log4j2
public class BarcodeService {

  private RemoteCataloguing remoteCataloguing;
  private RemoteFulfillment remoteFulfillment;

  public BarcodeService(RemoteCataloguing remoteCataloguing, RemoteFulfillment remoteFulfillment) {
    this.remoteCataloguing = remoteCataloguing;
    this.remoteFulfillment = remoteFulfillment;
  }

  public List<BarcodeInfo> getBarcodeInfo(String workOrderNumber) throws NotFoundException {
    hasText(workOrderNumber, "Work order number must not be null or empty");

    Acquisition acquisition = remoteCataloguing.getAcquisition(
        getAcquisitionForReceipt(getReceipt(workOrderNumber)).getPk());

    return acquisition.getCopies().stream()
        .map(copy -> BarcodeInfo.builder()
            .barcode(copy.getBarCodeNumber())
            .branchCode(copy.getBranchCode())
            .branchName(branchName(copy.getBranchCode()))
            .build()
        )
        .collect(Collectors.toList());
  }

  private Receipt getReceipt(String workOrderNumber) throws NotFoundException {
    Receipt receipt = remoteCataloguing.findReceipt(workOrderNumber);
    if (receipt == null) {
      throw new NotFoundException(String.format("Could not find receipt for work order number %s", workOrderNumber));
    }
    return receipt;
  }

  private Acquisition getAcquisitionForReceipt(Receipt receipt) throws NotFoundException {
    notNull(receipt, "Receipt must not be null");

    if (receipt.getAcquisition() == null) {
      throw new NotFoundException(String.format("Acquisition not found for receipt %s", receipt));
    }
    return receipt.getAcquisition();
  }

  private String branchName(String branchCode) {
    if (!StringUtils.hasText(branchCode)) {
      log.warn(() -> "Null or empty branch code");
      return "";
    }

    try {
      return remoteFulfillment.getCustomer(new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS, branchCode)).getName();
    } catch (NotFoundException ex) {
      log.error(() -> ex);
      throw new ResourceNotFoundException(String.format("Could not find customer for branch code %s", branchCode));
    }
  }
}
