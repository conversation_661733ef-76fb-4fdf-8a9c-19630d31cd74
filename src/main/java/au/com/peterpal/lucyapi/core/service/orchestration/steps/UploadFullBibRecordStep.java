package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.core.service.BatchItemService;
import au.com.peterpal.lucyapi.core.service.LMSService;
import au.com.peterpal.lucyapi.core.service.orchestration.WorkflowException;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Log4j2
public class UploadFullBibRecordStep extends SpydusEdiStepTemplate {

  @Value(
      "${jira.upload-full-bib-record.webhook.url:"
          + "https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf4-1af9-7348-adea-450ea83b35d0}")
  private String jiraWebhookUrl;

  @Value(
      "${jira.upload-full-bib-record.webhook.secret:"
          + "61d59151bde2abd59795f83c600bd4b51e18d118}")
  private String jiraWebhookSecret;

  private final BatchItemService batchItemService;
  private final LMSService lmsService;

  @Override
  protected ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration) {

    PartiallyInvoicedWorkOrderMessage partiallyInvoicedWorkOrderMessage =
        getPartiallyInvoicedMessage(spydusEdiOrchestration);

    UUID invoiceGroupId = partiallyInvoicedWorkOrderMessage.getInvoiceGroupId();

    Set<UUID> invoiceIds =
        partiallyInvoicedWorkOrderMessage.getInvoices().stream()
            .map(PartiallyInvoicedWorkOrderMessage.Invoice::getInvoiceId)
            .collect(Collectors.toSet());
    Set<Integer> batchIds = batchItemService.getAllBatchIds(invoiceIds);
    if (batchIds.isEmpty()) {
      throw new WorkflowException(
          String.format(
              "Cannot get any batchIds in batchItems for invoiceGroup: %s, invoiceIds: %s",
              invoiceGroupId, invoiceIds));
    }
    lmsService.uploadBibRecordsToSpydus(
        partiallyInvoicedWorkOrderMessage.getCustomerCode(), batchIds);

    return ProcessingResult.of(true, null);
  }

  @Override
  protected String getJiraWebhookUrl() {
    return jiraWebhookUrl;
  }

  @Override
  protected String getJiraWebhookSecret() {
    return jiraWebhookSecret;
  }
}
