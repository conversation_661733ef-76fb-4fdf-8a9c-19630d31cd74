package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.core.service.orchestration.control.CheckBarcodeService;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@RequiredArgsConstructor
public class CheckWorkOrderBarcodeStep extends SpydusEdiStepTemplate {

  @Value(
      "${jira.check-work-order-and-barcode.webhook.url:"
          + "https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf0-a872-7f1d-9353-fd06e8ee6221}")
  private String jiraWebhookUrl;

  @Value(
      "${jira.check-work-order-and-barcode.webhook.secret:"
          + "7c71555f67fbacda43e8c90822432c117aa44c4e}")
  private String jiraWebhookSecret;

    private final CheckBarcodeService checkBarcodeService;

  @Override
  protected ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration) {
    return checkBarcodeService.checkBarcodes(spydusEdiOrchestration);
  }

  @Override
  protected String getJiraWebhookUrl() {
    return jiraWebhookUrl;
  }

  @Override
  protected String getJiraWebhookSecret() {
    return jiraWebhookSecret;
  }
}
