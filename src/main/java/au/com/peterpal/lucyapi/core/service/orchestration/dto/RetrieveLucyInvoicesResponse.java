package au.com.peterpal.lucyapi.core.service.orchestration.dto;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.*;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RetrieveLucyInvoicesResponse {

  @Builder.Default private String customerCode = "";

  @Builder.Default private List<String> validInvoices = Lists.newArrayList();

  @Builder.Default private Map<String, String> invalidInvoices = Maps.newHashMap();
}
