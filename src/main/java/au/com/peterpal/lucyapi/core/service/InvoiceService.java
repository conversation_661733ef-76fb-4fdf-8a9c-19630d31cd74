package au.com.peterpal.lucyapi.core.service;

import static org.springframework.util.Assert.notEmpty;
import static org.springframework.util.Assert.notNull;

import au.com.peterpal.lucyapi.model.InvoiceCompareInfo;
import au.com.peterpal.lucyapi.persistence.lucy.InvoiceCompareInfoRepository;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class InvoiceService {

  private final InvoiceCompareInfoRepository repo;

  public InvoiceService(InvoiceCompareInfoRepository repo) {
    this.repo = repo;
  }

  public List<InvoiceCompareInfo> getInvoiceCompareInfo(List<String> invoiceIdList) {
    notNull(invoiceIdList, "Invoice id list must not be null");
    notEmpty(invoiceIdList, "Invoice id list must not be empty");

    return repo.findInvoiceCompareInfo(invoiceIdList);
  }
}
