package au.com.peterpal.lucyapi.core.service;

import static org.springframework.util.Assert.hasText;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.model.*;
import au.com.peterpal.lucyapi.model.completetask.CompleteTaskSelectMultipleMessage;
import au.com.peterpal.lucyapi.model.completetask.CompleteTaskSelectOneMessage;
import au.com.peterpal.lucyapi.model.completetask.DoSubtask;
import au.com.peterpal.lucyapi.model.completetask.LogTaskActivitySelectMultipleRequest;
import au.com.peterpal.lucyapi.model.completetask.LogTaskActivitySelectOneRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.time.LocalDate;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.Receipt;
import lucy.common.NotFoundException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
@Log4j2
public class WorkOrderService {

  private static final String MESSAGE_TYPE_HEADER = "message_type";
  private static final String TASK_SELECT_ONE = "task-select-one";
  private static final String TASK_SELECT_MULTIPLE = "task-select-multiple";

  private final RemoteCataloguing remoteCataloguing;
  private final MessageChannel workOrderCompleteTaskChannel;
  private final MessageChannel removeBarcodeChannel;
  private final WorkOrderSlipService workOrderSlipService;

  @Value("${work-orders-service.url}")
  private String workOrderServiceURL;
  @Value("${k8s-username}")
  private String k8sUsername;
  @Value("${k8s-password}")
  private String k8sPassword;
  @Value("${printer-suffix:.peterpal.local}")
  private String printerSuffix;

  public List<String> getWorkOrderNumberForBarcode(String barcode, String customerId) {
    hasText(barcode, "Barcode must not be null or empty");
    hasText(customerId, "Customer id must not be null or empty");

    List<String> workOrderNumberList = new ArrayList<>();
    Optional.ofNullable(remoteCataloguing.findCopy(barcode, customerId)).ifPresent(c ->
        c.getAcquisition()
            .getReceipts()
            .stream().forEach(receipt -> workOrderNumberList.add(receipt.getOrderAssignmentId())));
    return workOrderNumberList;
  }

  public Receipt getReceipt(String workOrderNumber) throws NotFoundException {
    Receipt receipt = remoteCataloguing.findReceipt(workOrderNumber);
    if (receipt == null) {
      throw new NotFoundException(
          String.format("Could not find receipt for work order number %s", workOrderNumber));
    }
    return receipt;
  }

  private HttpHeaders createHttpHeaders() throws IOException {
    final String authURI =
        String.format(
            "http://%s/api/security/token?username=%s&password=%s",
            workOrderServiceURL, k8sUsername, k8sPassword);
    RestTemplate restTemplate = new RestTemplate();
    String result = restTemplate.getForObject(authURI, String.class);
    ObjectMapper objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(result);

    HttpHeaders headers = new HttpHeaders();
    headers.setBearerAuth(jsonNode.get("access_token").asText());
    headers.setContentType(MediaType.APPLICATION_JSON);

    return headers;
  }

  public List<Map.Entry<String, String>> getItemBarcodesAndDestinations(String workOrderNumber)
      throws IOException {
    // Set pageSize = 500 to make sure we get all the items in the response
    JsonNode jsonArray = searchItems(workOrderNumber);

    return StreamSupport.stream(
            Spliterators.spliteratorUnknownSize(jsonArray.iterator(), Spliterator.ORDERED), false)
        .filter(n -> !n.get("status").asText().equals("CANCELLED"))
        .map(n -> new AbstractMap.SimpleEntry<>(n.get("customerItemIdentifier").asText(),
            n.get("destinationLocationCode").asText()))
        .collect(Collectors.toList());
  }

  public Set<String> getActiveItemBarcodes(String workOrderNumber) throws IOException {
    // Set pageSize = 500 to make sure we get all the items in the response
    JsonNode jsonArray = searchItems(workOrderNumber);

    return StreamSupport.stream(
            Spliterators.spliteratorUnknownSize(jsonArray.iterator(), Spliterator.ORDERED), false)
        .filter(n -> !n.get("status").asText().equals("CANCELLED"))
        .map(n -> n.get("customerItemIdentifier").asText())
        .collect(Collectors.toSet());
  }

  private JsonNode searchItems(String workOrderNumber) throws IOException {
    final String url = String.format(
        "http://%s/api/query/work-orders/items/search?workOrderNumber=%s&size=500",
        workOrderServiceURL, workOrderNumber);
    RestTemplate restTemplate = new RestTemplate();
    ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET,
        new HttpEntity<>(createHttpHeaders()), String.class);

    ObjectMapper objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(response.getBody());
    return jsonNode.get("content");
  }

  private String getWorkOrderId(String workOrderNumber) throws IOException {
    final String url = String.format(
        "http://%s/api/query/work-orders/findByWorkOrderNumber?workOrderNumber=%s",
        workOrderServiceURL, workOrderNumber);
    RestTemplate restTemplate = new RestTemplate();
    ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET,
        new HttpEntity<>(createHttpHeaders()), String.class);

    ObjectMapper objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(response.getBody());

    return jsonNode.get("workOrderId").asText();
  }

  public List<TaskInfo> getTasks(String workOrderNumber) throws IOException {
    String workOrderId = getWorkOrderId(workOrderNumber);

    final String url = String.format("http://%s/api/query/work-orders/%s/tasks",
        workOrderServiceURL, workOrderId);
    RestTemplate restTemplate = new RestTemplate();
    ObjectMapper objectMapper = new ObjectMapper();

    String response = restTemplate.exchange(url, HttpMethod.GET,
        new HttpEntity<>(createHttpHeaders()), String.class).getBody();

    return objectMapper.readValue(response, new TypeReference<List<TaskInfo>>() {
    });
  }

  public List<WorkOrderBarcodeResponse> findItemsByInvoiceNumbers(Set<String> invoiceNumbers)
      throws IOException {
    String invoiceNumbersParam = String.join(",", invoiceNumbers);

    final String url =
        String.format(
            "http://%s/api/query/work-orders/items/find-by-invoice-numbers?invoiceNumbers=%s",
            workOrderServiceURL, invoiceNumbersParam);
    RestTemplate restTemplate = new RestTemplate();
    ObjectMapper objectMapper = new ObjectMapper();

    String response =
        restTemplate
            .exchange(url, HttpMethod.GET, new HttpEntity<>(createHttpHeaders()), String.class)
            .getBody();
    return objectMapper.readValue(response, new TypeReference<List<WorkOrderBarcodeResponse>>() {
    });
  }

  public void completeAccessioningTask(TaskInfo task,
      Map<String, Integer> optionalFees,
      String currentUser,
      String printer) {
    List<SubtaskInfo> subtasks = task.getSubtasks();

    // TODO: Check if there are subtasks for the optional fees
    // subtasks.stream().filter(s -> s.getFeeCode().equals("CR")).findFirst()
    //        .orElseThrow(() -> new BusinessException("Accessioning Task doesn't have a subtask with a CR fee!"));

    switch (task.getServiceType()) {
      case SELECT_ONE:
        completeAccessioningTaskSelectOne(task, currentUser, subtasks, printer);
        break;
      case SELECT_MULTIPLE:
        // TODO: add subtask completion for optional fees here
        completeAccessioningTaskSelectMultiple(task, currentUser, subtasks, printer);
        break;
      default:
        break;
    }
    log.info("Completed accessioning task for task number {}, currentUser {}", task.getTaskNumber(),
        currentUser);
  }

  private void completeAccessioningTaskSelectMultiple(TaskInfo task,
      String currentUser,
      List<SubtaskInfo> subtasks,
      String printer) {
    // complete each subtask that has default of true
    List<DoSubtask> doSubtasks = subtasks.stream()
        .filter(SubtaskInfo::getServiceComponentDefaultVal)
        .map(DoSubtask::from)
        .collect(Collectors.toList());

    CompleteTaskSelectMultipleMessage selectMultipleMessage = CompleteTaskSelectMultipleMessage.builder()
        .currentUser(currentUser)
        .logTaskActivitySelectMultiple(LogTaskActivitySelectMultipleRequest.builder()
            .workOrderId(task.getWorkOrderId())
            .taskId(task.getTaskId())
            .doSubtasks(doSubtasks)
            .printer(printer)
            .build())
        .build();
    workOrderCompleteTaskChannel.send(MessageBuilder
        .withPayload(selectMultipleMessage)
        .setHeader(MESSAGE_TYPE_HEADER, TASK_SELECT_MULTIPLE)
        .build());
    log.info("");
  }

  private void completeAccessioningTaskSelectOne(TaskInfo task,
      String currentUser,
      List<SubtaskInfo> subtasks,
      String printer) {
    SubtaskInfo completedSubtask = subtasks.stream()
        .filter(SubtaskInfo::getServiceComponentDefaultVal)
        .findFirst()
        .orElseThrow(() -> new BusinessException("Can not find any Subtask to be completed"));

    CompleteTaskSelectOneMessage selectOneMessage = CompleteTaskSelectOneMessage.builder()
        .currentUser(currentUser)
        .logTaskActivitySelectOne(LogTaskActivitySelectOneRequest.builder()
            .workOrderId(task.getWorkOrderId())
            .taskId(task.getTaskId())
            .completedSubtaskId(completedSubtask.getSubtaskId())
            .quantityCompleted(completedSubtask.getQuantity())
            .printer(printer)
            .build())
        .build();
    workOrderCompleteTaskChannel.send(MessageBuilder
        .withPayload(selectOneMessage)
        .setHeader(MESSAGE_TYPE_HEADER, TASK_SELECT_ONE)
        .build());
  }

  public void removeBarcodes(String workOrderNumber, List<String> barcodes) {
    log.info("Removing barcodes {} for workOrderNumber {}", barcodes, workOrderNumber);
    RemoveBarcodeMessage removeBarcodeMessage = RemoveBarcodeMessage.builder()
        .workOrderNumber(workOrderNumber)
        .barcodes(barcodes)
        .build();
    removeBarcodeChannel.send(MessageBuilder
        .withPayload(removeBarcodeMessage)
        .setHeader(MESSAGE_TYPE_HEADER, "remove-barcode")
        .build());
  }

  public void completeAccessioningTask(String workOrderNumber, String currentUser, String printer)
      throws IOException {

    // validate
    List<TaskInfo> accessioningTasks = extractAndValidateWorkOrderAccessioningTask(workOrderNumber);

    // Complete task
    for (TaskInfo accessioningTask : accessioningTasks) {
      completeAccessioningTask(accessioningTask, Collections.emptyMap(), currentUser, printer);
    }
  }

  public List<TaskInfo> extractAndValidateWorkOrderAccessioningTask(String workOrderNumber)
      throws IOException {

    List<TaskInfo> tasks = getTasks(workOrderNumber);

    List<TaskInfo> accessioningTasks =
        tasks.stream()
            .filter(task -> task.getDepartment() == Department.ACCESSIONING)
            .collect(Collectors.toList());

    // Check task is exist
    if (CollectionUtils.isEmpty(accessioningTasks)) {
      throw new BusinessException(
          String.format("No Accessioning task found for work order %s", workOrderNumber));
    }

    // Check task isn't done already
    if (accessioningTasks.stream().allMatch(task -> task.getStatus() != TaskStatus.TODO)) {
      throw new BusinessException("Accessioning already completed");
    }

    // Check cataloguing task(s) are complete
    List<TaskInfo> notCompleteCataloguingTasks =
        tasks.stream()
            .filter(
                task ->
                    task.getDepartment() == Department.CATALOGUING
                        && (task.getStatus() == TaskStatus.TODO
                        || task.getStatus() == TaskStatus.IN_PROGRESS))
            .collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(notCompleteCataloguingTasks)) {
      throw new BusinessException("Cataloguing task has not been done");
    }

    return accessioningTasks;
  }

  public WorkOrderSlipMessage printWorkOrderSlip(WorkOrderSlipMessage workOrderSlipMessage) {
    log.debug("WorkOrderSlipMessage {}", workOrderSlipMessage);
    String workOrderSlip = workOrderSlipService.printWorkOrderSlip(
        "workorderslip/workordersliptemplate.xml", workOrderSlipMessage, LocalDate.now());
    workOrderSlipService.print(workOrderSlipMessage.getPrinter() + printerSuffix,
        workOrderSlipMessage.getWorkOrderNumber(), workOrderSlip
    );

    return workOrderSlipMessage;
  }
}
