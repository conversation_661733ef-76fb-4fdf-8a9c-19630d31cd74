package au.com.peterpal.lucyapi.core.service.message;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.IncomingMessage;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.IncomingMessageStatus;
import au.com.peterpal.lucyapi.persistence.lucyapi.IncomingMessageRepository;
import brave.Tracer;
import lombok.extern.log4j.Log4j2;
import org.apache.activemq.artemis.jms.client.ActiveMQDestination;
import org.apache.activemq.artemis.jms.client.ActiveMQQueue;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.cloud.sleuth.instrument.messaging.TraceMessageHeaders;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.TimeZone;

import static au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID;

@Log4j2
@Component
public class IncomingMessageProcessor {

    static final String MESSAGE_TYPE_HEADER = "message_type";

    private final IncomingMessageRepository incomingMessageRepository;
    private final JmsTemplate jmsTemplate;
    private final Tracer tracer;

    public IncomingMessageProcessor(
        IncomingMessageRepository incomingMessageRepository, JmsTemplate jmsTemplate, Tracer tracer) {
        this.incomingMessageRepository = incomingMessageRepository;
        this.jmsTemplate = jmsTemplate;
        this.jmsTemplate.setPubSubDomain(false);
        this.tracer = tracer;
    }

    public void logIncomingMessage(Message<String> message) {
        String messageType = message.getHeaders().get(MESSAGE_TYPE_HEADER, String.class);
        Object contentType = message.getHeaders().get("contentType");
        Object jms_destination = message.getHeaders().get("jms_destination");

        String queueName = null;
        if (jms_destination != null) {
            if (jms_destination instanceof ActiveMQQueue) {
                ActiveMQQueue queue = (ActiveMQQueue) jms_destination;
                queueName = queue.getQueueName();

            } else if (jms_destination instanceof ActiveMQDestination) {
                ActiveMQDestination queue = (ActiveMQDestination) jms_destination;
                queueName = queue.getAddress();
            }
        }

        String source = message.getHeaders().get("message_source", String.class);
        String messageId = message.getHeaders().get(INCOMING_MESSAGE_ID, String.class);
        Long timestamp = message.getHeaders().get("jms_timestamp", Long.class);

        LocalDateTime receivedDateTime =
            LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), TimeZone.getDefault().toZoneId());

        log.debug(String.format("saving incoming message, %s, %s", messageId, source));

        String hash = buildHash(message.getPayload());

        IncomingMessage incomingMessage =
            StringUtils.isNotEmpty(messageId)
                ? incomingMessageRepository.findById(messageId).orElse(null)
                : null;

        if (incomingMessage == null) {
            incomingMessage =
                IncomingMessage.builder()
                    .id(messageId)
                    .hash(hash)
                    .messageType(
                        StringUtils.defaultString(
                            messageType, contentType != null ? contentType.toString() : ""))
                    .queueName(queueName)
                    .messageSource(source)
                    .body(message.getPayload())
                    .status(IncomingMessageStatus.SUCCEEDED)
                    .retryCount(0)
                    .receivedDateTime(receivedDateTime)
                    .lastModifiedDateTime(receivedDateTime)
                    .build();
        } else {
            int retry = incomingMessage.getRetryCount().intValue();
            incomingMessage.setRetryCount(retry + 1);
            incomingMessage.setLastModifiedDateTime(LocalDateTime.now());
        }

        incomingMessageRepository.save(incomingMessage);
    }

    public void updateIncomingMessageStatus(Message<String> message, IncomingMessageStatus status) {

        String messageId = message.getHeaders().get(INCOMING_MESSAGE_ID, String.class);

        IncomingMessage incomingMessage =
            incomingMessageRepository.findById(messageId).orElse(null);

        if (incomingMessage != null) {
            incomingMessage.setStatus(status);
            incomingMessageRepository.save(incomingMessage);

            log.debug(
                () -> String.format("incoming message with id: %s updated to: %s", messageId, status));
        } else {
            log.info(
                () ->
                    String.format(
                        "incoming message status update failed. not found record with id: %s",
                        messageId));
        }
    }

    public String resendMessageToJMSQueue(String incomingMessageId) {
        IncomingMessage incomingMessage =
            incomingMessageRepository
                .findById(incomingMessageId)
                .orElseThrow(
                    () -> new ResourceNotFoundException(IncomingMessage.class, incomingMessageId));

        IncomingMessage updatedMessage = incomingMessageRepository.save(incomingMessage);
        sendJMSQueue(
            incomingMessageId,
            updatedMessage.getBody(),
            updatedMessage.getMessageType(),
            updatedMessage.getQueueName());
        return incomingMessageId;
    }

    public void sendJMSQueue(
        String incomingMessageId, String messageString, String messageType, String queueName) {
        log.debug(
            () ->
                String.format(
                    "sending to queue: %s, message_type: %s body: %s",
                    queueName, messageType, messageString));
        jmsTemplate.convertAndSend(
            queueName,
            messageString,
            message -> {
                String traceId = tracer.currentSpan().context().traceIdString();
                String spanId = Long.toHexString(tracer.currentSpan().context().spanId());

                message.setStringProperty(MESSAGE_TYPE_HEADER, messageType);
                message.setStringProperty(INCOMING_MESSAGE_ID, incomingMessageId);
                message.setStringProperty(TraceMessageHeaders.TRACE_ID_NAME, traceId);
                message.setStringProperty(TraceMessageHeaders.SPAN_ID_NAME, spanId);
                return message;
            });
        log.debug(
            () ->
                String.format(
                    "sent to queue: %s, message_type: %s body: %s",
                    queueName, messageType, messageString));
    }

    public static String generateIncomingMessageId(Object headers) {
        return buildHash(headers.toString());
    }

    public static String buildHash(String message) {
        String hash = null;
        try {
            hash = DigestUtils.md5DigestAsHex(IOUtils.toInputStream(message)).toUpperCase();
        } catch (IOException e) {
            log.error(ExceptionUtils.getMessage(e));
        }
        return hash;
    }
}
