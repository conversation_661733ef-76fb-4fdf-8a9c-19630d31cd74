package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.persistence.lucyapi.PrinterRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.Printer;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@Log4j2
public class PrinterService {

  private final PrinterRepository printerRepository;

  public static final String POST_REQUEST_HEADER =
      "<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\n"
          + "    <s:Header>\n"
          + "        <parameter xmlns=\"http://www.epson-pos.com/schemas/2011/03/epos-print\">\n"
          + "            <devid>local_printer</devid>\n"
          + "            <timeout>300000</timeout>\n"
          + "            <printjobid>%s</printjobid>\n"
          + "        </parameter>\n"
          + "    </s:Header>\n"
          + "    <s:Body>\n"
          + "       <epos-print xmlns=\"http://www.epson-pos.com/schemas/2011/03/epos-print\">\n";

  public static final String POST_REQUEST_FOOTER =
      "        </epos-print>\n"
          + "                    </s:Body>\n"
          + "                </s:Envelope>\n";

  public PrinterService(PrinterRepository printerRepository) {
    this.printerRepository = printerRepository;
  }

  public Printer create(Printer printer) {
    log.debug(() -> String.format("Save printer %s", printer.getLabel()));

    return printerRepository.save(printer);
  }

  public List<Printer> findAll() {
    return printerRepository.findAll();
  }

  public Printer findById(UUID printerId) {
    log.debug(() -> String.format("Find printer %s", printerId));

    return printerRepository
        .findById(printerId)
        .orElseThrow(() -> new ResourceNotFoundException(Printer.class, printerId.toString()));
  }

  public Printer update(Printer printer) {
    log.debug(() -> String.format("Update printer %s", printer.getLabel()));

    return printerRepository.save(printer);
  }

  public void delete(Printer printer) {
    log.debug(() -> String.format("Delete printer %s", printer.getLabel()));

    printerRepository.delete(printer);
  }
}
