package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

@Service
@RequiredArgsConstructor
@Log4j2
public class InvoiceSpydusEdiStep extends SpydusEdiStepTemplate {

  @Value(
      "${jira.invoice-spydus-edi.webhook.url:"
          + "https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf2-9041-7d91-8bad-615539844970}")
  private String jiraWebhookUrl;

  @Value(
      "${jira.invoice-spydus-edi.webhook.secret:"
          + "2c240873b06ce9ba4cc96bce4276355bf8eac9fa}")
  private String jiraWebhookSecret;

  @Value("${invoice-spydus-edi.url}")
  private String invoiceSpydusEdiUrl;

  @Value("${spydus-edi-orchestrator.skip-spydus-edi-step-in-test-env: false}")
  private boolean skipStepInTest;

  @Value("${spydus-edi-orchestrator.webclient.timeout.seconds:10}")
  private int timeoutSeconds;

  @Override
  protected ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration) {

    String customerCode = spydusEdiOrchestration.getCustomerCode();
    if (skipStepInTest) {
      log.info("Skip this step in test environment");
    } else {
      retrieveInvoices(customerCode);
    }
    return ProcessingResult.of(true, null);
  }

  private void retrieveInvoices(String customerCode) {
    WebClient.builder()
        .baseUrl(invoiceSpydusEdiUrl)
        .defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        .build()
        .method(HttpMethod.POST)
        .uri(String.format("/SpydusEDI/invoices?customer=%s", customerCode))
        .retrieve()
        .bodyToMono(String.class)
        .timeout(Duration.ofSeconds(timeoutSeconds))
        .onErrorReturn("Reached to timeout! Checking all invoices are valid in next steps")
        .blockOptional()
        .ifPresent(
            response ->
                log.info(
                    "Response of invoice spydus edi for customer code {} : {}",
                    customerCode,
                    response));
  }

  @Override
  protected String getJiraWebhookUrl() {
    return jiraWebhookUrl;
  }

  @Override
  protected String getJiraWebhookSecret() {
    return jiraWebhookSecret;
  }
}
