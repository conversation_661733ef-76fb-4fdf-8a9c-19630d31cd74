package au.com.peterpal.lucyapi.core.service;

import static au.com.peterpal.lucyapi.utils.Helper.notNull;
import static org.springframework.util.Assert.hasText;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.model.BibRecord;
import java.util.Comparator;

import lombok.extern.log4j.Log4j2;
import lucy.catalogue.codes.RecordSourceTypeCode;
import lucy.cataloguing.CataloguingException;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.codes.BibRecordStatus;
import lucy.cataloguing.codes.BibRecordType;
import lucy.cataloguing.entity.*;
import lucy.marc.MarcException;
import org.marc4j.marc.Record;
import org.springframework.stereotype.Service;
import lucy.common.NotFoundException;
import java.util.ArrayList;
import java.util.List;

@Service
@Log4j2
public class BibRecordService {

  static final String USER_NAME = "lucy-api";

  private RemoteCataloguing remoteCataloguing;

  public BibRecordService(RemoteCataloguing remoteCataloguing) {
    this.remoteCataloguing = remoteCataloguing;
  }

  /**
   * Returns a Bib record given a Bib record id.
   *
   * @param bibRecordId
   * @return bib record for the given id.
   * @throws NotFoundException if a bib record does not exist for the given id.
   */
  public BibRecord getBibRecord(String bibRecordId) throws NotFoundException {
    hasText(bibRecordId, "bibRecordId must not be null and must contain at least one non-whitespace character");
    lucy.cataloguing.entity.BibRecord bibRecord = remoteCataloguing.getBibRecord(Integer.valueOf(bibRecordId));
    return BibRecord.builder()
        .bibRecordId(bibRecord.getPk())
        .type(bibRecord.getBibRecordType().getCode().toUpperCase())
        .status(bibRecord.getStatus().getCode().toUpperCase())
        .build();
  }

  public lucy.cataloguing.entity.BibRecord getBibRecord(Integer id) {
    notNull(id, "BibRecord id must be not null");

    try {
      return remoteCataloguing.getBibRecord(id);
    } catch (NotFoundException ex) {
      String msg = String.format("Could not find bib record with id %d", id);
      throw new BusinessException(msg, ex);
    }
  }

  public List<lucy.cataloguing.entity.BibRecord> createFullBibRecord(BibCollection bibCollection, List<Holding> holdings,
                                             List<Acquisition> acquisitions, List<BibTemplate> bibTemplates,
                                             lucy.cataloguing.entity.BibRecord baseBibRecord) {
    try {
      return remoteCataloguing.createFullBibRecords(bibCollection, holdings, acquisitions, bibTemplates, baseBibRecord);
    } catch (CataloguingException e) {
      String msg = "Failed to create full bib record. " + e.getMessage();
      log.error(msg, e);
      throw new BusinessException(msg);
    }
  }

  /**
   *
   * @param bibRecordId
   * @return
   * @throws NotFoundException
   * @throws MarcException
   */
  public String getBibRecordMarcInJson(String bibRecordId) throws NotFoundException, MarcException {
    lucy.cataloguing.entity.BibRecord bibRecord = remoteCataloguing.getBibRecord(Integer.valueOf(bibRecordId));

    Record marcRecord = bibRecord.getMarcRecord();

    return MarcStreamToJsonReader.convertMarcInJson(marcRecord);
  }

  public List<lucy.cataloguing.entity.BibRecord> getSuppliedBibRecords(int bibCollectionPk) {
    try {
      BibCollection bibCollection = remoteCataloguing.getBibCollection(bibCollectionPk);
      return new ArrayList<>(bibCollection.getSuppliedBibRecords());
    } catch (NotFoundException e) {
      String message = String.format("Could not get bib collection with pk %s", bibCollectionPk);
      log.error(message, e);
      throw new ResourceNotFoundException(message);
    }
  }

  public lucy.cataloguing.entity.BibRecord getBaseBibRecord(List<lucy.cataloguing.entity.BibRecord> bibRecords){
    return bibRecords.stream()
        .filter(b -> b.getBibRecordType().equals(BibRecordType.NORMAL))
        .filter(b -> b.getStatus().equals(BibRecordStatus.INCOMPLETE))
        .max(Comparator.comparing(o -> o.getLastModificationRecord().getDateModified().toInstant()))
        .orElseThrow(() -> new BusinessException("Could not find base bib record"));
  }

  public void updateStatus(lucy.cataloguing.entity.BibRecord bibRecord, BibRecordStatus status) {
    bibRecord.setStatus(status);
    bibRecord.recordModification(USER_NAME, RecordSourceTypeCode.UNSPEC, "COMPLETED");
    remoteCataloguing.updateBibRecord(bibRecord);
  }

}
