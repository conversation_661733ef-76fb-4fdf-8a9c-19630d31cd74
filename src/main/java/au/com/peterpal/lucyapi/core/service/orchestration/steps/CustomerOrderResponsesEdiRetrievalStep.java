package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.core.service.orchestration.CustomerInvoicesEdiServiceClient;
import au.com.peterpal.lucyapi.core.service.orchestration.WorkflowException;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.RetrieveLucyInvoicesResponse;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Log4j2
public class CustomerOrderResponsesEdiRetrievalStep extends SpydusEdiStepTemplate {

  @Value(
      "${jira.customer-invoice-edi-retrieval.webhook.url:"
          + "https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf1-fdc0-7f6f-b7c6-ea9cbbd9a063}")
  private String jiraWebhookUrl;

  @Value(
      "${jira.customer-invoice-edi-retrieval.webhook.secret:"
          + "8f34038b0a8bcbff5983dcbaad91c62acd5dbf36}")
  private String jiraWebhookSecret;

  @Value("${customer-order-responses-edi-service.url}")
  private String customerOrderResponsesEdiUrl;

  private final CustomerInvoicesEdiServiceClient customerInvoicesEdiServiceClient;

  @Override
  protected ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration) {

    List<RetrieveLucyInvoicesResponse> lucyInvoices =
        customerInvoicesEdiServiceClient.retrieveInvoiceByCustomer(
            spydusEdiOrchestration.getCustomerCode(),
            customerOrderResponsesEdiUrl,
            "/api/admin/responses");

    List<String> validLucy4InvoicesFromEdi =
        lucyInvoices.stream()
            .map(RetrieveLucyInvoicesResponse::getValidInvoices)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
    log.info("Retrieved lucy invoices with valid Invoices:{}", validLucy4InvoicesFromEdi);

    PartiallyInvoicedWorkOrderMessage partiallyInvoicedWorkOrderMessage =
        getPartiallyInvoicedMessage(spydusEdiOrchestration);

    List<String> lucy4InvoicesFromCustomerInvoice =
        partiallyInvoicedWorkOrderMessage.getInvoices().stream()
            .map(PartiallyInvoicedWorkOrderMessage.Invoice::getLucy4InvoiceNumber)
            .collect(Collectors.toList());
    if (CollectionUtils.isEqualCollection(
        validLucy4InvoicesFromEdi, lucy4InvoicesFromCustomerInvoice)) {
      return ProcessingResult.of(true, null);
    }

    Collection<String> lucy4InvoicesMissing =
        CollectionUtils.subtract(lucy4InvoicesFromCustomerInvoice, validLucy4InvoicesFromEdi);
    Collection<String> lucy4InvoicesInAdditional =
        CollectionUtils.subtract(validLucy4InvoicesFromEdi, lucy4InvoicesFromCustomerInvoice);
    String errorMessage = String.format(
        "Lucy4 invoices in customer invoice service and customer order response edi are not matching."
            + " The lucy4 invoices no missing: %s, and lucy4 invoices no in additional: %s",
        lucy4InvoicesMissing, lucy4InvoicesInAdditional);
    log.info(errorMessage);
    throw new WorkflowException(errorMessage);
  }

  @Override
  protected String getJiraWebhookUrl() {
    return jiraWebhookUrl;
  }

  @Override
  protected String getJiraWebhookSecret() {
    return jiraWebhookSecret;
  }
}
