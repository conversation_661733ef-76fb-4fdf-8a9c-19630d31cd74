package au.com.peterpal.lucyapi.core.service.orchestration.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;

@ToString
@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TokenResponse {
    @JsonProperty(value = "access_token")
    @NonNull
    @NotEmpty
    private String accessToken;
}
