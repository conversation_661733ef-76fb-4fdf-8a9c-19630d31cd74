package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.model.SupplyErrorAdvicePrintMessage;
import javax.jms.ConnectionFactory;
import javax.jms.Session;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.jms.dsl.Jms;

@Log4j2
@Configuration
public class SupplyErrorAdvicePrintListener {

  private final ConnectionFactory connectionFactory;
  private final SupplyErrorAdvicePrinterService supplyErrorAdvicePrinterService;
  private final String supplyErrorAdviceQueueName;

  public SupplyErrorAdvicePrintListener(
      ConnectionFactory connectionFactory,
      SupplyErrorAdvicePrinterService supplyErrorAdvicePrinterService,
      @Value("${supply-error-advice-slip.queue.name:supply-error-advice-slip-print-channel}")
          String supplyErrorAdviceQueueName) {
    this.connectionFactory = connectionFactory;
    this.supplyErrorAdvicePrinterService = supplyErrorAdvicePrinterService;
    this.supplyErrorAdviceQueueName = supplyErrorAdviceQueueName;
  }

  @Bean
  public IntegrationFlow supplyErrorAdviceJmsListener() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(this.connectionFactory)
                .destination(supplyErrorAdviceQueueName)
                .configureListenerContainer(
                    spec ->
                        spec.sessionTransacted(true)
                            .sessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE)))
        .enrichHeaders(
            headerEnricherSpec ->
                headerEnricherSpec.headerFunction(
                    IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID,
                    message ->
                        IncomingMessageProcessor.generateIncomingMessageId(message.getPayload()),
                    true))
        .wireTap(IncomingMessageLoggingControlFlow.LOG_INCOMING_MESSAGE_CHANNEL)
        .log(m -> "Received via JMS: " + m)
        .transform(Transformers.fromJson(SupplyErrorAdvicePrintMessage.class))
        .handle(supplyErrorAdvicePrinterService)
        .get();
  }
}
