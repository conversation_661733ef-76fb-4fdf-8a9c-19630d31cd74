package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.core.service.retrieve.RetrieveService;
import au.com.peterpal.lucyapi.model.*;
import au.com.peterpal.lucyapi.persistence.lucy.LucyHelperRepository;
import au.com.peterpal.lucyapi.persistence.lucy.TitleOrderActionInfoRepository;
import au.com.peterpal.lucyapi.utils.Affirm;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.codes.OrganisationIdentifierTypeCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.cataloguing.CataloguingException;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.BibTemplate;
import lucy.cataloguing.util.TitleOrderMatch;
import lucy.common.NotFoundException;
import lucy.fulfillment.beans.RemoteFulfillment;
import lucy.fulfillment.codes.CataloguingAction;
import lucy.fulfillment.entity.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.ejb.EJBTransactionRolledbackException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static au.com.peterpal.lucyapi.utils.Helper.notNull;
import static au.com.peterpal.lucyapi.utils.LambdaExceptionWrappers.throwingConsumerWrapper;
import static java.util.stream.Collectors.toList;
import static org.springframework.util.Assert.hasText;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Service
@Log4j2
public class TitleOrderService {

  @Value("${title.order.max.hours.since.records.retrieved:2}")
  private Integer maxHoursSinceRecordsRetrieved;

  @Value("${addToPurchaseOrderRetry:3}")
  @Setter
  private int addToPurchaseOrderRetry;

  private RemoteCataloguing cataloguing;
  private RemoteFulfillment fulfillment;
  private final TitleOrderActionInfoRepository repo;
  private final RetrieveService retrieveService;
  private final LucyHelperRepository helperRepo;

  public TitleOrderService(
          RemoteCataloguing cataloguing,
          RemoteFulfillment fulfillment,
          TitleOrderActionInfoRepository repo,
          RetrieveService retrieveService,
          LucyHelperRepository helperRepo) {
    this.cataloguing = cataloguing;
    this.fulfillment = fulfillment;
    this.repo = repo;
    this.retrieveService = retrieveService;
    this.helperRepo = helperRepo;
  }

  public TitleOrderInfo add(TitleOrderInfo titleOrderInfo) {
    return Optional.ofNullable(titleOrderInfo)
            .map(info -> TitleOrderInfo.from(fulfillment.addTitleOrder(titleOrderInfo.to()))
                    .orElseThrow(() -> new BusinessException(String.format("Could not create title order for %s", titleOrderInfo)))
            )
            .orElseThrow(() -> new IllegalArgumentException("TitleOrderInfo must not be null"));
  }

  public TitleOrderInfo getTitleOrder(String workOrderNumber, boolean searchTitleClosed) {
    try {
      return TitleOrderInfo.from(fulfillment.getTitleOrder(workOrderNumber))
          .orElseThrow(
              () ->
                  new ResourceNotFoundException(
                      String.format("Title Order for %s Not Found", workOrderNumber)));

    } catch (Exception ex) {
      log.warn(
          () -> String.format("Could not find title order for work order %s", workOrderNumber));
      if (searchTitleClosed) {
        return getCloseTitleOrder(workOrderNumber);
      }
      throw new ResourceNotFoundException(
          String.format("Title Order for %s Not Found", workOrderNumber));
    }
  }

  public OpenTitleOrder getOpenTitleOrderWithWorkOrderNumber(String workOrderNumber) {
    try {
      return fulfillment.getTitleOrder(workOrderNumber);
    } catch (Exception ex) {
      log.warn("Could not find title order for work order {}", workOrderNumber, ex);
      throw new ResourceNotFoundException(
              String.format("Title Order for %s Not Found", workOrderNumber));
    }
  }

  public String getCustomerCode(String workOrderNumber) {
    TitleOrderInfo titleOrder = getTitleOrderOpenOrClose(workOrderNumber);
    return titleOrder.getCustomerCode();
  }

  /**
   * Given a comma separated title orders return a list of corresponding objects.
   *
   * @param orderNumbers comma separated order numbers
   * @return list of corresponding <class>OpenTitleOrders</class>
   */
  public List<TitleOrder> getTitleOrders(String orderNumbers) {
    return Optional.ofNullable(orderNumbers)
        .filter(str -> !str.isEmpty())
        .map(
            str ->
                Stream.of(str.split(","))
                    .map(String::trim)
                    .map(this::getTitleOrder)
                    .filter(Objects::nonNull)
                    .collect(toList()))
        .orElseGet(ArrayList<TitleOrder>::new);
  }

  private TitleOrder getTitleOrder(String openTitleNumber) {
    try {
      TransactionIdentifier identifier = new TransactionIdentifier();
      identifier.setTransactionIdValue(openTitleNumber);
      return fulfillment.getTitleOrder(identifier);
    } catch (NotFoundException ex) {
      log.warn(() -> String.format("Could not find open order number %s", openTitleNumber));
      return getCloseTitleOrderByTitleNumber(openTitleNumber);
    }
  }

  private TitleOrder getCloseTitleOrderByTitleNumber(String titleNumber) {
    try {
      TransactionIdentifier identifier = new TransactionIdentifier();
      identifier.setTransactionIdValue(titleNumber);
      return fulfillment.getClosedTitleOrder(identifier);
    } catch (NotFoundException ex) {
      log.warn(() -> String.format("Could not find close order number %s", titleNumber));
    }
    return null;
  }

  public void sendTitleOrder(OpenTitleOrder order) throws CataloguingException {
    cataloguing.sendTitleOrder(order);
  }

  public void sendTitleOrder(String orderNumber) throws CataloguingException, NotFoundException {
    hasText(orderNumber, "Order number must not be null or empty");

    OpenTitleOrder order = fulfillment.getTitleOrder(new TransactionIdentifier(orderNumber));
    sendTitleOrder(order);
  }

  public void updateBibRecordId(String orderNumber, Integer bibRecordId) throws NotFoundException {
    notNull(bibRecordId, "Bib record id must not be null");
    hasText(orderNumber, "Order number must not be null or empty");

    OpenTitleOrder existingOrder =
            fulfillment.getTitleOrder(new TransactionIdentifier(orderNumber));
    existingOrder.setBibRecordId(bibRecordId);
    fulfillment.updateTitleOrder(existingOrder);
  }

  public List<OpenTitleOrder> getTitleOrders(
          String customer, CataloguingAction cataloguingAction, LocalDate dateFrom, LocalDate dateTo) {
    OrganisationIdentifier org =
            new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS, customer);

    return fulfillment.getTitleOrders(
            org, cataloguingAction, fromLocalDate(dateFrom), fromLocalDate(dateTo));
  }

  public List<TitleOrderActionInfo> getTitleOrderActionInfo(
          String customer, LocalDate dateFrom, LocalDate dateTo) {
    hasText(customer, "Customer must not be null or empty");
    return repo.getTitleOrderActionInfo(customer, dateFrom, dateTo);
  }

  public List<MatchInfo> match(String customerCode, int bibTemplateId) throws NotFoundException {

    List<OpenTitleOrder> orders = getTitleOrders(customerCode, CataloguingAction.MATCH, null, null);
    log.info(String.format("Total number of orders to match %d", orders.size()));

    List<String> tos = Optional.ofNullable(orders)
            .map(l -> l.stream()
                    .map(to -> to.getOrderId().getTransactionIdValue())
                    .collect(toList())
            )
            .orElse(Collections.emptyList());

    return match(tos, bibTemplateId);
  }

  public List<MatchInfo> match(List<String> titleOrderNumbers, int bibTemplateId)
          throws NotFoundException {

    BibTemplate template = cataloguing.getBibTemplate(bibTemplateId);

    // 2. for each title order number; cataloguing.matchTitleOrder -> TitleOrderMatch
    return Optional.ofNullable(titleOrderNumbers)
            .filter(list -> !list.isEmpty())
            .map(
                    list -> {
                      List<MatchInfo> oto = new ArrayList<>();
                      list.stream()
                              .forEach(throwingConsumerWrapper(str -> oto.add(doMatch(str, template))));
                      return oto;
                    })
            .orElseThrow(() -> new IllegalArgumentException("Title orders can not be null or empty"));
  }

  private MatchInfo doMatch(String str, BibTemplate template) throws NotFoundException {
    TransactionIdentifier trnId = new TransactionIdentifier(str);
    OpenTitleOrder order = fulfillment.getTitleOrder(trnId);
    String msg = null;
    TitleOrderMatch match = null;

    if (order != null && CataloguingAction.MATCH.equals(order.getCataloguingAction())) {
      try {
        if (isMatchEnabled(order)) {
          match = cataloguing.matchTitleOrder(order, template);

          // Refresh the title order after the match
          order = fulfillment.getTitleOrder(trnId);
        } else {
          msg = "Title order was not matched because it has not been retrieved or has expired.";
          String title = order.getTitle();
          String err = "Title order '%s' was not matched because client records have not been retrieved or have expired.";
          log.warn(() -> String.format(err, title));
          order.setCataloguingAction(CataloguingAction.RETRIEVE);
          fulfillment.updateTitleOrder(order);
        }
      } catch (Exception ex) {
        String orderId = order.getOrderId().getTransactionIdValue();
        String title = order.getTitle();
        msg = "Matching of this title order has failed.";
        String err = String.format("Matching failed: order id %s, title %s", orderId, title);
        log.error(() -> err, ex);
      }
    } else {
      msg = handleWrongNextAction(order, trnId);
    }

    return MatchInfo.from(order, match, template, msg);
  }

  private String handleWrongNextAction(OpenTitleOrder order, TransactionIdentifier id) {
    String msg = null;
    if (order == null) {
      String err = "Could not find title order for %s.";
      log.warn(() -> String.format(err, id.getTransactionIdValue()));
      msg = err;
    } else {
      String title = order.getTitle();
      String nextAction = order.getCataloguingAction().name();
      String err = String.format("Title order '%s' next action is wrong; expecting MATCH; found %s", title, nextAction);
      log.warn(() -> err);
      order.setCataloguingAction(CataloguingAction.RETRIEVE);
      msg = "Next action is wrong. Expecting MATCH.";
    }
    return msg;
  }

  /**
   *
   * @param customer
   * @param searchCriteria
   * @param dateFrom
   * @param dateTo
   * @return
   */
  public List<TitleOrderInfo> getTitleOrdersByState(
          String customer, SearchCriteria searchCriteria, LocalDate dateFrom, LocalDate dateTo) {
    notNull(searchCriteria, "Search criteria must not be null");

    List<OpenTitleOrder> orders = this.getTitleOrders(customer, null, dateFrom, dateTo);
    List<TitleOrderInfo> titleOrders = TitleOrderInfo.from(orders);

    return titleOrders.stream()
            .filter(to -> filter(searchCriteria.getCataloguingStates(), to.getState()))
            .filter(to -> filter(searchCriteria.getSource(), to.getSourceCode()))
            .filter(to -> filter(searchCriteria.getFundCode(), to.getFundCode()))
            .filter(to -> filter(searchCriteria.getMedia(), to.getMedia()))
            .filter(to -> filter(searchCriteria.getCategory2(), to.getCategoryCode2()))
            .collect(toList());
  }

  public List<TitleOrderInfo> updateState(CataloguingState newState, List<String> orderNumbers) {
    notNull(newState, "Cataloguing state must not be null");
    notNull(orderNumbers, "List of order numbers must not be null");


    return orderNumbers.stream()
            .map(
                    str -> {
                      TitleOrderInfo result = null;
                      try {
                        OpenTitleOrder order = fulfillment.getTitleOrder(new TransactionIdentifier(str));
                        order.setCataloguingAction(ActionStateMap.toAction(newState));
                        fulfillment.updateTitleOrder(order);
                        result = TitleOrderInfo.from(order).orElseGet(null);
                      } catch (NotFoundException ex) {
                        log.warn(() -> String.format("Could not find order number %s", str));
                      }
                      return result;
                    }
            )
            .filter(Objects::nonNull)
            .collect(toList());
  }

  /**
   * Process the list of title order numbers. This method calls directly Lucy4.
   * It invokes sp_ProcessTitleOrder for each title order number.
   *
   * @param titleOrderNumberList a list of title order numbers
   */
  public void process(List<String> titleOrderNumberList) {
    Affirm.of(titleOrderNumberList).notNull("list of title numbers must not be null");
    log.info(() -> String.format("Processing title order numbers: %s", titleOrderNumberList));

    List<String> l = titleOrderNumberList;
    int retryCount = 0;
    do {
      l = processList(l);
      retryCount++;
    } while (isNotEmpty(l) && retryCount < addToPurchaseOrderRetry);
    if (isNotEmpty(l)) {
      log.warn(String.format("Some TOs did not process: %s", l));
    }
  }

  protected List<String> processList(List<String> titleOrderNumberList) {
    return titleOrderNumberList.stream()
            .map(ton -> {
              String result = null;
              try {
                helperRepo.processTitleOrder(ton);
              } catch (Exception ex) {
                log.warn(String.format("Exception while processing TO %s", ton), ex);
                result = ton;
              }
              return result;
            })
            .filter(Objects::nonNull)
            .collect(toList());
  }

  private boolean filter(List<String> criteria, CataloguingState value) {
    return Optional.ofNullable(criteria)
            .map(c -> value != null && criteria.contains(value.getCode()))
            .orElse(true);
  }

  private boolean filter(String criteria, String value) {
    return Optional.ofNullable(criteria)
            .map(c -> value != null && value.equals(c))
            .orElse(true);
  }

  private Date fromLocalDate(LocalDate dateToConvert) {
    Date result = null;
    if (dateToConvert != null) {
      result = Date.from(dateToConvert.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
    return result;
  }

  private boolean isMatchEnabled(OpenTitleOrder order) {
    return order != null &&
            order.getLastRetrievedClientRecords() != null &&
            order.getLastRetrievedClientRecords()
                    .after(DateUtils.addHours(new Date(), -maxHoursSinceRecordsRetrieved));
  }

  private boolean isExported(CataloguingAction action) {
    return CataloguingState.EXPORTED.equals(ActionStateMap.fromAction(action));
  }

  private boolean isNotExported(CataloguingAction action) {
    return !isExported(action);
  }
  public ItemReceiptAssignment getItemReceiptAssignment(String workOrderNumber, boolean searchTitleClosed){
    TitleOrderInfo titleOrder = getTitleOrder(workOrderNumber, searchTitleClosed);
    log.debug(() -> String.format("Title order is: %s", titleOrder.getOrderNumber()));

    List<TitleOrder> titleOrders = getTitleOrders(titleOrder.getOrderNumber());

    List<ItemReceiptAssignment> itemReceiptAssignments = (List<ItemReceiptAssignment>) titleOrders.get(0).getReceipts();
    log.debug(() -> String.format("Item receipts are: %s", itemReceiptAssignments.stream()
            .map(ItemReceiptAssignment::getOrderAssignmentId).collect(Collectors.joining(", "))));

    return itemReceiptAssignments.stream().filter(ira -> ira.getOrderAssignmentId()==null? false : ira.getOrderAssignmentId().equals(workOrderNumber)).findFirst().get();
  }

  public TitleOrderInfo getCloseTitleOrder(String workOrderNumber) {
    try {
      return TitleOrderInfo.from(fulfillment.getClosedTitleOrder(workOrderNumber))
          .orElseThrow(
              () ->
                  new ResourceNotFoundException(
                      String.format("Closed Title Order for %s Not Found", workOrderNumber)));
    } catch (Exception ex) {
      log.warn(
          () -> String.format("Could not find closed title order for work order %s", workOrderNumber));
      throw new ResourceNotFoundException(
          String.format("Closed Title Order for %s Not Found", workOrderNumber));
    }
  }

  public TitleOrderInfo getTitleOrderOpenOrClose(String workOrderNumber) {
    Optional<TitleOrderInfo> titleOrderInfo = Optional.empty();
    try {
      titleOrderInfo = TitleOrderInfo.from(fulfillment.getTitleOrder(workOrderNumber));
    } catch (NotFoundException | EJBTransactionRolledbackException ex) {
      log.warn(
          String.format(
              "Could not find open title order for work order %s. Trying to find closed title order",
              workOrderNumber),
          ex);
      try {
        titleOrderInfo = TitleOrderInfo.from(fulfillment.getClosedTitleOrder(workOrderNumber));
      } catch (NotFoundException | EJBTransactionRolledbackException e) {
        log.warn(
            String.format("Could not find closed title order for work order %s", workOrderNumber),
            e);
      }
    }
    return titleOrderInfo.orElseThrow(() -> new ResourceNotFoundException(
        String.format("Title Order for %s Not Found", workOrderNumber)));
  }
}
