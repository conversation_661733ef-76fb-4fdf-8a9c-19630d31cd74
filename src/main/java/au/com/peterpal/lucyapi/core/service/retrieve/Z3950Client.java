package au.com.peterpal.lucyapi.core.service.retrieve;

import lucy.cataloguing.entity.RecordSource;
import org.jzkit.search.util.ResultSet.IRResultSetStatus;
import org.marc4j.marc.Record;

/**
 * The Z3950 client can have more that one implementation: jzkit, yaz4j
 */
public interface Z3950Client {

  public static int COMPLETE = IRResultSetStatus.COMPLETE;

  public static int FAILURE = IRResultSetStatus.FAILURE;

  //void init() throws Z3950Exception;

  void init(RecordSource recordSource) throws Z3950Exception;

  void query(String query) throws Z3950Exception;

  /**
   * @return the resultCount
   */
  int getResultCount();

  /**
   * @return the status
   */
  int getStatus();

  boolean hasNext() throws Z3950Exception;

  Record next();

  void close();

}
