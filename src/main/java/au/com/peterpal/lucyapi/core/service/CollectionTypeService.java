package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.model.TitleOrderInfo;
import au.com.peterpal.lucyapi.persistence.lucyapi.CollectionTypeByFundRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.CollectionTypeByFund;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.CollectionTypeByFundKey;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CollectionTypeService {
    private final TitleOrderService titleOrderService;
    private final CollectionTypeByFundRepository collectionTypeByFundRepository;

    public CollectionTypeService(TitleOrderService titleOrderService,
                                 CollectionTypeByFundRepository collectionTypeByFundRepository) {
        this.titleOrderService = titleOrderService;
        this.collectionTypeByFundRepository = collectionTypeByFundRepository;
    }

    public Optional<CollectionTypeByFund> getCollectionType(String workOrderNumber) {
        TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber, false);
        CollectionTypeByFundKey collectionTypeByFundKey = CollectionTypeByFundKey.of(titleOrder.getCustomerCode(), titleOrder.getFundCode());
        return collectionTypeByFundRepository.findById(collectionTypeByFundKey);
    }

}
