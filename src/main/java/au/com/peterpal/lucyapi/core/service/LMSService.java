package au.com.peterpal.lucyapi.core.service;

import static au.com.peterpal.lucyapi.batch.control.BatchService.isSeparateBatch;
import static java.nio.file.StandardCopyOption.REPLACE_EXISTING;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.batch.control.BatchService;
import au.com.peterpal.lucyapi.batch.dto.SearchParams;
import au.com.peterpal.lucyapi.batch.model.BatchInfo;
import au.com.peterpal.lucyapi.batch.model.BatchStatus;
import au.com.peterpal.lucyapi.batch.model.ExportInfo;
import au.com.peterpal.lucyapi.core.service.spydus.SpydusClient;
import au.com.peterpal.lucyapi.model.BibRecordType;
import au.com.peterpal.lucyapi.model.TitleOrderInfo;
import au.com.peterpal.lucyapi.model.spydus.JiraCreateTicketRequest;
import au.com.peterpal.lucyapi.model.spydus.JiraProcessBatchDetail;
import au.com.peterpal.lucyapi.model.spydus.ProcessBatchResponse;
import au.com.peterpal.lucyapi.model.spydus.ProcessFileToSpydusResult;
import au.com.peterpal.lucyapi.persistence.cataloguing.BatchJdbcTemplateRepository;
import au.com.peterpal.lucyapi.persistence.cataloguing.model.BatchProductIdValue;
import au.com.peterpal.lucyapi.persistence.lucyapi.LMSConfigurationRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.SeparateBatchByHoldingRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.codes.BibRecordBatchStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class LMSService {

  public static final int LOAD_TYPE_TEST = 1;
  public static final int LOAD_TYPE_REAL = 0;
  private final TitleOrderService titleOrderService;
  private final SpydusClient spydusClient;
  private final LMSConfigurationRepository lmsConfigurationRepository;
  private final BatchService batchService;
  private final SeparateBatchByHoldingRepository separateBatchByHoldingRepository;
  private final BatchJdbcTemplateRepository batchJdbcTemplateRepository;
  private final JiraWebhookService jiraWebhookService;
  private final EmailMarcRecordsService emailMarcRecordsService;
  private final String fileExportDir;
  private final LocalTime endOfDayTime;
  private final String processBatchStatusSuccessList;

  public LMSService(
      TitleOrderService titleOrderService,
      SpydusClient spydusClient,
      LMSConfigurationRepository lmsConfigurationRepository,
      BatchService batchService,
      SeparateBatchByHoldingRepository separateBatchByHoldingRepository,
      BatchJdbcTemplateRepository batchJdbcTemplateRepository,
      JiraWebhookService jiraWebhookService,
      EmailMarcRecordsService emailMarcRecordsService,
      @Value("${file.export.dir:''}") String fileExportDir,
      @Value("${upload-bib-records.end-of-day-time:16:30}") String endOfDayTime,
      @Value("${upload-bib-records.spydus.process-batch-status-success: 5, 6, 7}")
          String processBatchStatusSuccess) {
    this.titleOrderService = titleOrderService;
    this.spydusClient = spydusClient;
    this.lmsConfigurationRepository = lmsConfigurationRepository;
    this.batchService = batchService;
    this.separateBatchByHoldingRepository = separateBatchByHoldingRepository;
    this.batchJdbcTemplateRepository = batchJdbcTemplateRepository;
    this.jiraWebhookService = jiraWebhookService;
    this.emailMarcRecordsService = emailMarcRecordsService;
    this.fileExportDir = fileExportDir;
    this.endOfDayTime = LocalTime.parse(endOfDayTime, DateTimeFormatter.ofPattern("HH:mm"));
    this.processBatchStatusSuccessList = processBatchStatusSuccess;
  }

  public void deleteOnOrderRecord(String workOrderNumber) {
    TitleOrderInfo titleOrderInfo = titleOrderService.getTitleOrder(workOrderNumber, false);

    String customerCode = titleOrderInfo.getCustomerCode();
    String isbn = titleOrderInfo.getProductIdValue();
    Map<String, Object> jiraMetadata = new HashMap<>();
    jiraMetadata.put("workOrderNumber", workOrderNumber);
    loadLms(customerCode);
    spydusClient.deleteOnOrderRecordWithRetry(customerCode, isbn, jiraMetadata);
  }

  public void deleteOnOrderRecordsByBatchId(Integer batchId) {
    Map<String, Object> jiraMetadata = new HashMap<>();
    jiraMetadata.put("batchId", batchId);
    List<BatchProductIdValue> productIdValueByBatchIds =
        batchJdbcTemplateRepository.getProductIdValueByBatchId(batchId);
    if (productIdValueByBatchIds.isEmpty()) {
      log.warn("Cannot get isbn for batchId {}", batchId);
      return;
    }
    loadLms(productIdValueByBatchIds.get(0).getCustomerCode());
    for (BatchProductIdValue productIdValueByBatchId : productIdValueByBatchIds) {
      try {
        spydusClient.deleteOnOrderRecordWithRetry(
            productIdValueByBatchId.getCustomerCode(),
            productIdValueByBatchId.getProductIdValue(),
            jiraMetadata);
      } catch (Exception e) {
        log.error(
            "Cannot delete order records for batchId {}, customer {}, isbn {}",
            batchId,
            productIdValueByBatchId.getCustomerCode(),
            productIdValueByBatchId.getProductIdValue(),
            e);
      }
    }
  }

  public void uploadBibRecordsToSierra(String customerCode, List<Integer> batchIdList) {
    LMSConfiguration lmsConfiguration =
        lmsConfigurationRepository
            .findByCustomerCodeAndLmsType(customerCode, LMSType.SIERRA)
            .orElseThrow(
                () ->
                    new BusinessException(
                        String.format(
                            "customer with code %s not found to upload bib records to Sierra",
                            customerCode)));
    if (CollectionUtils.isEmpty(batchIdList)) {
      uploadBibRecordsToSierra(lmsConfiguration);
    } else {
      List<BatchInfo> batchInfos =
          batchIdList.stream().map(batchService::getBatch).collect(Collectors.toList());
      validateBatchInfos(customerCode, batchInfos);
      this.uploadBibRecordsToFtpFolder(lmsConfiguration, batchInfos);
    }
  }

  public void uploadBibRecordsForOCLC(String customerCode, List<Integer> batchIdList) {
    LMSConfiguration lmsConfiguration =
        lmsConfigurationRepository
            .findByCustomerCodeAndLmsType(customerCode, LMSType.OCLC)
            .orElseThrow(
                () ->
                    new BusinessException(
                        String.format(
                            "Customer with code %s not found for OCLC bib records upload.",
                            customerCode)));
    if (CollectionUtils.isEmpty(batchIdList)) {
      uploadBibRecordsForOCLC(lmsConfiguration);
    } else {
      List<BatchInfo> batchInfos =
          batchIdList.stream().map(batchService::getBatch).collect(Collectors.toList());
      validateBatchInfos(customerCode, batchInfos);
      this.uploadBibRecordsToFtpFolder(lmsConfiguration, batchInfos);
    }
  }

  private void uploadBibRecordsForOCLC(LMSConfiguration lmsConfiguration) {
    String customerCode = lmsConfiguration.getCustomerCode();
    log.info("Uploading OCLC bib records for customer code: {}", customerCode);
    List<BatchInfo> openBatchInfos = getBatchInfos(customerCode, BatchStatus.OPEN);
    updateBatchStatus(openBatchInfos, BibRecordBatchStatus.CLOSED);
    List<BatchInfo> closedBatchInfos = getBatchInfos(customerCode, BatchStatus.CLOSED);

    this.uploadBibRecordsToFtpFolder(lmsConfiguration, closedBatchInfos);
  }

  public void sendBibRecordsToEmail(String customerCode, List<Integer> batchIdList) {
    LMSConfiguration lmsConfiguration =
        lmsConfigurationRepository
            .findByCustomerCodeAndLmsType(customerCode, LMSType.EMAIL)
            .orElseThrow(
                () ->
                    new BusinessException(
                        String.format(
                            "customer with code %s not found to send bib records to email",
                            customerCode)));
    if (CollectionUtils.isEmpty(batchIdList)) {
      sendBibRecordsToEmailWithBatchInfos(lmsConfiguration);
    } else {
      List<BatchInfo> batchInfos =
          batchIdList.stream().map(batchService::getBatch).collect(Collectors.toList());
      validateBatchInfos(customerCode, batchInfos);
      this.sendBibRecordsToEmailWithBatchInfos(lmsConfiguration, batchInfos);
    }
  }

  public void uploadBibRecordsToSpydus(String customerCode, Set<Integer> batchIdList) {
    log.info(
        "Uploading bib records with batchIds {} to Spydus for customer {}",
        batchIdList,
        customerCode);
    LMSConfiguration lmsConfiguration =
        lmsConfigurationRepository
            .findByCustomerCodeAndLmsType(customerCode, LMSType.SPYDUS)
            .orElseThrow(
                () ->
                    new BusinessException(
                        String.format(
                            "customer with code %s not found to upload bib records to Spydus",
                            customerCode)));
    if (CollectionUtils.isEmpty(batchIdList)) {
      loadLms(customerCode);
      uploadBibRecordsToSpydus(lmsConfiguration);
    } else {
      List<BatchInfo> batchInfos =
          batchIdList.stream().map(batchService::getBatch).collect(Collectors.toList());
      validateBatchInfos(customerCode, batchInfos);
      loadLms(customerCode);
      uploadBibRecordsToSpydus(lmsConfiguration, batchInfos);
    }
  }

  private static void validateBatchInfos(String customerCode, List<BatchInfo> batchInfos) {
    List<Integer> batchesWithDifferentCustomerCode = batchInfos.stream()
        .filter(batch -> !batch.getCustomerCode().equals(customerCode))
        .map(BatchInfo::getId)
        .collect(Collectors.toList());

    List<Integer> batchesAlreadySent = batchInfos.stream()
        .filter(batch -> batch.getStatus() == BibRecordBatchStatus.SENT)
        .map(BatchInfo::getId)
        .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(batchesWithDifferentCustomerCode)) {
      throw new BusinessException(
          String.format(
              "The following batch IDs contain a different customer code than expected: %s",
              batchesWithDifferentCustomerCode
          )
      );
    }

    if (CollectionUtils.isNotEmpty(batchesAlreadySent)) {
      throw new BusinessException(
          String.format(
              "The following batch IDs have already been sent and cannot be processed: %s",
              batchesAlreadySent
          )
      );
    }
  }

  public void loadLms(String customerCode) {
    spydusClient.loadLMS(customerCode);
  }

  @Scheduled(cron = "${upload-bib-records-to-spydus.schedule.cron:0 */15 * * * ?}")
  public void uploadBibRecordsToSpydus() {
    List<LMSConfiguration> lsmConfigurations =
        lmsConfigurationRepository.findAllByLmsTypeAndAutomaticallyCloseBatchesIsTrue(
            LMSType.SPYDUS);
    for (LMSConfiguration lmsConfiguration : lsmConfigurations) {
      if (runEveryQuarterHour(lmsConfiguration) || runEndOfDay(lmsConfiguration)) {
        loadLms(lmsConfiguration.getCustomerCode());
        uploadBibRecordsToSpydus(lmsConfiguration);
      }
    }
  }

  @Scheduled(cron = "${upload-bib-records-to-sierra.schedule.cron:0 */15 * * * ?}")
  public void uploadBibRecordsToSierra() {
    LocalDateTime start = LocalDateTime.now();
    List<LMSConfiguration> lmsConfigurations =
        lmsConfigurationRepository.findAllByLmsTypeAndAutomaticallyCloseBatchesIsTrue(
            LMSType.SIERRA);

    for (LMSConfiguration lmsConfiguration : lmsConfigurations) {
      if (runEveryQuarterHour(lmsConfiguration) || runEndOfDay(lmsConfiguration)) {
        uploadBibRecordsToSierra(lmsConfiguration);
      }
    }
    LocalDateTime finish = LocalDateTime.now();
    log.info(
        "Upload bib records to Sierra schedule finished: {},  during: {} seconds",
        finish,
        Duration.between(start, finish).getSeconds());
  }

  @Scheduled(cron = "${upload-bib-records-for-oclc.schedule.cron:0 */15 * * * ?}")
  public void uploadBibRecordsForOCLC() {
    LocalDateTime start = LocalDateTime.now();
    List<LMSConfiguration> lmsConfigurations =
        lmsConfigurationRepository.findAllByLmsTypeAndAutomaticallyCloseBatchesIsTrue(LMSType.OCLC);

    for (LMSConfiguration lmsConfiguration : lmsConfigurations) {
      if (runEveryQuarterHour(lmsConfiguration) || runEndOfDay(lmsConfiguration)) {
        uploadBibRecordsForOCLC(lmsConfiguration);
      }
    }
    LocalDateTime finish = LocalDateTime.now();
    log.info(
        "OCLC bib records upload schedule finished at: {}, duration: {} seconds",
        finish,
        Duration.between(start, finish).getSeconds());
  }

  @Scheduled(cron = "${send-bib-records-to-email.schedule.cron:0 */15 * * * ?}")
  public void sendBibRecordsToEmailWithBatchInfos() {
    LocalDateTime start = LocalDateTime.now();
    List<LMSConfiguration> lmsConfigurations =
        lmsConfigurationRepository.findAllByLmsTypeAndAutomaticallyCloseBatchesIsTrue(
            LMSType.EMAIL);

    for (LMSConfiguration lmsConfiguration : lmsConfigurations) {
      if (runEveryQuarterHour(lmsConfiguration) || runEndOfDay(lmsConfiguration)) {
        sendBibRecordsToEmailWithBatchInfos(lmsConfiguration);
      }
    }
    LocalDateTime finish = LocalDateTime.now();
    log.info(
        "Sent bib records to email schedule finished: {},  during: {} seconds",
        finish,
        Duration.between(start, finish).getSeconds());
  }

  private boolean runEndOfDay(LMSConfiguration lmsConfiguration) {
    return (BatchScheduleType.END_OF_DAY == lmsConfiguration.getBatchSchedule()
            || isEndOfDayOnceWeek(lmsConfiguration.getBatchSchedule()))
        && isEndOfDay();
  }

  private boolean isEndOfDayOnceWeek(BatchScheduleType batchSchedule) {
    DayOfWeek currentDayOfWeek = LocalDate.now().getDayOfWeek();
    return (batchSchedule == BatchScheduleType.MONDAY && currentDayOfWeek == DayOfWeek.MONDAY)
        || (batchSchedule == BatchScheduleType.TUESDAY && currentDayOfWeek == DayOfWeek.TUESDAY)
        || (batchSchedule == BatchScheduleType.WEDNESDAY && currentDayOfWeek == DayOfWeek.WEDNESDAY)
        || (batchSchedule == BatchScheduleType.THURSDAY && currentDayOfWeek == DayOfWeek.THURSDAY)
        || (batchSchedule == BatchScheduleType.FRIDAY && currentDayOfWeek == DayOfWeek.FRIDAY);
  }

  private boolean runEveryQuarterHour(LMSConfiguration lmsConfiguration) {
    return lmsConfiguration.getBatchSchedule() == BatchScheduleType.EVERY_QUARTER_HOUR;
  }

  private boolean isEndOfDay() {
    LocalTime currentTime = LocalTime.now();
    return !endOfDayTime.isAfter(currentTime)
        && currentTime.minusMinutes(15L).isBefore(endOfDayTime);
  }

  private void uploadBibRecordsToSierra(LMSConfiguration lmsConfiguration) {
    String customerCode = lmsConfiguration.getCustomerCode();
    log.info("Uploading bib records to Sierra for customer code {}", customerCode);

    List<BatchInfo> openBatchInfos = getBatchInfos(customerCode, BatchStatus.OPEN);
    updateBatchStatus(openBatchInfos, BibRecordBatchStatus.CLOSED);
    List<BatchInfo> closedBatchInfos = getBatchInfos(customerCode, BatchStatus.CLOSED);

    this.uploadBibRecordsToFtpFolder(lmsConfiguration, closedBatchInfos);
  }

  private void sendBibRecordsToEmailWithBatchInfos(LMSConfiguration lmsConfiguration) {
    String customerCode = lmsConfiguration.getCustomerCode();
    log.info("Sending bib records to email for customer code {}", customerCode);

    List<BatchInfo> openBatchInfos = getBatchInfos(customerCode, BatchStatus.OPEN);
    updateBatchStatus(openBatchInfos, BibRecordBatchStatus.CLOSED);
    List<BatchInfo> closedBatchInfos = getBatchInfos(customerCode, BatchStatus.CLOSED);

    this.sendBibRecordsToEmailWithBatchInfos(lmsConfiguration, closedBatchInfos);
  }

  private void sendBibRecordsToEmailWithBatchInfos(
      LMSConfiguration lmsConfiguration, List<BatchInfo> batchInfos) {
    String customerCode = lmsConfiguration.getCustomerCode();
    if (batchInfos.isEmpty()) {
      log.info("Not found any batch infos for customer {}", customerCode);
      return;
    }
    List<ExportInfo> exportInfos = getExportInfos(batchInfos);
    Map<Integer, File> marcFiles =
        exportInfos.stream()
            .collect(
                Collectors.toMap(
                    ExportInfo::getId,
                    exportInfo -> new File(fileExportDir, exportInfo.getFilename())));
    emailMarcRecordsService.sendEmail(
        lmsConfiguration.getCustomerCode(), lmsConfiguration.getUrl(), marcFiles);
    updateBatchStatus(batchInfos, BibRecordBatchStatus.SENT);
    log.info(
        "Sent email and updated {} batches status CLOSED to SENT for customer code: {}",
        batchInfos.size(),
        customerCode);
  }

  private void uploadBibRecordsToFtpFolder(
      LMSConfiguration lmsConfiguration, List<BatchInfo> batchInfos) {
    String customerCode = lmsConfiguration.getCustomerCode();
    if (batchInfos.isEmpty()) {
      log.info("Not found any batch infos for customer {}", customerCode);
      return;
    }
    Map<Integer, String> locations = getLocations(customerCode, batchInfos);
    String lmsConfigLocation = lmsConfiguration.getBatchLocation();
    if (locations.isEmpty() && StringUtils.isBlank(lmsConfigLocation)) {
      log.warn("The location configuration is not found");
      return;
    }
    List<ExportInfo> exportInfos = getExportInfos(batchInfos);
    List<Integer> uploadedBatchIds = new ArrayList<>();
    for (ExportInfo exportInfo : exportInfos) {
      String location =
          Optional.ofNullable(locations.get(exportInfo.getId())).orElse(lmsConfigLocation);
      if (location != null && moveFileToLocation(location, exportInfo.getFilename())) {
        log.info(
            "Uploading bib record file: {} to ftp folder: {} for customer: {}",
            exportInfo.getFilename(),
            location,
            customerCode);
        uploadedBatchIds.add(exportInfo.getId());
      }
    }
    updateUploadedBatchStatus(customerCode, batchInfos, uploadedBatchIds);
  }

  private void updateUploadedBatchStatus(
      String customerCode, List<BatchInfo> batchInfos, List<Integer> uploadedBatchIds) {
    List<BatchInfo> uploadedBatchInfos =
        batchInfos.stream()
            .filter(b -> uploadedBatchIds.contains(b.getId()))
            .collect(Collectors.toList());
    updateBatchStatus(uploadedBatchInfos, BibRecordBatchStatus.SENT);
    log.info(
        "Updated {} batches status CLOSED to SENT for customer code: {}",
        uploadedBatchInfos.size(),
        customerCode);
  }

  private Map<Integer, String> getLocations(String customerCode, List<BatchInfo> batchInfos) {
    List<SeparateBatchByHolding> separateBatchByHoldings =
        separateBatchByHoldingRepository.findAllByCustomerCode(customerCode).stream()
            .filter(s -> StringUtils.isNotBlank(s.getLocation()))
            .collect(Collectors.toList());
    Map<Integer, String> locations = new HashMap<>();

    for (BatchInfo batchInfo : batchInfos) {
      separateBatchByHoldings.stream()
          .filter(s -> isSeparateBatch(batchInfo.getDescription(), s.getBatchCode()))
          .findFirst()
          .ifPresent(s -> locations.put(batchInfo.getId(), s.getLocation()));
    }
    log.info("The map of batchId and location: {}", locations);
    return locations;
  }

  private List<ExportInfo> getExportInfos(List<BatchInfo> batchInfos) {
    List<Integer> batchIds = batchInfos.stream().map(BatchInfo::getId).collect(Collectors.toList());
    return batchService.export(batchIds);
  }

  private void updateBatchStatus(List<BatchInfo> batchInfos, BibRecordBatchStatus status) {
    if (CollectionUtils.isEmpty(batchInfos)) {
      return;
    }
    batchInfos.forEach(batchInfo -> batchInfo.setStatus(status));
    batchService.update(batchInfos);
  }

  private boolean moveFileToLocation(String location, String filename) {
    File fileToUpload = new File(fileExportDir, filename);
    try {
      Path targetLocation = new File(location).toPath();
      if (!Files.exists(targetLocation) || !Files.isDirectory(targetLocation)) {
        log.info("Create directory {}", location);
        Files.createDirectory(targetLocation);
      }
      Files.move(
          fileToUpload.toPath(),
          new File(location, fileToUpload.getName()).toPath(),
          REPLACE_EXISTING);
      log.info("Moved file {} to location {}", fileToUpload.getName(), location);
      return true;
    } catch (IOException e) {
      log.error("Cannot move file {} to location {}", fileToUpload.getName(), location, e);
      return false;
    }
  }

  private void uploadBibRecordsToSpydus(LMSConfiguration lmsConfiguration) {
    try {
      log.info(
          "Uploading bib records to Spydus for customer {}", lmsConfiguration.getCustomerCode());
      List<BatchInfo> batchInfos =
          getBatchInfos(lmsConfiguration.getCustomerCode(), BatchStatus.OPEN);
      if (batchInfos.isEmpty()) {
        log.info(
            "No open batches found to upload for customer {}", lmsConfiguration.getCustomerCode());
        return;
      }
      uploadBibRecordsToSpydus(lmsConfiguration, batchInfos);

    } catch (Exception e) {
      log.error(
          "Failed to upload bib records for customer code {}",
          lmsConfiguration.getCustomerCode(),
          e);
    }
  }

  private List<BatchInfo> getBatchInfos(String customerCode, BatchStatus status) {
    SearchParams searchParams =
        SearchParams.builder()
            .customerCode(customerCode)
            .status(status)
            .bibRecordType(BibRecordType.NORMAL)
            .build();
    return batchService.find(searchParams);
  }

  private void uploadBibRecordsToSpydus(
      LMSConfiguration lmsConfiguration, List<BatchInfo> batchInfos) {
    updateBatchStatus(batchInfos, BibRecordBatchStatus.CLOSED);
    List<Integer> batchIds = batchInfos.stream().map(BatchInfo::getId).collect(Collectors.toList());
    List<ExportInfo> exportInfos = batchService.export(batchIds);
    List<Integer> uploadedBatchIds = new ArrayList<>();
    for (ExportInfo exportInfo : exportInfos) {
      ProcessFileToSpydusResult processFileToSpydusResult =
          uploadAndProcessFileToSpydus(lmsConfiguration, exportInfo);
      if (processFileToSpydusResult.isSuccess()) {
        batchInfos.stream()
            .filter(batchInfo -> batchInfo.getId().equals(exportInfo.getId()))
            .forEach(
                batchInfo -> {
                  batchInfo.setStatus(BibRecordBatchStatus.SENT);
                  uploadedBatchIds.add(batchInfo.getId());
                });
      } else {
        JiraCreateTicketRequest requestBodyObject =
            JiraCreateTicketRequest.builder()
                .status(
                    processFileToSpydusResult.getFailureStatuses().entrySet().stream()
                        .collect(
                            Collectors.toMap(
                                e -> String.valueOf(e.getKey()),
                                e -> String.valueOf(e.getValue()))))
                .customerCode(lmsConfiguration.getCustomerCode())
                .batchNumber(exportInfo.getId())
                .processBatchDetails(
                    JiraProcessBatchDetail.from(processFileToSpydusResult.getResponses()))
                .build();
        jiraWebhookService.createUploadBibRecordJiraTicket(exportInfo.getId(), requestBodyObject);
      }
    }
    batchService.update(batchInfos);
    log.info(
        "Uploaded {} bib records to Spydus for customer {}",
        exportInfos.size(),
        lmsConfiguration.getCustomerCode());
    if (!uploadedBatchIds.isEmpty()) {
      log.info("Deleting on order records for batchIds: {}", batchIds);
      deleteOnOrderRecordsByBatchIds(lmsConfiguration, batchIds);
    }
  }

  private void deleteOnOrderRecordsByBatchIds(
      LMSConfiguration lmsConfiguration, List<Integer> batchIds) {
    if (lmsConfiguration.isDeleteOOR()) {
      batchIds.forEach(this::deleteOnOrderRecordsByBatchId);
    }
  }

  private ProcessFileToSpydusResult uploadAndProcessFileToSpydus(
      LMSConfiguration lmsConfiguration, ExportInfo exportInfo) {
    try {
      log.info(
          "Uploading bib record file {} to Spydus for customer {}",
          exportInfo.getFilename(),
          lmsConfiguration.getCustomerCode());

      File fileToUpload = new File(fileExportDir, exportInfo.getFilename());
      String uploadResponse =
          spydusClient.uploadBatchWithRetry(lmsConfiguration.getCustomerCode(), fileToUpload);
      ObjectMapper objectMapper = new ObjectMapper();
      UploadMarcFileResponse uploadMarcFileResponse =
          objectMapper.readValue(uploadResponse, UploadMarcFileResponse.class);

      String sessionId = uploadMarcFileResponse.getSessionId();
      spydusClient.getBatchMarcsBriefWithRetry(
          lmsConfiguration.getCustomerCode(), sessionId, exportInfo.getFilename());

      int numberOfRecords =
          spydusClient.getNumberRecordsWithRetry(lmsConfiguration.getCustomerCode(), sessionId);

      List<ProcessBatchResponse> processBatchResponses =
          spydusClient.processBatchWithRetry(
              lmsConfiguration.getCustomerCode(), sessionId, numberOfRecords, LOAD_TYPE_TEST);
      Map<String, Long> failureStatus =
          processBatchResponses.stream()
              .filter(p -> !p.isSucceed() || isNotStartWithBatchStatusSuccessful(p.getMessage()))
              .map(ProcessBatchResponse::getMessage)
              .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
      if (failureStatus.isEmpty()) {
        log.info(
            "Uploaded and processed bib record file {} to Spydus for customer {}",
            exportInfo.getFilename(),
            lmsConfiguration.getCustomerCode());
        spydusClient.processBatchWithRetry(
            lmsConfiguration.getCustomerCode(), sessionId, numberOfRecords, LOAD_TYPE_REAL);
        return ProcessFileToSpydusResult.builder()
            .success(true)
            .failureStatuses(failureStatus)
            .build();
      } else {
        log.warn(
            "Has an issue when processed bib record file {} to Spydus for customer {}",
            exportInfo.getFilename(),
            lmsConfiguration.getCustomerCode());
        return ProcessFileToSpydusResult.builder()
            .success(false)
            .failureStatuses(failureStatus)
            .responses(
                processBatchResponses.stream()
                    .filter(
                        p -> !p.isSucceed() || isNotStartWithBatchStatusSuccessful(p.getMessage()))
                    .collect(Collectors.toList()))
            .build();
      }
    } catch (Exception e) {
      log.error(
          "Cannot upload and process bib record file for customer {} and batchId {}",
          lmsConfiguration.getCustomerCode(),
          exportInfo.getId(),
          e);
      HashMap<String, Long> failureStatuses = new HashMap<>();
      failureStatuses.put(
          String.format("Cannot upload and process bib record file: %s", e.getMessage()), 1L);

      return ProcessFileToSpydusResult.builder()
          .success(false)
          .failureStatuses(failureStatuses)
          .build();
    }
  }

  private boolean isNotStartWithBatchStatusSuccessful(String message) {
    for (String success : processBatchStatusSuccessList.split(",")) {
      if (message != null && message.trim().startsWith(success.trim())) {
        return false;
      }
    }

    return true;
  }
}
