package au.com.peterpal.lucyapi.core.service.orchestration;

import au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.jms.dsl.Jms;

import javax.jms.ConnectionFactory;
import javax.jms.Session;

@Log4j2
@Configuration
@EnableIntegration
public class PartiallyInvoicedWorkOrderListenerConfig {

    private final ConnectionFactory connectionFactory;
    private final SpydusEdiOrchestratorService orchestratorService;
    private final String partiallyInvoicedWorkOrderQueueName;

    public PartiallyInvoicedWorkOrderListenerConfig(
        ConnectionFactory connectionFactory,
        SpydusEdiOrchestratorService orchestratorService,
        @Value("${events.partially-invoiced-work-order.channel:customer-invoice.lucy-api.partially-invoiced-work-order-event}")
            String partiallyInvoicedWorkOrderQueueName) {
        this.connectionFactory = connectionFactory;
        this.orchestratorService = orchestratorService;
        this.partiallyInvoicedWorkOrderQueueName = partiallyInvoicedWorkOrderQueueName;
    }

    @Bean
    public IntegrationFlow partiallyInvoicedWorkOrderListener() {
        return IntegrationFlows.from(
                Jms.messageDrivenChannelAdapter(this.connectionFactory)
                    .destination(partiallyInvoicedWorkOrderQueueName)
                    .configureListenerContainer(
                        spec ->
                            spec.sessionTransacted(true)
                                .sessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE)))
            .enrichHeaders(
                headerEnricherSpec ->
                    headerEnricherSpec.headerFunction(
                        IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID,
                        message ->
                            IncomingMessageProcessor.generateIncomingMessageId(message.getPayload()),
                        true))
            .wireTap(IncomingMessageLoggingControlFlow.LOG_INCOMING_MESSAGE_CHANNEL)
            .log(m -> "Received via JMS: " + m)
            .handle(PartiallyInvoicedWorkOrderMessage.class, (p, m) -> orchestratorService.handle(p))
            .log(message -> "handled: " + message)
            .get();
    }

}
