package au.com.peterpal.lucyapi.core.service.retrieve;

import java.io.ByteArrayInputStream;
import java.util.Enumeration;
import java.util.Optional;
import java.util.Vector;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.entity.RecordSource;
import org.apache.commons.lang.StringUtils;
import org.jzkit.search.provider.iface.IRQuery;
import org.jzkit.search.provider.iface.SearchException;
import org.jzkit.search.provider.iface.Searchable;
import org.jzkit.search.provider.z3950.SimpleZAuthenticationMethod;
import org.jzkit.search.provider.z3950.Z3950Origin;
import org.jzkit.search.provider.z3950.Z3950ServiceFactory;
import org.jzkit.search.util.RecordModel.ArchetypeRecordFormatSpecification;
import org.jzkit.search.util.RecordModel.marc.iso2709;
import org.jzkit.search.util.ResultSet.IRResultSet;
import org.jzkit.search.util.ResultSet.IRResultSetStatus;
import org.marc4j.MarcReader;
import org.marc4j.MarcStreamReader;
import org.marc4j.marc.Record;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class JzkitZ3950Client implements Z3950Client {

  private static final String USMARC_F_STR = "usmarc::F";

  private ApplicationContext context;

  private RecordSource recordSource;

  private Searchable searchable;

  private int status;

  private int resultCount;

  @SuppressWarnings("rawtypes")
  private Enumeration enumeration;

  public JzkitZ3950Client(ApplicationContext context) {
    this.context = context;
  }


  @Override
  public void init(RecordSource recordSource) throws Z3950Exception {
    Z3950ServiceFactory factory = Optional
        .ofNullable(recordSource)
        .map(source -> {
          this.recordSource = source;
          Z3950ServiceFactory f = new Z3950ServiceFactory(source.getHost(), source.getPort());
          f.setApplicationContext(context);
          f.setDefaultRecordSyntax(
              StringUtils.defaultString(source.getSyntax(), "usmarc"));
          f.setDefaultElementSetName(StringUtils.defaultString(source.getElementSet(), "F"));
          f.getRecordArchetypes().put("Default", USMARC_F_STR);
          f.getRecordArchetypes().put("FullDisplay", USMARC_F_STR);
          f.getRecordArchetypes().put("BriefDisplay", "usmarc::B");
          f.getRecordArchetypes().put("Holdings", USMARC_F_STR);

          f.setAuthMethod(new SimpleZAuthenticationMethod(3, source.getUsername(), "", source.getPassword()));
          return f;
        })
        .orElseThrow(() -> new Z3950Exception("RecordSource must not be null."));

    try {
      searchable = factory.newSearchable();
    } catch (SearchException e) {
      throw new Z3950Exception(String.format("ZClient init failed with error: %s", e.getMessage()), e);
    }
    searchable.setApplicationContext(context);
  }

  @Override
  public void query(String query) throws Z3950Exception {
    try {
      // Pause to limit the load applied to the z-server
      //if (recordSource.getPause() > 0) {
      //  sleep(recordSource.getPause());
      //}

      IRQuery irquery = new IRQuery();
      irquery.collections = new Vector();
      for (String database : StringUtils.split(recordSource.getDatabase(), ',')) {
        irquery.collections.add(database);
      }
      irquery.query = new org.jzkit.search.util.QueryModel.PrefixString.PrefixString(query);

      IRResultSet result = searchable.evaluate(irquery);

      long millis = recordSource.getTimeout();
      // Wait until result set is complete or failure
      result.waitForStatus(IRResultSetStatus.COMPLETE | IRResultSetStatus.FAILURE, millis);

      status = result.getStatus();

      if (status == IRResultSetStatus.COMPLETE) {
        resultCount = result.getFragmentCount();
        enumeration = new org.jzkit.search.util.ResultSet.ReadAheadEnumeration(result, new ArchetypeRecordFormatSpecification("Default"), 10, 25000, null);

      } else {
        throw new Z3950Exception(String.format("Search failed: %s", IRResultSetStatus.getCode(status)), query);
      }

    } catch (Exception e) {
      throw new Z3950Exception(e.getMessage(), e, query);
    }
  }

  private void sleep(int pause) {
    try {
      Thread.sleep(pause);
    } catch (InterruptedException e) {
      log.warn(() -> "Query exception: ", e);
      Thread.currentThread().interrupt();
    }
  }

  @Override
  public int getResultCount() {
    log.debug(() -> String.format("Count: %d", resultCount));
    return resultCount;
  }

  @Override
  public int getStatus() {
    log.debug(() -> String.format("Status: %d", status));
    return status;
  }

  @Override
  public boolean hasNext() throws Z3950Exception {
    if (!((Z3950Origin)searchable).connected()) {
      log.debug(() -> "Client has been disconnected....");
      throw new Z3950Exception(String.format("Client has been disconnected...."));
    }
    return enumeration.hasMoreElements();
  }

  @Override
  public Record next() {
    log.debug(() -> "Next record");
    iso2709 iso2709Record = (iso2709) enumeration.nextElement();

    if (iso2709Record != null) {
      MarcReader reader = new MarcStreamReader(new ByteArrayInputStream((byte[]) iso2709Record.getOriginalObject()));

      if (reader.hasNext()) {
        return reader.next();
      }
    }
    return null;
  }

  @Override
  public void close() {
    searchable.close();
  }

}
