package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.persistence.cataloguing.AcquisitionJdbcTemplateRepository;
import au.com.peterpal.lucyapi.persistence.cataloguing.CopyRepository;
import au.com.peterpal.lucyapi.persistence.cataloguing.model.Copy;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Service
public class CataloguingAcquisitionService {
    private final AcquisitionJdbcTemplateRepository acquisitionJdbcTemplateRepository;
    private final CopyRepository copyRepository;
    private final WorkOrderService workOrderService;

    public CataloguingAcquisitionService(AcquisitionJdbcTemplateRepository acquisitionJdbcTemplateRepository,
                                         CopyRepository copyRepository,
                                         WorkOrderService workOrderService) {
        this.acquisitionJdbcTemplateRepository = acquisitionJdbcTemplateRepository;
        this.copyRepository = copyRepository;
        this.workOrderService = workOrderService;
    }

    @Transactional
    public void removeAcquisitions(String workOrderNumber) {
        log.info("Removing acquisition for workOrderNumber: {}", workOrderNumber);
        List<Integer> acquisitionPks = acquisitionJdbcTemplateRepository.findAcquisitionPkByWorkOrderNumber(workOrderNumber);
        if (acquisitionPks.isEmpty()) {
            log.warn("Acquisition for workOrderNumber: {} not found", workOrderNumber);
            return;
        }
        List<Copy> copies = copyRepository.findAllByAcquisitionPkIn(acquisitionPks);
        List<String> barcodes = copies.stream().map(Copy::getBarcodeNumber)
            .filter(b -> !StringUtils.isEmpty(b))
            .collect(Collectors.toList());
        acquisitionJdbcTemplateRepository.removeAcquisitions(acquisitionPks);
        if (!barcodes.isEmpty()) {
            workOrderService.removeBarcodes(workOrderNumber, barcodes);
        }
    }
}
