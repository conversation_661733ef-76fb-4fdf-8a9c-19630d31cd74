package au.com.peterpal.lucyapi.core.service;

import java.util.List;
import lucy.catalogue.codes.ProductIdentifierTypeCode;
import lucy.catalogue.entity.ProductIdentifier;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.TitleAuthority;
import lucy.common.NotFoundException;
import org.springframework.stereotype.Service;

@Service
public class TitleAuthorityService {

  private final RemoteCataloguing cataloguing;

  public TitleAuthorityService(RemoteCataloguing cataloguing) {
    this.cataloguing = cataloguing;
  }

  /**
   * Find matching title authorities.
   *
   * @param productIdType
   * @param productIdValue
   * @param productIdSource
   * @return
   * @throws NotFoundException
   */
  public List<TitleAuthority> find(ProductIdentifierTypeCode productIdType,
      String productIdValue, String productIdSource) throws NotFoundException {

    ProductIdentifier productIdentifier = new ProductIdentifier(productIdType, productIdValue, productIdSource);
    return cataloguing.findMatchingTitleAuthorities(productIdentifier);
  }

}
