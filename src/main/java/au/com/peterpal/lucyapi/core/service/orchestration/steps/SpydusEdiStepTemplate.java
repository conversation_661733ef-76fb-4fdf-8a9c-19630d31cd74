package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.core.service.JiraWebhookService;
import au.com.peterpal.lucyapi.core.service.orchestration.WorkflowException;
import au.com.peterpal.lucyapi.core.service.orchestration.WorkflowStep;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.WorkflowStepStatus;
import au.com.peterpal.lucyapi.persistence.lucyapi.SpydusEdiOrchestrationRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestrationCompletedStep;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;

@Log4j2
public abstract class SpydusEdiStepTemplate implements WorkflowStep {

  private SpydusEdiOrchestrationRepository spydusEdiOrchestrationRepository;
  private JiraWebhookService jiraWebhookService;

  @Autowired
  public void setSpydusEdiAccessioningRepository(
      SpydusEdiOrchestrationRepository spydusEdiOrchestrationRepository) {
    this.spydusEdiOrchestrationRepository = spydusEdiOrchestrationRepository;
  }

  @Autowired
  public void setJiraWebhookService(JiraWebhookService jiraWebhookService) {
    this.jiraWebhookService = jiraWebhookService;
  }

  @Retryable(
      value = WorkflowException.class,
      maxAttemptsExpression = "${retry.spydus-edi.maxAttempts:20}",
      backoff = @Backoff(delayExpression = "${retry.spydus-edi.maxDelay:60000}"))
  public boolean process(SpydusEdiOrchestration spydusEdiOrchestration) {
    Optional<SpydusEdiOrchestration> existingOrchestrationOpt =
        spydusEdiOrchestrationRepository.findById(spydusEdiOrchestration.getId());

    if (existingOrchestrationOpt.isPresent()
        && existingOrchestrationOpt.get().getWorkflowStepStatus() == WorkflowStepStatus.STOPPED) {
      return false;
    }
    String stepName = this.getClass().getName();
    log.info("Starting to process step {}", stepName);
    if (spydusEdiOrchestration.getCompletedSteps().stream()
        .map(SpydusEdiOrchestrationCompletedStep::getCompletedStep)
        .anyMatch(s -> s.equals(stepName))) {
      log.info("Step {} has already completed", stepName);
      return true;
    }
    try {
      ProcessingResult processingResult = doProcess(spydusEdiOrchestration);
      if (processingResult.isSuccess()) {
        handleSuccess(spydusEdiOrchestration, stepName);
      } else {
        handleFailure(spydusEdiOrchestration, processingResult.getErrorMessage());
      }
      log.info(
          "Completed to process step {} with success is {}",
          stepName,
          processingResult.isSuccess());
      return processingResult.isSuccess();
    } catch (WorkflowException workflowException) {
      throw workflowException;
    } catch (Exception e) {
      log.error("Failed to process spydus edi accessioning automation", e);
      handleFailure(spydusEdiOrchestration, e.getMessage());
      return false;
    }
  }

  @Override
  public void handleFailure(SpydusEdiOrchestration spydusEdiOrchestration, String errorMessage) {
    String jiraWebhookUrl = getJiraWebhookUrl();
    String jiraWebhookSecret = getJiraWebhookSecret();
    Map<String, Object> jiraMetadata = getJiraMetadata(spydusEdiOrchestration);
    jiraMetadata.put("errorMessage", errorMessage);
    jiraMetadata.put("status", "FAILURE");
    jiraWebhookService.createJiraTicket(jiraMetadata, jiraWebhookUrl, jiraWebhookSecret);
  }

  private Map<String, Object> getJiraMetadata(SpydusEdiOrchestration spydusEdiOrchestration) {
    Map<String, Object> jiraMetadata = new HashMap<>();
    String stepName = this.getClass().getName();
    jiraMetadata.put("spydusOrchestrationId", spydusEdiOrchestration.getId());
    jiraMetadata.put("invoiceGroupId", spydusEdiOrchestration.getInvoiceGroupId());
    jiraMetadata.put("customerCode", spydusEdiOrchestration.getCustomerCode());
    jiraMetadata.put("step", stepName);
    return jiraMetadata;
  }

  private void handleSuccess(SpydusEdiOrchestration spydusEdiOrchestration, String stepName) {
    String jiraWebhookUrl = getJiraWebhookUrl();
    String jiraWebhookSecret = getJiraWebhookSecret();
    Map<String, Object> jiraMetadata = getJiraMetadata(spydusEdiOrchestration);
    spydusEdiOrchestration
        .getCompletedSteps()
        .add(SpydusEdiOrchestrationCompletedStep.from(spydusEdiOrchestration, stepName));
    jiraMetadata.put("status", "SUCCESS");
    jiraWebhookService.createJiraTicket(jiraMetadata, jiraWebhookUrl, jiraWebhookSecret);
    spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
  }

  protected abstract ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration);

  protected abstract String getJiraWebhookUrl();

  protected abstract String getJiraWebhookSecret();

  protected PartiallyInvoicedWorkOrderMessage getPartiallyInvoicedMessage(
      SpydusEdiOrchestration spydusEdiOrchestration) {
    String bodyMessage = spydusEdiOrchestration.getBodyMessage();
    return PartiallyInvoicedWorkOrderMessage.from(bodyMessage);
  }
}
