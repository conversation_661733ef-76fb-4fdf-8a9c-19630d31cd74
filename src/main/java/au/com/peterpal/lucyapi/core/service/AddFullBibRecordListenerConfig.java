package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.model.AddFullBibRecordMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.jms.dsl.Jms;

import javax.jms.ConnectionFactory;
import javax.jms.Session;

@Log4j2
@Configuration
@EnableIntegration
public class AddFullBibRecordListenerConfig {

    private final ConnectionFactory connectionFactory;
    private final HoldingService holdingService;
    private final String addFullBibRecordEvents;

    public AddFullBibRecordListenerConfig(
        ConnectionFactory connectionFactory,
        HoldingService holdingService,
        @Value("${events.add-full-bib-record.channel:customer-invoice.lucy-api.add-full-bib-record-event}")
            String addFullBibRecordEvents) {
        this.connectionFactory = connectionFactory;
        this.holdingService = holdingService;
        this.addFullBibRecordEvents = addFullBibRecordEvents;
    }

    @Bean
    public IntegrationFlow addFullBibRecordListener() {
        return IntegrationFlows.from(
                Jms.messageDrivenChannelAdapter(this.connectionFactory)
                    .destination(addFullBibRecordEvents)
                    .configureListenerContainer(
                        spec ->
                            spec.sessionTransacted(true)
                                .sessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE)))
            .enrichHeaders(
                headerEnricherSpec ->
                    headerEnricherSpec.headerFunction(
                        IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID,
                        message ->
                            IncomingMessageProcessor.generateIncomingMessageId(message.getPayload()),
                        true))
            .wireTap(IncomingMessageLoggingControlFlow.LOG_INCOMING_MESSAGE_CHANNEL)
            .log(m -> "Received via JMS: " + m)
            .handle(AddFullBibRecordMessage.class, (p, m) -> holdingService.addFullBibRecord(p))
            .log(message -> "handled: " + message)
            .get();
    }

}
