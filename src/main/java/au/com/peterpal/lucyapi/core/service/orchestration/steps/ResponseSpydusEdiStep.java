package au.com.peterpal.lucyapi.core.service.orchestration.steps;

import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

@Service
@RequiredArgsConstructor
@Log4j2
public class ResponseSpydusEdiStep extends SpydusEdiStepTemplate {

  @Value(
      "${jira.response-spydus-edi.webhook.url:"
          + "https://api-private.atlassian.com/automation/webhooks/jira/a/************************63897cab3269/018c3cf3-085e-7108-a840-b6af2e0b58b8}")
  private String jiraWebhookUrl;

  @Value(
      "${jira.response-spydus-edi.webhook.secret:"
          + "633c677e553f1b153b02b5785957b60218eeb5fb}")
  private String jiraWebhookSecret;

  @Value("${response-spydus-edi.url}")
  private String responseSpydusEdiUrl;

  @Value("${spydus-edi-orchestrator.skip-spydus-edi-step-in-test-env: false}")
  private boolean skipStepInTest;

  @Value("${spydus-edi-orchestrator.webclient.timeout.seconds:10}")
  private int timeoutSeconds;

  @Override
  protected ProcessingResult doProcess(SpydusEdiOrchestration spydusEdiOrchestration) {

    String customerCode = spydusEdiOrchestration.getCustomerCode();
    if (skipStepInTest) {
      log.info("Skip this step in test environemnt");
    } else {
      retrieveResponses(customerCode);
    }
    return ProcessingResult.of(true, null);
  }

  private void retrieveResponses(String customerCode) {
    WebClient.builder()
        .baseUrl(responseSpydusEdiUrl)
        .defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        .build()
        .method(HttpMethod.POST)
        .uri(String.format("/SpydusEDI/responses?customer=%s", customerCode))
        .retrieve()
        .bodyToMono(String.class)
        .timeout(Duration.ofSeconds(timeoutSeconds))
        .onErrorReturn("Reached to timeout! Checking all response are valid in next steps")
        .blockOptional()
        .ifPresent(
            response ->
                log.info(
                    "Response of responses spydus edi for customer code {} : {}",
                    customerCode,
                    response));
  }

  @Override
  protected String getJiraWebhookUrl() {
    return jiraWebhookUrl;
  }

  @Override
  protected String getJiraWebhookSecret() {
    return jiraWebhookSecret;
  }
}
