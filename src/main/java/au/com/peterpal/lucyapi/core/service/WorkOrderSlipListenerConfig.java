package au.com.peterpal.lucyapi.core.service;

import au.com.peterpal.lucyapi.core.service.message.IncomingMessageLoggingControlFlow;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.model.WorkOrderSlipMessage;
import javax.jms.ConnectionFactory;
import javax.jms.Session;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.jms.dsl.Jms;

@Log4j2
@Configuration
@EnableIntegration
public class WorkOrderSlipListenerConfig {

  private final ConnectionFactory connectionFactory;
  private final WorkOrderService workOrderService;

  private final String workOrderSlipQueueName;

  public WorkOrderSlipListenerConfig(ConnectionFactory connectionFactory,
      WorkOrderService workOrderService,
      @Value("${work-order.slip.queue.name:receiving-lucy-api.work-order-slip}")
      String workOrderSlipQueueName) {
    this.connectionFactory = connectionFactory;
    this.workOrderService = workOrderService;
    this.workOrderSlipQueueName = workOrderSlipQueueName;
  }

  @Bean
  public IntegrationFlow workOrderSlipSlipJmsListener() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(this.connectionFactory)
                .destination(workOrderSlipQueueName)
                .configureListenerContainer(
                    spec ->
                        spec.sessionTransacted(true)
                            .sessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE)))
        .enrichHeaders(
            headerEnricherSpec ->
                headerEnricherSpec.headerFunction(
                    IncomingMessageLoggingControlFlow.INCOMING_MESSAGE_ID,
                    message ->
                        IncomingMessageProcessor.generateIncomingMessageId(message.getPayload()),
                    true))
        .wireTap(IncomingMessageLoggingControlFlow.LOG_INCOMING_MESSAGE_CHANNEL)
        .log(m -> "Received via JMS: " + m)
        .transform(Transformers.fromJson(WorkOrderSlipMessage.class))
        .handle(WorkOrderSlipMessage.class, (p, m) -> workOrderService.printWorkOrderSlip(p))
        .log(message -> "handled: " + message)
        .get();
  }
}
