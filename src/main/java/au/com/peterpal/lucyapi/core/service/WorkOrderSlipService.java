package au.com.peterpal.lucyapi.core.service;

import static au.com.peterpal.lucyapi.core.service.PrinterService.POST_REQUEST_FOOTER;
import static au.com.peterpal.lucyapi.core.service.PrinterService.POST_REQUEST_HEADER;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.model.WorkOrderSlipMessage;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Log4j2
@RequiredArgsConstructor
public class WorkOrderSlipService {

  private final RestTemplate restTemplate;

  public void print(String printer, String printJobId, String xmlBody) {
    try {
      String postRequest =
          String.format(POST_REQUEST_HEADER, printJobId) + xmlBody + POST_REQUEST_FOOTER;

      log.info(() -> String.format("Post request for receipt printer is: %s", postRequest));
      ResponseEntity<String> response =
          restTemplate.postForEntity(
              String.format("http://%s/cgi-bin/epos/service.cgi", printer),
              postRequest,
              String.class);

      log.info(() -> String.format("Response form receipt printer is: %s", response));
    } catch (Exception e) {
      String message = String.format("Receipt Printer %s cannot be contacted", printer);
      log.error(message, e);
      throw new BusinessException(message);
    }
  }

  public String printWorkOrderSlip(
      String templateName, WorkOrderSlipMessage workOrderSlipMessage, LocalDate printedDate) {
    try {
      String template =
          readXMLTemplate(
              templateName,
              StringUtils.equalsIgnoreCase(
                  workOrderSlipMessage.getIsbnReceived(), workOrderSlipMessage.getIsbnOrdered()));

      Map<String, String> valuesMap = new HashMap<>();
      valuesMap.put(
          "barcode", StringEscapeUtils.escapeXml11(workOrderSlipMessage.getWorkOrderNumber()));
      valuesMap.put(
          "work_order_number",
          StringEscapeUtils.escapeXml11(workOrderSlipMessage.getWorkOrderNumber()));
      valuesMap.put("printed_date", printedDate.format(DateTimeFormatter.ofPattern("d/M/yyyy")));
      valuesMap.put(
          "title_order_number",
          StringEscapeUtils.escapeXml11(workOrderSlipMessage.getTitleOrderNumber()));
      valuesMap.put(
          "title_order_date",
          Optional.ofNullable(workOrderSlipMessage.getTitleOrderDate())
              .map(date -> date.format(DateTimeFormatter.ofPattern("d/M/yyyy")))
              .orElse(""));
      valuesMap.put(
          "title",
          StringUtils.defaultString(
              StringEscapeUtils.escapeXml11(workOrderSlipMessage.getTitle()), ""));
      valuesMap.put(
          "author",
          StringUtils.defaultString(
              StringEscapeUtils.escapeXml11(workOrderSlipMessage.getAuthor()), ""));
      valuesMap.put("isbn_received", workOrderSlipMessage.getIsbnReceived());
      valuesMap.put("isbn_ordered", workOrderSlipMessage.getIsbnOrdered());
      valuesMap.put(
          "customer_reference",
          StringEscapeUtils.escapeXml11(
              StringUtils.defaultString(workOrderSlipMessage.getCustomerReference(), "")));

      int paddingSize =
          (20 - StringEscapeUtils.escapeXml11(workOrderSlipMessage.getCustomerCode()).length()) / 2;
      String paddingSpaces = StringUtils.repeat(" ", paddingSize);

      valuesMap.put(
          "customer_code",
          (paddingSpaces + StringEscapeUtils.escapeXml11(workOrderSlipMessage.getCustomerCode()))
              + paddingSpaces);
      valuesMap.put("part_supply", workOrderSlipMessage.isPartSupply() ? "(Part Supply)" : "");
      valuesMap.put("received_total", String.valueOf(workOrderSlipMessage.getReceivedTotal()));
      valuesMap.put("ordered_total", String.valueOf(workOrderSlipMessage.getOrderedTotal()));
      valuesMap.put("fund_code", Objects.toString(workOrderSlipMessage.getFundCode(), ""));
      valuesMap.put(
          "delivery_instructions",
          StringEscapeUtils.escapeXml11(
              StringUtils.defaultString(workOrderSlipMessage.getDeliveryInstruction(), "")));

      if (StringUtils.isNotBlank(workOrderSlipMessage.getOrderType())) {
        valuesMap.put(
            "order_type",
            "********** " + String.valueOf(workOrderSlipMessage.getOrderType()) + " **********");
      } else {
        valuesMap.put("order_type", "");
      }

      StringSubstitutor substitutor = new StringSubstitutor(valuesMap);
      return substitutor.replace(template);

    } catch (Exception e) {
      throw new RuntimeException("Error reading or processing the work order slip template", e);
    }
  }

  public static String readXMLTemplate(String template, boolean excludeIsbnOrdered) throws IOException {
    ClassPathResource resource =
        new ClassPathResource(template);

    StringBuilder contentBuilder = new StringBuilder();
    boolean skip = false;

    try (BufferedReader reader =
        new BufferedReader(
            new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {

      String line;
      while ((line = reader.readLine()) != null) {
        if (line.contains("<isbn-ordered-block>")) {
          if (excludeIsbnOrdered) {
            skip = true;
          }
          continue;
        }
        if (line.contains("</isbn-ordered-block>")) {
          skip = false;
          continue;
        }
        if (!skip) {
          contentBuilder.append(line).append("\n");
        }
      }
    }

    return contentBuilder.toString();
  }
}
