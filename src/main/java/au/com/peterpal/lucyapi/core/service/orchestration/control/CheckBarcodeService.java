package au.com.peterpal.lucyapi.core.service.orchestration.control;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.batch.control.BatchService;
import au.com.peterpal.lucyapi.batch.model.BatchInfo;
import au.com.peterpal.lucyapi.core.service.*;
import au.com.peterpal.lucyapi.core.service.orchestration.WorkflowException;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.model.*;
import au.com.peterpal.lucyapi.persistence.cataloguing.WorkOrderBarcodeJdbcTemplateRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.SpydusEdiOrchestrationRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.BatchItem;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.Acquisition;
import lucy.cataloguing.entity.BibRecord;
import lucy.cataloguing.entity.Copy;
import lucy.common.NotFoundException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@RequiredArgsConstructor
public class CheckBarcodeService {
  private final SpydusEdiOrchestrationRepository spydusEdiOrchestrationRepository;
  private final BatchItemService batchItemService;
  private final WorkOrderService workOrderService;
  private final WorkOrderBarcodeJdbcTemplateRepository workOrderBarcodeJdbcTemplateRepository;
  private final HoldingService holdingService;
  private final BatchService batchService;
  private final RemoteCataloguing remoteCataloguing;
  private final TitleOrderService titleOrderService;
  private final CustomerInvoiceService customerInvoiceService;
  @Value("#{'${marc.barcode-line-prefix}'.split(',')}")
  private List<String> barcodeLinePrefixes;

  public ProcessingResult checkBarcodes(
      UUID orchestrationId, Set<String> invoiceNumbers, Set<Integer> batchIds) {
    if (Objects.nonNull(orchestrationId)) {
      SpydusEdiOrchestration spydusEdiOrchestration =
          spydusEdiOrchestrationRepository
              .findById(orchestrationId)
              .orElseThrow(
                  () ->
                      new ResourceNotFoundException(
                          SpydusEdiOrchestration.class, orchestrationId.toString()));
      return checkBarcodes(spydusEdiOrchestration);
    } else {
      try {
        return checkBarcodes(invoiceNumbers, batchIds);
      } catch (Exception e) {
        String errorMsg =
            String.format(
                "Failed to check work order and barcode for Spydus EDI Orchestration with invoiceNumbers: %s and batchIds %s",
                invoiceNumbers, batchIds);
        log.warn(errorMsg, e);
        throw new WorkflowException(errorMsg);
      }
    }
  }

  public ProcessingResult checkBarcodes(SpydusEdiOrchestration spydusEdiOrchestration) {
    PartiallyInvoicedWorkOrderMessage partiallyInvoicedMessage =
        PartiallyInvoicedWorkOrderMessage.from(spydusEdiOrchestration.getBodyMessage());

    try {
      Set<String> invoiceNumbers =
          partiallyInvoicedMessage.getInvoices().stream()
              .map(PartiallyInvoicedWorkOrderMessage.Invoice::getInvoiceNumber)
              .collect(Collectors.toSet());
      Set<UUID> invoiceIds =
          partiallyInvoicedMessage.getInvoices().stream()
              .map(PartiallyInvoicedWorkOrderMessage.Invoice::getInvoiceId)
              .collect(Collectors.toSet());
      Set<Integer> batchIds = batchItemService.getAllBatchIds(invoiceIds);
      return checkBarcodes(invoiceNumbers, batchIds);
    } catch (Exception e) {
      String errorMsg =
          String.format(
              "Failed to check work order and barcode for Spydus EDI Orchestration with ID: %s. Error details: %s",
              spydusEdiOrchestration.getId(), e.getMessage());
      log.warn(errorMsg, e);
      throw new WorkflowException(errorMsg);
    }
  }

  private ProcessingResult checkBarcodes(Set<String> invoiceNumbers, Set<Integer> batchIds) {
    List<WorkOrderBarcode> workOrderBarcodesFromWorkOrderService =
        getWorkOrderBarcodesFromWorkOrderService(invoiceNumbers).stream()
            .map(
                w ->
                    WorkOrderBarcode.builder()
                        .barcode(w.getBarcode())
                        .workOrderNumber(w.getWorkOrderNumber())
                        .build())
            .collect(Collectors.toList());
    if (workOrderBarcodesFromWorkOrderService.isEmpty()) {
      throw new BusinessException(
          String.format(
              "not found barcodes from work order service for invoices %s and batches %s",
              invoiceNumbers, batchIds));
    }

    List<WorkOrderBarcode> workOrderBarcodeFromLucyCataloguing =
        getWorkOrderBarcodeFromLucyCataloguing(batchIds);
    if (CollectionUtils.isEqualCollection(
        workOrderBarcodesFromWorkOrderService, workOrderBarcodeFromLucyCataloguing)) {
      return ProcessingResult.of(true, null);
    } else {
      String missingBarcodeMessage =
          getMissingBarcodeMessage(
              workOrderBarcodesFromWorkOrderService, workOrderBarcodeFromLucyCataloguing);
      log.warn(missingBarcodeMessage);
      return ProcessingResult.of(
          false,
          String.format(
              "The items do not match between the work order service and Lucy cataloguing. %s",
              missingBarcodeMessage));
    }
  }

  private List<WorkOrderBarcode> getWorkOrderBarcodeFromLucyCataloguing(Set<Integer> batchIds) {
    return batchIds.isEmpty()
        ? new ArrayList<>()
        : workOrderBarcodeJdbcTemplateRepository.findBarcodeByBatchIds(batchIds);
  }

  private List<WorkOrderBarcodeResponse> getWorkOrderBarcodesFromWorkOrderService(
      Set<String> invoiceNumbers) {
    try {
      return workOrderService.findItemsByInvoiceNumbers(invoiceNumbers).stream()
          .filter(
              workOrderBarcodeResponse -> Objects.nonNull(workOrderBarcodeResponse.getBarcode()))
          .map(
              w ->
                  WorkOrderBarcodeResponse.builder()
                      .barcode(w.getBarcode())
                      .workOrderNumber(w.getWorkOrderNumber())
                      .invoiceNumber(w.getInvoiceNumber())
                      .build())
          .collect(Collectors.toList());
    } catch (IOException e) {
      log.warn("Can't get barcode from work order service", e);
      throw new RuntimeException(e);
    }
  }

  private static String getMissingBarcodeMessage(
      List<WorkOrderBarcode> workOrderBarcodesFromWorkOrderService,
      List<WorkOrderBarcode> workOrderBarcodeFromLucyCataloguing) {
    Collection missingInService =
        CollectionUtils.subtract(
            workOrderBarcodeFromLucyCataloguing, workOrderBarcodesFromWorkOrderService);
    Collection missingInLucy =
        CollectionUtils.subtract(
            workOrderBarcodesFromWorkOrderService, workOrderBarcodeFromLucyCataloguing);
    StringBuilder missingMessage = new StringBuilder();
    if (!missingInService.isEmpty()) {
      String message =
          String.format(
              "Barcodes missing in work order service but present in Lucy cataloguing: %s.",
              missingInService);
      missingMessage.append(message);
    }

    if (!missingInLucy.isEmpty()) {
      String message =
          String.format(
              "Barcodes missing in Lucy cataloguing but present in work order service: %s.",
              missingInLucy);
      if (missingMessage.length() > 0) {
        missingMessage.append(" ");
      }
      missingMessage.append(message);
    }
    return missingMessage.toString().trim();
  }

  @Transactional
  public String fixBarcodesMismatch(UUID orchestrationId) {
    Set<String> invoiceNumbers = getInvoiceNumbers(orchestrationId);
    log.info("Fixing barcodes mismatch for invoiceNumbers {}", invoiceNumbers);
    Map<UUID, String> invoiceIds = getInvoiceIds(invoiceNumbers);
    Set<Integer> batchIds = batchItemService.getAllBatchIds(invoiceIds.keySet());

    List<WorkOrderBarcodeResponse> workOrderBarcodeResponses =
        getWorkOrderBarcodesFromWorkOrderService(invoiceNumbers);
    if (CollectionUtils.isEmpty(workOrderBarcodeResponses)) {
      throw new BusinessException("Work order barcodes in work order service not found");
    }
    List<WorkOrderBarcode> workOrderBarcodesFromWorkOrderService =
        convertToWorkOrderBarcode(workOrderBarcodeResponses);
    List<WorkOrderBarcode> workOrderBarcodeFromLucyCataloguing =
        getWorkOrderBarcodeFromLucyCataloguing(batchIds);
    if (CollectionUtils.isEqualCollection(
        workOrderBarcodesFromWorkOrderService, workOrderBarcodeFromLucyCataloguing)) {
      return "No fixed barcodes applied due to barcode matching";
    }
    Collection<WorkOrderBarcode> missingInService =
        CollectionUtils.subtract(
            workOrderBarcodeFromLucyCataloguing, workOrderBarcodesFromWorkOrderService);
    Collection<WorkOrderBarcode> missingInLucy =
        CollectionUtils.subtract(
            workOrderBarcodesFromWorkOrderService, workOrderBarcodeFromLucyCataloguing);
    Map<String, UUID> barcodesInvoiceMaps =
        getBarcodesInvoiceMaps(invoiceIds, workOrderBarcodeResponses);
    Set<String> includedBarcodes = fixMissingInLucy(missingInLucy, barcodesInvoiceMaps, batchIds);
    Set<String> removedBarcodes =
        fixMissingInService(missingInService, batchIds, invoiceIds.keySet());

    String message =
        String.format(
            "Fixed barcodes by added Barcodes in Lucy cataloguing: %s and removed Barcodes in work order service: %s.",
            includedBarcodes, removedBarcodes);
    log.info(message);
    return message;
  }

  private Set<String> getInvoiceNumbers(UUID orchestrationId) {
    SpydusEdiOrchestration spydusEdiOrchestration =
        spydusEdiOrchestrationRepository
            .findById(orchestrationId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        SpydusEdiOrchestration.class, orchestrationId.toString()));
    PartiallyInvoicedWorkOrderMessage partiallyInvoicedMessage =
        PartiallyInvoicedWorkOrderMessage.from(spydusEdiOrchestration.getBodyMessage());
    return partiallyInvoicedMessage.getInvoices().stream()
        .map(PartiallyInvoicedWorkOrderMessage.Invoice::getInvoiceNumber)
        .collect(Collectors.toSet());
  }

  private Map<String, UUID> getBarcodesInvoiceMaps(
      Map<UUID, String> invoiceIds, List<WorkOrderBarcodeResponse> workOrderBarcodeResponses) {

    return workOrderBarcodeResponses.stream()
        .collect(
            Collectors.toMap(
                WorkOrderBarcodeResponse::getBarcode,
                response -> getInvoiceIdByNumber(invoiceIds, response.getInvoiceNumber())));
  }

  private UUID getInvoiceIdByNumber(Map<UUID, String> invoiceIds, String invoiceNumber) {
    return invoiceIds.entrySet().stream()
        .filter(entry -> entry.getValue().equals(invoiceNumber))
        .map(Map.Entry::getKey)
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalArgumentException("No matching invoice ID found for: " + invoiceNumber));
  }

  private Map<UUID, String> getInvoiceIds(Set<String> invoiceNumbers) {
    return invoiceNumbers.stream()
        .collect(
            Collectors.toMap(
                invoiceNumber ->
                    UUID.fromString(
                        customerInvoiceService
                            .findInvoiceByInvoiceNumber(invoiceNumber)
                            .getInvoiceId()),
                invoiceNumber -> invoiceNumber));
  }

  private List<WorkOrderBarcode> convertToWorkOrderBarcode(
      List<WorkOrderBarcodeResponse> responses) {
    return responses.stream()
        .map(w -> new WorkOrderBarcode(w.getWorkOrderNumber(), w.getBarcode(), null))
        .collect(Collectors.toList());
  }

  private Set<String> fixMissingInLucy(
      Collection<WorkOrderBarcode> missingInLucy,
      Map<String, UUID> barcodesInvoiceMaps,
      Set<Integer> batchIds) {
    if (missingInLucy.isEmpty()) return Collections.emptySet();

    Map<String, List<String>> missingInLucyMap =
        missingInLucy.stream()
            .collect(
                Collectors.groupingBy(
                    WorkOrderBarcode::getWorkOrderNumber,
                    Collectors.mapping(WorkOrderBarcode::getBarcode, Collectors.toList())));

    Set<String> includesBarcodes = new HashSet<>();
    for (Map.Entry<String, List<String>> workOrder : missingInLucyMap.entrySet()) {
      try {
        Set<String> barcodes =
            fixMissingInLucy(
                workOrder.getKey(), workOrder.getValue(), barcodesInvoiceMaps, batchIds);
        includesBarcodes.addAll(barcodes);

      } catch (Exception e) {
        log.error("Can't fix barcodes in Lucy cataloguing for workOrder {}", workOrder.getKey(), e);
      }
    }

    return includesBarcodes;
  }

  private Set<String> fixMissingInLucy(
      String workOrderNumber, List<String> barcodes, Map<String, UUID> barcodesInvoiceMaps, Set<Integer> batchIds) {
    TitleOrderInfo titleOrder = titleOrderService.getTitleOrder(workOrderNumber ,true);

    FullBibRecordCreatedResult fullBibRecordWithResult =
        holdingService.createFullBibRecord(workOrderNumber, true, titleOrder, true, batchIds);

    Set<String> fullBarCodes =
        fullBibRecordWithResult.getAcquisitions().stream()
            .map(Acquisition::getCopies)
            .flatMap(Collection::stream)
            .map(Copy::getBarCodeNumber)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

    Set<String> includedBarcodes = new HashSet<>(fullBarCodes);
    Set<String> notIncludedBarcodes =
        fullBarCodes.stream()
            .filter(fullBarCode -> !barcodes.contains(fullBarCode))
            .collect(Collectors.toSet());
    includedBarcodes.removeAll(notIncludedBarcodes);

    if (!notIncludedBarcodes.isEmpty()) {
      try {
        holdingService.modifyBibRecords(
            notIncludedBarcodes, fullBibRecordWithResult.getBibRecord().getPk());
        log.info(
            String.format("Update BibRecord :%s ", fullBibRecordWithResult.getBibRecord().getPk()));
      } catch (NotFoundException e) {
        log.warn("Can't modify bib records", e);
        throw new RuntimeException(e);
      }
    }

    barcodes.forEach(
        barcode ->
            batchItemService.storeBarcode(
                workOrderNumber,
                fullBibRecordWithResult.getBatchId(),
                barcode,
                false,
                null,
                barcodesInvoiceMaps.get(barcode)));

    return includedBarcodes;
  }

  private Set<String> fixMissingInService(
      Collection<WorkOrderBarcode> missingInService, Set<Integer> batchIds, Set<UUID> invoiceIds) {
    if (missingInService.isEmpty()) return Collections.emptySet();
    Set<String> workOrders =
        missingInService.stream()
            .map(WorkOrderBarcode::getWorkOrderNumber)
            .collect(Collectors.toSet());
    Set<String> notIncludedBarcodes =
        missingInService.stream().map(WorkOrderBarcode::getBarcode).collect(Collectors.toSet());
    Set<Integer> batchPks = missingInService.stream().map(WorkOrderBarcode::getBatchId).collect(Collectors.toSet());
    Set<String> removedBarcodes = new HashSet<>();

    for (Integer batchPk : batchPks) {
      BatchInfo batchInfo = batchService.getBatch(batchPk);
      for (Integer bibRecordPk : batchInfo.getBibRecordIdList()) {
        try {
          BibRecord bibRecord = remoteCataloguing.getBibRecord(bibRecordPk);
          Set<String> containedBarCodesLines =
              holdingService.containBarCodesLines(bibRecord, notIncludedBarcodes);
          if (CollectionUtils.isNotEmpty(containedBarCodesLines)) {
            BibRecord updated = holdingService.removeBarCodesLines(bibRecord, notIncludedBarcodes);
            if (isEmptyBarcodes(updated)) {
              log.debug("Removed BibRecord {}", bibRecordPk);
              remoteCataloguing.removeBibRecord(updated.getPk());
            } else {
              log.debug("Updated BibRecords {} with remove barcodes {}", bibRecordPk, notIncludedBarcodes);
              remoteCataloguing.updateBibRecord(updated);
            }
            removedBarcodes.addAll(containedBarCodesLines);
          }
        } catch (NotFoundException e) {
          log.warn("Can't modify bib records", e);
          throw new RuntimeException(e);
        }
      }
    }
    removedBarcodes.forEach(
        removedBarcode ->
            batchItemService.deleteByBarcodeAndInvoiceIdIn(removedBarcode, invoiceIds));
    return removedBarcodes;
  }

  public boolean isEmptyBarcodes(BibRecord bibRecord) {
    String marcText = bibRecord.getMarcText();
    String[] lines = marcText.split("\n");

      for (String line : lines) {
      for (String prefix : barcodeLinePrefixes) {
        if (line.startsWith(prefix)) {
          return false;
        }
      }
    }

    return true;
  }

  private static String getInvoiceId(
      List<WorkOrderBarcodeResponse> workOrderBarcodeResponses, String workOrderNumber) {
    return String.valueOf(
        workOrderBarcodeResponses.stream()
            .filter(w -> w.getWorkOrderNumber().equals(workOrderNumber))
            .map(WorkOrderBarcodeResponse::getInvoiceNumber)
            .findAny());
  }
}
