package au.com.peterpal.lucyapi.core.service.orchestration;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.WorkflowStepStatus;
import au.com.peterpal.lucyapi.core.service.orchestration.steps.*;
import au.com.peterpal.lucyapi.persistence.lucy.Lucy4InvoiceRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.LMSConfigurationRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.SpydusEdiOrchestrationRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.LMSConfiguration;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestration;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.SpydusEdiOrchestrationCompletedStep;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.persistence.EntityNotFoundException;
import javax.transaction.Transactional;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Log4j2
@Transactional
public class SpydusEdiOrchestratorService {

  private final InitializationStep initializationStep;
  private final CheckWorkOrderBarcodeStep checkWorkOrderBarcodeStep;
  private final CustomerInvoiceEdiRetrievalStep customerInvoiceEdiRetrievalStep;
  private final CustomerOrderResponsesEdiRetrievalStep customerOrderResponsesEdiRetrievalStep;
  private final InvoiceSpydusEdiStep invoiceSpydusEdiStep;
  private final CheckInvoiceSpydusEdiStep checkInvoiceSpydusEdiStep;
  private final ResponseSpydusEdiStep responseSpydusEdiStep;
  private final CheckResponseSpydusEdiStep checkResponseSpydusEdiStep;
  private final UploadFullBibRecordStep uploadFullBibRecordStep;

  private final SpydusEdiOrchestrationRepository spydusEdiOrchestrationRepository;
  private final LMSConfigurationRepository lmsConfigurationRepository;
  private final Lucy4InvoiceRepository lucy4InvoiceRepository;

  @Transactional
  public PartiallyInvoicedWorkOrderMessage handle(PartiallyInvoicedWorkOrderMessage message) {

    triggerSpydusEdiOrchestration(message);
    return message;
  }

  @Transactional
  public Optional<UUID> triggerSpydusEdiOrchestration(PartiallyInvoicedWorkOrderMessage message) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      SpydusEdiOrchestration spydusEdiOrchestration =
          SpydusEdiOrchestration.builder()
              .bodyMessage(objectMapper.writeValueAsString(message))
              .id(UUID.randomUUID())
              .invoiceGroupId(message.getInvoiceGroupId())
              .customerCode(message.getCustomerCode())
              .retryCount(0)
              .createdDate(LocalDateTime.now())
              .build();
      spydusEdiOrchestration.setWorkflowStepStatus(WorkflowStepStatus.IN_PROGRESS);
      boolean success = initializeOrchestration(spydusEdiOrchestration);
      if (success) {
        return Optional.of(spydusEdiOrchestration.getId());
      }

    } catch (Exception e) {
      log.error("Cannot process spydus edi accessioning automation for message {}", message, e);
    }
    return Optional.empty();
  }

  private boolean initializeOrchestration(SpydusEdiOrchestration spydusEdiOrchestration) {
    Boolean spydusEdi =
        lmsConfigurationRepository
            .findById(spydusEdiOrchestration.getCustomerCode())
            .map(LMSConfiguration::getSpydusEDI)
            .orElse(false);
    if (!spydusEdi) {
      log.info("The spydusEdi flag is off. No process spydus edi accessioning automation");
      return false;
    }
    List<SpydusEdiOrchestration> inProgressOrchestrations =
        spydusEdiOrchestrationRepository.findByInvoiceGroupIdAndWorkflowStepStatus(
            spydusEdiOrchestration.getInvoiceGroupId(), WorkflowStepStatus.IN_PROGRESS);
    if (!inProgressOrchestrations.isEmpty()) {
      log.warn(
          "Skip this process. Only allowing one spydus_edi_orchestration per invoice group that is IN_PROGRESS at a time");
      return false;
    }
    boolean success = initializationStep.process(spydusEdiOrchestration);
    if (!success) {
      spydusEdiOrchestration.setWorkflowStepStatus(WorkflowStepStatus.FAILED);
    }
    spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
    return success;
  }

  private void performSteps(SpydusEdiOrchestration spydusEdiOrchestration) {
    List<WorkflowStep> workflowSteps = getWorkflowSteps();
    try {
      for (WorkflowStep step : workflowSteps) {
        performStep(spydusEdiOrchestration, step);
      }
      spydusEdiOrchestration.setWorkflowStepStatus(WorkflowStepStatus.COMPLETE);
      spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
    } catch (WorkflowException ex) {
      spydusEdiOrchestration.setWorkflowStepStatus(WorkflowStepStatus.FAILED);
      spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
    }
  }

  private static void performStep(
      SpydusEdiOrchestration spydusEdiOrchestration, WorkflowStep step) {
    boolean success;
    try {
      success = step.process(spydusEdiOrchestration);

    } catch (WorkflowException workflowException) {
      step.handleFailure(spydusEdiOrchestration, workflowException.getMessage());
      throw workflowException;
    }
    if (!success) {
      throw new WorkflowException("spydus edi accessioning automation failed!");
    }
  }

  private List<WorkflowStep> getWorkflowSteps() {
    return Lists.newArrayList(
        checkWorkOrderBarcodeStep,
        customerInvoiceEdiRetrievalStep,
        customerOrderResponsesEdiRetrievalStep,
        invoiceSpydusEdiStep,
        checkInvoiceSpydusEdiStep,
        responseSpydusEdiStep,
        checkResponseSpydusEdiStep,
        uploadFullBibRecordStep);
  }

  @Transactional
  public WorkflowStepStatus reprocess(UUID spydusEdiOrchestrationId) {
    SpydusEdiOrchestration spydusEdiOrchestration =
        getSpydusEdiOrchestration(spydusEdiOrchestrationId);
    performSteps(spydusEdiOrchestration);
    return spydusEdiOrchestration.getWorkflowStepStatus();
  }

  private SpydusEdiOrchestration getSpydusEdiOrchestration(UUID spydusEdiOrchestrationId) {
    SpydusEdiOrchestration spydusEdiOrchestration =
        spydusEdiOrchestrationRepository
            .findById(spydusEdiOrchestrationId)
            .orElseThrow(EntityNotFoundException::new);
    if (!spydusEdiOrchestration.isAllInvoicePosted()) {
      throw new BusinessException(
          "Can not reprocess for spydusEdiOrchestrationId {} due to all invoices are not posted.");
    }
    return spydusEdiOrchestration;
  }

  @Scheduled(cron = "${check-invoice-posted.schedule.cron:0 */15 * * * ?}")
  @Transactional
  public void checkInvoicePosted() {
    spydusEdiOrchestrationRepository.findAllByAllInvoicePostedIsFalse().stream()
        .filter(
            spydusEdiAccessioning ->
                spydusEdiAccessioning.getCompletedSteps().size() == 1
                    && spydusEdiAccessioning
                        .getCompletedSteps()
                        .get(0)
                        .getCompletedStep()
                        .equals(InitializationStep.class.getName()))
        .forEach(
            spydusEdiOrchestration -> {
              spydusEdiOrchestration.setRetryCount(spydusEdiOrchestration.getRetryCount() + 1);
              PartiallyInvoicedWorkOrderMessage partiallyInvoicedWorkOrderMessage =
                  PartiallyInvoicedWorkOrderMessage.from(spydusEdiOrchestration.getBodyMessage());
              Set<UUID> invoiceIds =
                  partiallyInvoicedWorkOrderMessage.getInvoices().stream()
                      .map(PartiallyInvoicedWorkOrderMessage.Invoice::getInvoiceId)
                      .collect(Collectors.toSet());
              if (areAllInvoicesPosted(partiallyInvoicedWorkOrderMessage)) {
                log.info(
                    "All invoices: {} of invoiceGroupId {} are posted.",
                    invoiceIds,
                    spydusEdiOrchestration.getInvoiceGroupId());
                spydusEdiOrchestration.setAllInvoicePosted(true);
                this.performSteps(spydusEdiOrchestration);
              } else {
                log.info(
                    "All invoices: {} of invoiceGroupId {} are not posted.",
                    invoiceIds,
                    spydusEdiOrchestration.getInvoiceGroupId());
              }
              spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
            });
  }

  private boolean areAllInvoicesPosted(
      PartiallyInvoicedWorkOrderMessage partiallyInvoicedWorkOrderMessage) {
    Set<String> lucy4InvoiceNos =
        partiallyInvoicedWorkOrderMessage.getInvoices().stream()
            .map(PartiallyInvoicedWorkOrderMessage.Invoice::getLucy4InvoiceNumber)
            .collect(Collectors.toSet());
    return CollectionUtils.isEqualCollection(
        lucy4InvoiceNos, lucy4InvoiceRepository.checkInvoicePosted(lucy4InvoiceNos));
  }

  @Transactional
  public void initReprocess(UUID spydusOrchestrationId) {
    SpydusEdiOrchestration spydusEdiOrchestration =
        getSpydusEdiOrchestration(spydusOrchestrationId);
    List<SpydusEdiOrchestration> inProgressOrchestrations =
        spydusEdiOrchestrationRepository.findByInvoiceGroupIdAndWorkflowStepStatusIn(
            spydusEdiOrchestration.getInvoiceGroupId(),
            Lists.newArrayList(WorkflowStepStatus.IN_PROGRESS, WorkflowStepStatus.STOPPED));
    if (!inProgressOrchestrations.isEmpty()) {
      String message =
          "Skip this process. Only allowing one spydus_edi_orchestration per invoice group that is IN_PROGRESS or STOPPED at a time";
      log.warn(message);
      throw new BusinessException(message);
    }
    spydusEdiOrchestration.setWorkflowStepStatus(WorkflowStepStatus.IN_PROGRESS);
    spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
  }

  @Transactional
  public WorkflowStepStatus stop(UUID spydusEdiOrchestrationId) {
    SpydusEdiOrchestration spydusEdiOrchestration =
        getSpydusEdiOrchestration(spydusEdiOrchestrationId);
    if (spydusEdiOrchestration.getWorkflowStepStatus() != WorkflowStepStatus.IN_PROGRESS) {
      String errorMsg =
          String.format(
              "Cannot stop spydus edi accessioning automation for spydusOrchestrationId: %s due to it's status is %s",
              spydusEdiOrchestrationId, spydusEdiOrchestration.getWorkflowStepStatus());
      throw new BusinessException(errorMsg);
    }
    spydusEdiOrchestration.setWorkflowStepStatus(WorkflowStepStatus.STOPPED);
    spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
    return spydusEdiOrchestration.getWorkflowStepStatus();
  }

  @Transactional
  public void initSkipStepProcess(UUID spydusEdiOrchestrationId) {
    SpydusEdiOrchestration spydusEdiOrchestration =
        getSpydusEdiOrchestration(spydusEdiOrchestrationId);
    if (spydusEdiOrchestration.getWorkflowStepStatus() != WorkflowStepStatus.FAILED) {
      String errorMsg =
          String.format(
              "Cannot skip step spydus edi accessioning automation for spydusOrchestrationId: %s "
                  + "due to it's status is %s",
              spydusEdiOrchestrationId, spydusEdiOrchestration.getWorkflowStepStatus());
      throw new BusinessException(errorMsg);
    }
    spydusEdiOrchestration.setWorkflowStepStatus(WorkflowStepStatus.IN_PROGRESS);
    spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
  }

  @Transactional
  public WorkflowStepStatus skipStep(UUID spydusEdiOrchestrationId) {
    SpydusEdiOrchestration spydusEdiOrchestration =
        getSpydusEdiOrchestration(spydusEdiOrchestrationId);
    WorkflowStep nextProcessStep =
        getNextProcessStep(spydusEdiOrchestration)
            .orElseThrow(
                () ->
                    new BusinessException(
                        String.format(
                            "All workflow steps are completed for orchestration ID: %s",
                            spydusEdiOrchestration.getId())));
    spydusEdiOrchestration
        .getCompletedSteps()
        .add(
            SpydusEdiOrchestrationCompletedStep.from(
                spydusEdiOrchestration, nextProcessStep.getClass().getSuperclass().getName()));
    spydusEdiOrchestrationRepository.save(spydusEdiOrchestration);
    log.debug(
        "Start to reprocess for pydus edi accessioning automation for spydusOrchestrationId: {}",
        spydusEdiOrchestrationId);
    performSteps(spydusEdiOrchestration);
    return spydusEdiOrchestration.getWorkflowStepStatus();
  }

  private Optional<WorkflowStep> getNextProcessStep(SpydusEdiOrchestration spydusEdiOrchestration) {
    List<WorkflowStep> workflowSteps = getWorkflowSteps();
    List<String> completedSteps =
        spydusEdiOrchestration.getCompletedSteps().stream()
            .map(SpydusEdiOrchestrationCompletedStep::getCompletedStep)
            .collect(Collectors.toList());
    for (WorkflowStep step : workflowSteps) {
      if (!completedSteps.contains(step.getClass().getSuperclass().getName())) {
        return Optional.of(step);
      }
    }
    return Optional.empty();
  }
}
