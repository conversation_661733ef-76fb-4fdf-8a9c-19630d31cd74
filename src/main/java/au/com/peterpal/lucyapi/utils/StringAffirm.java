package au.com.peterpal.lucyapi.utils;

import java.util.Optional;

public class StringAffirm {
  private String str;

  public static StringAffirm of(String str) {
    return new StringAffirm(str);
  }

  private StringAffirm(String str) {
    this.str = str;
  }

  public void hasText(String message) {
    String str = (String) Optional.ofNullable(message).filter((s) -> {
      return !s.isEmpty();
    }).orElse("String must not be blank");
    if (!this.hasText()) {
      throw new IllegalArgumentException(message);
    }
  }

  public boolean hasText() {
    return this.str != null && !this.str.isEmpty() && this.containsText(this.str);
  }

  private boolean containsText(CharSequence str) {
    int strLen = str.length();

    for(int i = 0; i < strLen; ++i) {
      if (!Character.isWhitespace(str.charAt(i))) {
        return true;
      }
    }

    return false;
  }
}
