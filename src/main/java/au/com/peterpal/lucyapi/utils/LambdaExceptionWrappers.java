package au.com.peterpal.lucyapi.utils;

import java.util.function.Consumer;

public class LambdaExceptionWrappers {

  private LambdaExceptionWrappers() {
    throw new IllegalStateException("Utility class");
  }

  public static <T> Consumer<T> throwingConsumerWrapper(ThrowingConsumer<T, Exception> throwingConsumer) {
    return i -> {
      try {
        throwingConsumer.accept(i);
      } catch (Exception ex) {
        throw new RuntimeException(ex);
      }
    };
  }
}
