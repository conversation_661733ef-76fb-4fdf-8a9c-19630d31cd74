package au.com.peterpal.lucyapi.utils;

import java.util.Optional;
import lucy.catalogue.codes.OrganisationIdentifierTypeCode;
import lucy.catalogue.entity.OrganisationIdentifier;

public class Helper {

  private Helper() {
    throw new IllegalStateException("Utility class");
  }

  public static Object notNull(Object obj, String msg) {
    return Optional.ofNullable(obj).orElseThrow(() -> new IllegalArgumentException(msg));
  }

  public static String notBlank(String str, String msg) {
    return Optional.ofNullable(str)
      .filter(s -> !s.isEmpty())
      .orElseThrow(() -> new IllegalArgumentException(msg));
  }

  public static OrganisationIdentifier getCustomerId(String customerCode) {
    OrganisationIdentifier orgId;
    if (customerCode == null || customerCode.isEmpty()) {
      orgId = new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS, "DEFAULT");
    } else {
      orgId = new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS, customerCode);
    }
    return orgId;
  }

}
