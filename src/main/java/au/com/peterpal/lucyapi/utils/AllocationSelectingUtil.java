package au.com.peterpal.lucyapi.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;

public class AllocationSelectingUtil {

    public static List<String> selectBranchCodeToProcess(Map<String, Integer> mapOfAllocationIdAndQuantity, int workOrderQuantity) {
        List<String> branchCodesToProcess = new ArrayList<>();
        List<String> branchCodesToHold = new ArrayList<>();
        int workOrderQuantityLeft = workOrderQuantity;

        for (Map.Entry<String, Integer> allocationIdAndQuantityEntry : mapOfAllocationIdAndQuantity.entrySet()) {
            if (workOrderQuantityLeft >= allocationIdAndQuantityEntry.getValue()) {
                branchCodesToProcess.add(allocationIdAndQuantityEntry.getKey());
                workOrderQuantityLeft -= allocationIdAndQuantityEntry.getValue();
            } else {
                branchCodesToHold.add(allocationIdAndQuantityEntry.getKey());
            }
        }
        if (workOrderQuantityLeft != 0) {
            return trySelectBranchCodeToMatchWorkOrderQuantity(mapOfAllocationIdAndQuantity,
                branchCodesToProcess,
                branchCodesToHold,
                workOrderQuantityLeft);
        }
        return branchCodesToProcess;
    }

    private static List<String> trySelectBranchCodeToMatchWorkOrderQuantity(Map<String, Integer> mapOfAllocationIdAndQuantity,
                                                                            List<String> branchCodesToProcess,
                                                                            List<String> branchCodesToHold,
                                                                            int workOrderQuantityLeft) {
        for (String branchCodeToHold : branchCodesToHold) {
            ListIterator<String> integerListIterator = branchCodesToProcess.listIterator(branchCodesToProcess.size());
            while (integerListIterator.hasPrevious()) {
                if (workOrderQuantityLeft
                    + mapOfAllocationIdAndQuantity.get(integerListIterator.previous())
                    - mapOfAllocationIdAndQuantity.get(branchCodeToHold) == 0) {
                    integerListIterator.remove();
                    branchCodesToProcess.add(branchCodeToHold);
                    return branchCodesToProcess;
                }
            }
        }
        return branchCodesToProcess;
    }
}
