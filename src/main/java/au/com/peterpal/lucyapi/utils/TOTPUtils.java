package au.com.peterpal.lucyapi.utils;
import com.eatthepath.otp.TimeBasedOneTimePasswordGenerator;

import java.security.InvalidKeyException;
import java.time.Instant;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.codec.binary.Base32;
import org.apache.commons.lang3.StringUtils;
@Log4j2
public class TOTPUtils {
    public static String generateOneTimePassword(String base32Secret) {
        if(StringUtils.isBlank(base32Secret)) {
            return null;
        }
        // Decode Base32 string to bytes
        Base32 base32 = new Base32();
        byte[] secretBytes = base32.decode(base32Secret);

        // Generate TOTP
        TimeBasedOneTimePasswordGenerator totp = new TimeBasedOneTimePasswordGenerator();
        SecretKey key = new SecretKeySpec(secretBytes, totp.getAlgorithm());
        String otpCode = null;
        try {
            otpCode = totp.generateOneTimePasswordString(key, Instant.now());
        } catch (InvalidKeyException e) {
            log.error("Can't generate one time password for base32Secret {}", base32Secret, e);

        }

        return otpCode;
    }
}
