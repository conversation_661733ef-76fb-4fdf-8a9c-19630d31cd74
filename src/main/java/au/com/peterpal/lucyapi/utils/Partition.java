package au.com.peterpal.lucyapi.utils;

import java.util.AbstractList;
import java.util.ArrayList;
import java.util.List;

/**
 * <class>Partition</class> transforms a list of a given type into a collection of blocks of that
 * type of a given size.
 *
 * @param <T>
 * @see <a href="https://e.printstacktrace.blog/divide-a-list-to-lists-of-n-size-in-Java-8/">Divide a list to lists of a given size</a>
 */
public final class Partition<T> extends AbstractList<List<T>> {
  private final List<T> list;
  private final int blockSize;

  public Partition(List<T> list, int blockSize) {
    this.list = new ArrayList<>(list);
    this.blockSize = blockSize;
  }

  public static <T> Partition<T> ofSize(List<T> list, int blockSize) {
    return new Partition<>(list, blockSize);
  }

  @Override
  public List<T> get(int index) {
    int start = index * blockSize;
    int end = Math.min(start + blockSize, list.size());

    if (start > end) {
      throw new IndexOutOfBoundsException(String.format("Index %d is out of the list range <0,%d>", index, size() - 1));
    }

    return new ArrayList<>(list.subList(start, end));
  }

  @Override
  public int size() {
    return (int) Math.ceil((double) list.size() / (double) blockSize);
  }
}
