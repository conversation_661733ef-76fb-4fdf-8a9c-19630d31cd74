package au.com.peterpal.lucyapi.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

public class DateFormatUtil {

    public static LocalDate parseDate(String dateString, List<String> formatPatterns) {
        for (String pattern : formatPatterns) {
            try {
                return LocalDate.parse(dateString, DateTimeFormatter.ofPattern(pattern));
            } catch (DateTimeParseException e) {
                // try next pattern
            }
        }
        throw new IllegalArgumentException("Cannot parse date: " + dateString);
    }
}
