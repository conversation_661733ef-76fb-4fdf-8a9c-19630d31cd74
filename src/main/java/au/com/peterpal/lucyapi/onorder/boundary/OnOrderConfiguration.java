package au.com.peterpal.lucyapi.onorder.boundary;

import au.com.peterpal.lucyapi.onorder.model.OnOrderEvent;
import java.util.concurrent.TimeUnit;
import javax.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.MessageChannels;
import org.springframework.integration.dsl.Pollers;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.integration.jpa.dsl.Jpa;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.messaging.MessageChannel;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

@Configuration
@EnableIntegration
@Transactional
public class OnOrderConfiguration {

  private static final String MESSAGE_TYPE = "message_type";

  @Value("${events.lucy-cataloguing-on-order:lucy-cataloguing-fees}")
  private static final String ON_ORDER_ORDER_SERVICE_CHANNEL = "lucy-api:customer-invoices";

  private static final String ON_ORDER_DISPATCHER_CHANNEL_INPUT =
      "dispatchOnOrderEvent.input";

  private final EntityManagerFactory entityManagerFactory;
  private final PlatformTransactionManager platformTransactionManager;
  private final JmsTemplate jmsTemplate;
  private final String EVENT_TYPE = "onorder";

  @Value("${events.onOrder.poller.time:10}")
  private int POLL_TIME_IN_SECONDS;

  @Value("${events.onOrder.poller.initial-delay:10}")
  private int INITIAL_DELAY_IN_SECONDS;

  @Value("${events.onOrder.poller.max-results:10}")
  private int MAX_RESULTS;

  public OnOrderConfiguration(
      @Qualifier("cataloguingEM") EntityManagerFactory entityManagerFactory,
      JmsTemplate jmsTemplate,
      @Qualifier("cataloguingTransactionManager") PlatformTransactionManager platformTransactionManager) {
    this.entityManagerFactory = entityManagerFactory;
    this.jmsTemplate = jmsTemplate;
    this.platformTransactionManager = platformTransactionManager;
  }

  @Bean
  public MessageChannel onOrderChannel() {
    return MessageChannels.publishSubscribe().get();
  }

  @Bean
  @Transactional
  public IntegrationFlow onOrderPoller() {
    return IntegrationFlows.from(
            Jpa.inboundAdapter(this.entityManagerFactory)
                .entityClass(OnOrderEvent.class)
                .deleteAfterPoll(true)
                .flushAfterDelete(true),
            entity ->
                entity.poller(
                    Pollers.fixedDelay(
                            POLL_TIME_IN_SECONDS, TimeUnit.SECONDS, INITIAL_DELAY_IN_SECONDS)
                        .transactional(platformTransactionManager)))
        .split()
        .channel(onOrderChannel())
        .get();
  }

  @Bean
  public IntegrationFlow onOrderRouter() {
    return IntegrationFlows.from(onOrderChannel())
        .enrichHeaders(
            header ->
                header.<OnOrderEvent>headerFunction(
                    MESSAGE_TYPE, message -> message.getPayload().getEventType()))
        .log(
            message ->
                "Received on local channel: "
                    + message.getPayload()
                    + " Headers: "
                    + message.getHeaders())
        .<OnOrderEvent, String>route(
            OnOrderEvent::getEventType,
            mapping ->
                mapping.channelMapping(
                    EVENT_TYPE, ON_ORDER_DISPATCHER_CHANNEL_INPUT))
        .get();
  }

  @Bean
  public IntegrationFlow dispatchOnOrderEvent() {
    return flow ->
        flow.log(LoggingHandler.Level.DEBUG, message -> "Dispatching: " + message.getPayload())
            .transform(OnOrderConverter::convert)
            .transform(Transformers.toJson())
            .log(LoggingHandler.Level.INFO, message -> "Sending via JMS: " + message.getPayload())
            .handle(
                Jms.outboundAdapter(this.jmsTemplate)
                    .destination(ON_ORDER_ORDER_SERVICE_CHANNEL));
  }
}
