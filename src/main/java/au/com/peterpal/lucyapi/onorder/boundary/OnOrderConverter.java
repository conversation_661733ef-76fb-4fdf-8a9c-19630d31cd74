package au.com.peterpal.lucyapi.onorder.boundary;

import au.com.peterpal.lucyapi.onorder.model.OnOrderEvent;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class OnOrderConverter {

  public static OnOrderMessage convert(OnOrderEvent onOrderEvent) {
    return OnOrderMessage.of(onOrderEvent.getEntityKey(), "OOR");
  }

  @Getter
  @AllArgsConstructor(staticName = "of")
  public static class OnOrderMessage {

    String orderNumber;

    String feeCode;
  }
}
