package au.com.peterpal.lucyapi.onorder.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Entity
@Table(name = "onorder_event")
public class OnOrderEvent {

  @Id
  Long id;

  @Column(name = "date_time_created")
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.S")
  LocalDateTime dateTimeCreated;

  @Column(name = "event_type")
  String eventType;

  @Column(name = "entity_name")
  String entityName;

  @Column(name = "entity_key")
  String entityKey;
}
