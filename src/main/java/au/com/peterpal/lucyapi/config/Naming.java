package au.com.peterpal.lucyapi.config;

import static java.lang.String.format;

import java.util.Properties;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.beans.RemoteCatalogue;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.beans.RemoteCataloguingBean;
import lucy.fulfillment.beans.RemoteFulfillment;
import lucy.fulfillment.beans.RemoteFulfillmentBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Log4j2
@Configuration
public class Naming {

  private static final String INITIAL_CONTEXT_FACTORY = "org.jboss.naming.remote.client.InitialContextFactory";
  private static final String URL_PKG_PREFIXES = "org.jboss.ejb.client.naming";
  private static final String APP_NAME = "lucy-wildfly";
  private static final String JNDI_NAME = "ejb:%s/%s/%s%s!%s";
  private static final String EJB_LOOKING_LOG = "Looking EJB via JNDI: %s";
  private static final String MODULE_NAME_CATALOGUING = "LucyCataloguingEJB";
  private static final String MODULE_NAME_FULFILLMENT = "LucyFulfillmentEJB";
  private static final String MODULE_NAME_CATALOGUE = "LucyCatalogueEJB";

  @Value("${naming.url:http-remoting://cw-uat-feeds.peterpal.local:9580}")
  private String remoteUrl;

  @Bean
  public Context context() throws NamingException {
    Properties jndiProps = new Properties();
    jndiProps.put(Context.INITIAL_CONTEXT_FACTORY, INITIAL_CONTEXT_FACTORY);
    jndiProps.put(Context.URL_PKG_PREFIXES, URL_PKG_PREFIXES);
    jndiProps.put(Context.PROVIDER_URL,remoteUrl);
    jndiProps.put("jboss.naming.client.ejb.context", true);
    return new InitialContext(jndiProps);
  }

  @Bean
  public RemoteCataloguing remoteCataloguing(Context context) throws NamingException {
    return (RemoteCataloguing)context.lookup(getCataloguingJndiName());
  }

  @Bean
  public RemoteFulfillment remoteFulfilment(Context context) throws NamingException {
    return (RemoteFulfillment) context.lookup(getFulfilmentJndiName());
  }

  @Bean
  public RemoteCatalogue remoteCatalogue(Context context) throws NamingException {
    return (RemoteCatalogue) context.lookup(getCatalogueJndiName());
  }

  private String getCataloguingJndiName() {
    final String appName = APP_NAME;
    final String moduleName = MODULE_NAME_CATALOGUING;
    String distinctName = "";
    final String beanName = RemoteCataloguingBean.class.getSimpleName();

    final String viewClassName = RemoteCataloguing.class.getName();

    distinctName = format("%s", (distinctName.isEmpty() ? "" : "/" + distinctName));
    String jndiName = format(JNDI_NAME, appName, moduleName, distinctName, beanName, viewClassName);
    log.info(format(EJB_LOOKING_LOG, jndiName));
    return jndiName;
  }

  private String getFulfilmentJndiName() {
    final String appName = APP_NAME;
    final String moduleName = MODULE_NAME_FULFILLMENT;
    String distinctName = "";
    final String beanName = RemoteFulfillmentBean.class.getSimpleName();

    final String viewClassName = RemoteFulfillment.class.getName();

    distinctName = format("%s", (distinctName.isEmpty() ? "" : "/" + distinctName));
    String jndiName = format(JNDI_NAME, appName, moduleName, distinctName, beanName, viewClassName);
    log.info(format(EJB_LOOKING_LOG, jndiName));
    return jndiName;
  }

  private String getCatalogueJndiName() {
    final String appName = APP_NAME;
    final String moduleName = MODULE_NAME_CATALOGUE;
    String distinctName = "";
    final String beanName = RemoteCatalogue.class.getSimpleName() + "Bean";

    final String viewClassName = RemoteCatalogue.class.getName();

    distinctName = format("%s", (distinctName.isEmpty() ? "" : "/" + distinctName));
    String jndiName = format(JNDI_NAME, appName, moduleName, distinctName, beanName, viewClassName);
    log.info(format(EJB_LOOKING_LOG, jndiName));
    return jndiName;
  }

}
