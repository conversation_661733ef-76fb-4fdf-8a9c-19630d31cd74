package au.com.peterpal.lucyapi.config;

import au.com.peterpal.common.security.config.SecurityAdapter;
import lombok.extern.log4j.Log4j2;
import org.keycloak.adapters.springsecurity.KeycloakConfiguration;
import org.keycloak.adapters.springsecurity.filter.KeycloakAuthenticationProcessingFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;

@Log4j2
@KeycloakConfiguration
public class SecurityConfig extends SecurityAdapter {

  private static final String ROLE_USER = "User";
  private static final String ROLE_ADMIN = "Admin";

  @Value("${security.enabled:true}")
  private boolean securityEnabled;

  @Override
  public void configure(HttpSecurity http) throws Exception {
    super.configure(http);

    http.authorizeRequests()
        .antMatchers(
            "/api/orders/**",
            "/api/recordsources/**",
            "/api/invoice/**",
            "/api/bibrecords/**",
            "/api/titleauthorities/**",
            "/api/bibtemplates/**",
            "/api/batches/**",
            "/api/clients/**",
            "/api/collectiontype/**",
            "/api/holdings/**",
            "/api/lms/**",
            "/api/printer/**",
            "/api/spydus-edi-accessioning/**")
        .hasRole(ROLE_USER)
        .antMatchers("/api/admin/**")
        .hasRole(ROLE_ADMIN)
        .anyRequest()
        .authenticated();
  }

  @Override
  public void configure(WebSecurity web) throws Exception {
    super.configure(web);

    if (!securityEnabled) web.ignoring().antMatchers("/**");
  }

  @Bean
  public FilterRegistrationBean filterRegistration(KeycloakAuthenticationProcessingFilter filter) {
    FilterRegistrationBean registration = new FilterRegistrationBean(filter);
    registration.setEnabled(securityEnabled);
    return registration;
  }
}
