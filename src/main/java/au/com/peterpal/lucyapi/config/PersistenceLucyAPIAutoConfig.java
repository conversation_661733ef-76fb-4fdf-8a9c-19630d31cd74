package au.com.peterpal.lucyapi.config;

import java.util.Properties;
import javax.sql.DataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@EnableJpaRepositories(
    basePackages = {
        "au.com.peterpal.lucyapi.persistence.lucyapi",
        "au.com.peterpal.lucyapi.persistence.lucyapi.model"
    },
    entityManagerFactoryRef = "lucyAPI",
    transactionManagerRef = "lucyAPITransactionManager")
public class PersistenceLucyAPIAutoConfig {

    private final Environment env;

    public PersistenceLucyAPIAutoConfig(Environment env) {
        this.env = env;
    }

    @Bean
    @ConfigurationProperties(prefix = "lucyapi.datasource")
    public DataSource lucyapiDataSource() {
        final DriverManagerDataSource ds = new DriverManagerDataSource();
        ds.setDriverClassName(env.getProperty("lucyapi.datasource.driverClassName"));
        ds.setUrl(env.getProperty("lucyapi.datasource.url"));
        ds.setUsername(env.getProperty("lucyapi.datasource.username"));
        ds.setPassword(env.getProperty("lucyapi.datasource.password"));
        return ds;
    }

    @Bean(name = "lucyAPI")
    public LocalContainerEntityManagerFactoryBean lucyAPIEntityManagerFactory() {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(lucyapiDataSource());
        em.setPackagesToScan(new String[] {
                "au.com.peterpal.lucyapi.persistence.lucyapi",
                "au.com.peterpal.lucyapi.persistence.lucyapi.model"});

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        Properties props = new Properties();
        props.setProperty("hibernate.hbm2ddl.auto", env.getProperty("lucyapi.jpa.hibernate.ddl-auto"));
        props.setProperty("hibernate.dialect", env.getProperty("lucyapi.jpa.hibernate.dialect"));
        em.setJpaProperties(props);

        return em;
    }

    @Bean
    public PlatformTransactionManager lucyAPITransactionManager() {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(lucyAPIEntityManagerFactory().getObject());
        return transactionManager;
    }
}
