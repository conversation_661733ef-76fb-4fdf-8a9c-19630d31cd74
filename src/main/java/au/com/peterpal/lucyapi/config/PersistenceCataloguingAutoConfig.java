package au.com.peterpal.lucyapi.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@EnableJpaRepositories(
    basePackages = {
        "au.com.peterpal.lucyapi.persistence.cataloguing",
        "au.com.peterpal.lucyapi.persistence.cataloguing.model",
        "au.com.peterpal.lucyapi.persistence.lucy",
        "au.com.peterpal.lucyapi.persistence.lucy.model"
    },
    entityManagerFactoryRef = "cataloguingEM",
    transactionManagerRef = "cataloguingTransactionManager")
public class PersistenceCataloguingAutoConfig {

  private final Environment env;

  public PersistenceCataloguingAutoConfig(Environment env) {
    this.env = env;
  }

  @Bean
  @ConfigurationProperties(prefix = "cataloguing.datasource")
  public DataSource cataloguingDataSource() {
    final DriverManagerDataSource ds = new DriverManagerDataSource();
    ds.setDriverClassName(env.getProperty("cataloguing.datasource.driverClassName"));
    ds.setUrl(env.getProperty("cataloguing.datasource.url"));
    ds.setUsername(env.getProperty("cataloguing.datasource.username"));
    ds.setPassword(env.getProperty("cataloguing.datasource.password"));
    return ds;
  }

  @Bean(name = "cataloguingEM")
  public LocalContainerEntityManagerFactoryBean cataloguingEntityManagerFactory() {
    LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
    em.setDataSource(cataloguingDataSource());
    em.setPackagesToScan(new String[] {
        "au.com.peterpal.lucyapi.persistence.cataloguing",
        "au.com.peterpal.lucyapi.persistence.cataloguing.model",
        "au.com.peterpal.lucyapi.onorder.model"
    });

    JpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
    em.setJpaVendorAdapter(vendorAdapter);
    Properties props = new Properties();
    props.setProperty("hibernate.hbm2ddl.auto", env.getProperty("cataloguing.jpa.hibernate.ddl-auto"));
    props.setProperty("hibernate.dialect", env.getProperty("cataloguing.jpa.hibernate.dialect"));
    em.setJpaProperties(props);

    return em;
  }

  @Bean
  public PlatformTransactionManager cataloguingTransactionManager() {
    JpaTransactionManager transactionManager = new JpaTransactionManager();
    transactionManager.setEntityManagerFactory(cataloguingEntityManagerFactory().getObject());
    return transactionManager;
  }

  @Bean
  public TransactionTemplate cataloguingTransactionTemplate(){
    return new TransactionTemplate(cataloguingTransactionManager());
  }

  @Bean
  public NamedParameterJdbcTemplate cataloguingJdbcTemplate(){
    return new NamedParameterJdbcTemplate(cataloguingDataSource());
  }
}
