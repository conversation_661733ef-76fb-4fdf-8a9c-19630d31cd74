package au.com.peterpal.lucyapi.config;

import au.com.peterpal.lucyapi.core.service.TitleOrderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.Arrays;
import javax.jms.ConnectionFactory;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.MessageChannels;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

@Log4j2
@Configuration
@EnableIntegration
public class IntegrationConfig {

  private static final String MESSAGE_TYPE_HEADER = "message_type";
  private static final String SEND_TRANSMISSION = "send-transmission";
  private static final String MT_ADD_TO_PURCHASE_ORDER = "add-to-purchase-order";

  @Value("${clientweb.messages.queue.name: clientweb-messages}")
  private String clientWebMessageQueue;

  @Value("${events.work-order-complete-task.channel:work-order-complete-task-events}")
  private String workOrderCompleteTaskEvents;
  @Value("${events.remove-barcode.channel:lucy-api:work-orders.remove-barcode-events}")
  private String removeBarcodeEvents;
  @Value("${events.print-label.channel:print-label-events}")
  private String printLabelEvents;
  @Value("${events.modified-full-bib-record.channel:orchestration:lucy-api.modified-full-bib-records-events}")
  private String modifiedFullBibRecordEvents;


  @Value("${events.email-service.channel:email-service-topic}")
  private String emailServiceChannel;

  private final ConnectionFactory connectionFactory;

  private final TitleOrderService titleOrderService;

  public IntegrationConfig(
      ConnectionFactory connectionFactory, TitleOrderService titleOrderService) {
    this.connectionFactory = connectionFactory;
    this.titleOrderService = titleOrderService;
  }

  @Bean
  public MessageChannel addToPurchaseOrderChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow addToPurchaseOrderFlow() {
    return IntegrationFlows.from(addToPurchaseOrderChannel())
        .enrichHeaders(
            headerEnricherSpec ->
                headerEnricherSpec.header(MESSAGE_TYPE_HEADER, MT_ADD_TO_PURCHASE_ORDER))
        .transform(Transformers.toJson())
        .log(
            message ->
                String.format(
                    "Sending title orders %s to %s", message.getPayload(), clientWebMessageQueue))
        .log(message -> String.format("With headers %s", message.getHeaders()))
        .handle(Jms.outboundAdapter(connectionFactory).destination(clientWebMessageQueue))
        .get();
  }

  @Bean
  public MessageChannel workOrderCompleteTaskChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow workOrderCompleteTaskFlow() {
    return IntegrationFlows.from(workOrderCompleteTaskChannel())
        .transform(Transformers.toJson())
        .log(
            message ->
                String.format(
                    "Sending work order complete task %s to %s", message.getPayload(), workOrderCompleteTaskEvents))
        .log(message -> String.format("With headers %s", message.getHeaders()))
        .handle(Jms.outboundAdapter(connectionFactory).destination(workOrderCompleteTaskEvents))
        .get();
  }
  @Bean
  public MessageChannel printLabelChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow printLabelFlow() {
    return IntegrationFlows.from(printLabelChannel())
        .transform(Transformers.toJson())
        .log(
            message ->
                String.format(
                    "Sending print label %s to %s", message.getPayload(), printLabelEvents))
        .log(message -> String.format("With headers %s", message.getHeaders()))
        .handle(Jms.outboundAdapter(connectionFactory).destination(printLabelEvents))
        .get();
  }

  @Bean
  public MessageChannel removeBarcodeChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow removeBarcodeFlow() {
    return IntegrationFlows.from(removeBarcodeChannel())
        .transform(Transformers.toJson())
        .log(
            message ->
                String.format(
                    "Sending remove barcode message %s to %s", message.getPayload(), removeBarcodeEvents))
        .log(message -> String.format("With headers %s", message.getHeaders()))
        .handle(Jms.outboundAdapter(connectionFactory).destination(removeBarcodeEvents))
        .get();
  }

  @Bean
  public IntegrationFlow addToPurchaseOrderJmsListener() {
    String msgType = String.format("%s = '%s'", MESSAGE_TYPE_HEADER, MT_ADD_TO_PURCHASE_ORDER);
    return IntegrationFlows.from(Jms.messageDrivenChannelAdapter(connectionFactory)
        .destination(clientWebMessageQueue)
        .configureListenerContainer(spec -> spec.messageSelector(msgType))
        .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
        .log(m -> "Received via JMS: " + m)
        .handle(m -> {
            String jsonStr = (String) m.getPayload();
            ObjectMapper mapper = new ObjectMapper();
            try {
              String[] titleOrders = mapper.readValue(jsonStr, String[].class);
              titleOrderService.process(Arrays.asList(titleOrders));
            } catch (IOException ex) {
              log.error("Exception converting JSON string array to Java array, exception: ", ex);
            }
        })
        .get();
  }
  @Bean
  public MessageChannel modifiedFullBibRecordChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow modifiedFullBibRecordFlow() {
    return IntegrationFlows.from(modifiedFullBibRecordChannel())
        .transform(Transformers.toJson())
        .log(
            message ->
                String.format(
                    "Sending modified full bib record %s to %s", message.getPayload(), modifiedFullBibRecordEvents))
        .log(message -> String.format("With headers %s", message.getHeaders()))
        .handle(Jms.outboundAdapter(connectionFactory).destination(modifiedFullBibRecordEvents))
        .get();
  }

  @Bean
  public MessageChannel marcRecordsEmailChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow marcRecordsEmailFlow() {
    return IntegrationFlows.from(marcRecordsEmailChannel())
        .transform(Transformers.toJson())
        .log(message -> String.format("Sending email event: %s", message.getPayload()))
        .enrichHeaders(spec -> spec.header(MESSAGE_TYPE_HEADER, SEND_TRANSMISSION))
        .log(message -> String.format("With headers %s", message.getHeaders()))
        .handle(Jms.outboundAdapter(connectionFactory).destination(emailServiceChannel))
        .get();
  }

}
