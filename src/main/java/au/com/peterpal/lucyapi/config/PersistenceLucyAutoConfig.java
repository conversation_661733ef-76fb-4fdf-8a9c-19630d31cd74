package au.com.peterpal.lucyapi.config;

import java.util.Properties;
import javax.sql.DataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
public class PersistenceLucyAutoConfig {

  private final Environment env;

  public PersistenceLucyAutoConfig(Environment env) {
    this.env = env;
  }

  @Primary
  @Bean
  @ConfigurationProperties(prefix = "spring.datasource")
  public DataSource lucyDataSource() {
    final DriverManagerDataSource ds = new DriverManagerDataSource();
    ds.setDriverClassName(env.getProperty("spring.datasource.driverClassName"));
    ds.setUrl(env.getProperty("spring.datasource.url"));
    ds.setUsername(env.getProperty("spring.datasource.username"));
    ds.setPassword(env.getProperty("spring.datasource.password"));
    return ds;
  }

  @Primary
  @Bean(name = "lucyEM")
  public LocalContainerEntityManagerFactoryBean lucyEntityManagerFactory() {
    LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
    em.setDataSource(lucyDataSource());
    em.setPackagesToScan(new String[] { "au.com.peterpal.lucyapi.persistence.lucy" });

    HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
    em.setJpaVendorAdapter(vendorAdapter);
    Properties props = new Properties();
    props.setProperty("hibernate.hbm2ddl.auto", env.getProperty("spring.jpa.hibernate.ddl-auto"));
    props.setProperty("hibernate.dialect", env.getProperty("spring.jpa.hibernate.dialect"));
    em.setJpaProperties(props);

    return em;
  }

  @Primary
  @Bean
  public PlatformTransactionManager lucyTransactionManager() {
    JpaTransactionManager transactionManager = new JpaTransactionManager();
    transactionManager.setEntityManagerFactory(lucyEntityManagerFactory().getObject());
    return transactionManager;
  }
}
