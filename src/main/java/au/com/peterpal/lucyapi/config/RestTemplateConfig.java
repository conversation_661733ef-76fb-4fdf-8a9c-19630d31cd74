package au.com.peterpal.lucyapi.config;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
@Log4j2
class RestTemplateConfig {

  @Value("${rest.template.connect-timeout-millis:60000}")
  private int serviceConnectTimeout;

  @Value("${rest.template.read-timeout-millis:60000}")
  private int serviceReadTimeout;

  @Bean(name = "restTemplate")
  public RestTemplate restTemplate() {
    log.info(
        "Initializing RestTemplate with properties "
            + "rest.template.read-timeout-millis: "
            + serviceReadTimeout
            + "rest.template.connect-timeout-millis: "
            + serviceConnectTimeout);
    ClientHttpRequestFactory factory = componentsClientHttpRequestFactory();
    return new RestTemplate(factory);
  }

  private ClientHttpRequestFactory componentsClientHttpRequestFactory() {
    SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
    factory.setReadTimeout(serviceReadTimeout);
    factory.setConnectTimeout(serviceConnectTimeout);
    return factory;
  }
}
