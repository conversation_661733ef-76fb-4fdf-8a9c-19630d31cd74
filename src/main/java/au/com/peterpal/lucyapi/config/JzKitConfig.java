package au.com.peterpal.lucyapi.config;

import org.jzkit.a2j.codec.util.OIDRegister;
import org.jzkit.util.PropsHolder;
import org.jzkit.z3950.QueryModel.PropsBasedInternalToType1ConversionRules;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JzKitConfig {

  @Bean(name = "OIDRegister")
  public OIDRegister oidRegister() {
    return new OIDRegister("/a2j.properties");
  }

  @Bean(name = "RPNToInternalRules")
  public PropsHolder rpnToInternalRules() {
    return new PropsHolder("/InternalAttrTypes.properties");
  }

  @Bean(name = "InternalToType1ConversionRules")
  public PropsBasedInternalToType1ConversionRules internalConversionRules() {
    return new PropsBasedInternalToType1ConversionRules("/InternalToType1Rules.properties");
  }
}
