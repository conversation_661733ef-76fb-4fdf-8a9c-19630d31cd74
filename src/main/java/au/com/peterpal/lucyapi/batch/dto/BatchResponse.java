package au.com.peterpal.lucyapi.batch.dto;

import au.com.peterpal.lucyapi.batch.model.BatchInfo;
import au.com.peterpal.lucyapi.batch.model.BatchStatus;
import au.com.peterpal.lucyapi.model.BibRecordType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchResponse {
  private Integer id;
  private String customerCode;
  private BatchStatus status;
  private BibRecordType bibType;
  private String description;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate dateLastModified;
  private String modifiedBy;
  private List<BibRecordInfo> bibRecords;

  public static List<BatchResponse> from(List<BatchInfo> batchInfoList) {
    return Optional.ofNullable(batchInfoList)
        .map(List::stream)
        .orElseGet(Stream::empty)
        .map(BatchResponse::from)
        .collect(Collectors.toList());
  }

  public static BatchResponse from(BatchInfo batchInfo) {
    return BatchResponse.builder()
        .id(batchInfo.getId())
        .customerCode(batchInfo.getCustomerCode())
        .status(BatchStatus.fromLucy(batchInfo.getStatus()))
        .bibType(batchInfo.getBibType())
        .description(batchInfo.getDescription())
        .dateLastModified(batchInfo.getDateLastModified())
        .modifiedBy(batchInfo.getModifiedBy())
        .bibRecords(
            Optional.ofNullable(batchInfo.getBibRecordIdList())
            .map(List::stream)
            .orElseGet(Stream::empty)
            .map(i -> BibRecordInfo.builder().bibRecordId(i).build())
            .collect(Collectors.toList())
        )
        .build();
  }
}
