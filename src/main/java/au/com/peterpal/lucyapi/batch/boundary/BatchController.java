package au.com.peterpal.lucyapi.batch.boundary;

import au.com.peterpal.lucyapi.batch.control.BatchService;
import au.com.peterpal.lucyapi.batch.control.FileService;
import au.com.peterpal.lucyapi.batch.dto.*;
import au.com.peterpal.lucyapi.batch.model.BatchDeleteInfo;
import au.com.peterpal.lucyapi.batch.model.ExportInfo;
import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import au.com.peterpal.lucyapi.core.service.LMSService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/batches", consumes = "application/json", produces = "application/json")
@Log4j2
@SecurityRequirement(name = "BearerAuth")
public class BatchController {

  private final BatchService batchService;
  private final FileService fileService;
  private final LMSService lmsService;

  public BatchController(BatchService batchService,
                         FileService fileService,
                         LMSService lmsService) {
    this.batchService = batchService;
    this.fileService = fileService;
    this.lmsService = lmsService;
  }

  @PostMapping
  public BatchResponse createBatch(
      @RequestHeader(value = "username", defaultValue = "unknown", required = false) String username,
      @RequestBody BatchRequest batchRequest) {
    log.debug(() -> String.format("Received create batch %s fom user %s", batchRequest, username));
    return BatchResponse.from(batchService.createBatch(batchRequest.toBatchInfo(), username));
  }

  @PostMapping(value = "/find")
  public List<BatchResponse> find(@RequestBody @Valid SearchParams request) {
    log.debug(() -> String.format("Received find batch request %s", request));
    return BatchResponse.from(batchService.find(request));
  }

  @GetMapping(value = "/{batchId}", consumes = "*/*")
  public BatchResponse getBatch(@PathVariable("batchId") Integer batchId) {
    log.debug(() -> String.format("Received get batch with id %d", batchId));
    return BatchResponse.from(batchService.getBatch(batchId));
  }

  @PostMapping(value = "/{batchId}/{customerCode}")
  public BatchResponse addBibRecords(
      @PathVariable Integer batchId,
      @PathVariable String customerCode,
      @RequestBody List<BibRecordRequest> records) {
    log.debug(() -> String.format("Received add bib records for batch %d, customer %s, records %s", batchId, customerCode, records));
    return BatchResponse.from(batchService.addBibRecords(batchId, customerCode, BibRecordRequest.to(records)));
  }

  @DeleteMapping
  public List<BatchDeleteInfo> deleteBatch(@RequestBody List<Integer> batchIdList) {
    log.debug(() -> String.format("Received get batch ids %s", batchIdList));
    return batchService.deleteBatch(batchIdList);
  }

  @DeleteMapping(value = "/{batchId}/delete-on-order-records", consumes = "*/*", produces = "*/*")
  public void deleteBatchOnOrderRecords(@PathVariable("batchId") Integer batchId) {
    log.debug("Delete order records for batch id: {}", batchId);
    lmsService.deleteOnOrderRecordsByBatchId(batchId);
  }

  @CrossOrigin
  @PutMapping
  public List<BatchResponse> updateBatch(@RequestBody List<BatchUpdateRequest> updateList) {
    log.debug(() -> String.format("Received update batch request  %s", updateList));
    return BatchResponse.from(batchService.update(BatchUpdateRequest.to(updateList)));
  }

  @PostMapping("/export")
  public List<ExportInfo> export(@RequestBody List<Integer> batchIdList) {
    log.debug(() -> String.format("Received export batch request %s", batchIdList));
    return batchService.export(batchIdList);
  }

  @GetMapping(value = "/downloadFile/{fileName}", consumes = "*/*", produces = "*/*")
  public ResponseEntity<Resource> downloadFile(@PathVariable String fileName, HttpServletRequest request) {
    log.debug(() -> String.format("Received request to download file %s", fileName));

    Resource resource = fileService.loadFileAsResource(fileName);

    return ResponseEntity.ok()
        .contentType(MediaType.parseMediaType(getContentType(resource, request)))
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
        .body(resource);
  }

  private String getContentType(Resource resource, HttpServletRequest request) {
    String contentType = null;
    try {
      contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
    } catch (IOException ex) {
      log.info(() -> "Could not determine file type");
    }
    if(contentType == null) {
      contentType = "application/octet-stream";
    }
    return contentType;
  }
}
