package au.com.peterpal.lucyapi.batch.dto;

import au.com.peterpal.lucyapi.batch.model.MatchStatus;
import au.com.peterpal.lucyapi.batch.model.RecordInfo;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;

@Value
@Builder
@ToString
public class BibRecordRequest {
  private String titleOrderId;
  private String productIdentifier;
  private Integer authorityBibRecordId;
  private Integer productDescriptorId;
  private Integer templateId;
  private Integer bibRecordMatchId;
  private MatchStatus status;
  private Integer matchThreshold;
  private Integer matchScore;

  public static List<RecordInfo> to(List<BibRecordRequest> records) {
    return Optional.ofNullable(records)
        .map(List::stream)
        .orElseGet(Stream::empty)
        .map(BibRecordRequest::to)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  public static RecordInfo to(BibRecordRequest record) {
    return Optional.ofNullable(record)
        .map(r -> RecordInfo.builder()
            .titleOrderId(record.titleOrderId)
            .productIdentifier(record.productIdentifier)
            .authorityId(record.authorityBibRecordId)
            .productDescriptorId(record.productDescriptorId)
            .templateId(record.templateId)
            .bibRecordMatchId(record.bibRecordMatchId)
            .status(record.status)
            .matchThreshold(record.matchThreshold)
            .matchScore(record.matchScore)
            .build())
        .orElse(null);
  }
}
