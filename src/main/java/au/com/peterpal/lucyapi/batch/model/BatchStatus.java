package au.com.peterpal.lucyapi.batch.model;

import java.util.EnumMap;
import java.util.Map;
import lucy.cataloguing.codes.BibRecordBatchStatus;

public enum BatchStatus {
  OPEN, CLOSED, SENT;

  private static final Map<BatchStatus, BibRecordBatchStatus> toLucyMap;

  private static final Map<BibRecordBatchStatus, BatchStatus> fromLucyMap;

  static {
    toLucyMap = new EnumMap<>(BatchStatus.class);
    toLucyMap.put(BatchStatus.OPEN, BibRecordBatchStatus.OPEN);
    toLucyMap.put(BatchStatus.CLOSED, BibRecordBatchStatus.CLOSED);
    toLucyMap.put(BatchStatus.SENT, BibRecordBatchStatus.SENT);

    fromLucyMap = new EnumMap<>(BibRecordBatchStatus.class);
    fromLucyMap.put(BibRecordBatchStatus.OPEN, BatchStatus.OPEN);
    fromLucyMap.put(BibRecordBatchStatus.CLOSED, BatchStatus.CLOSED);
    fromLucyMap.put(BibRecordBatchStatus.SENT, BatchStatus.SENT);
  }



  public BibRecordBatchStatus toLucy() {
    return toLucyMap.get(this);
  }

  public static BatchStatus fromLucy(BibRecordBatchStatus status) {
    return fromLucyMap.get(status);
  }
}
