package au.com.peterpal.lucyapi.batch.dto;

import au.com.peterpal.lucyapi.batch.model.BatchInfo;
import au.com.peterpal.lucyapi.model.BibRecordType;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class BatchRequest {
  private String customer;
  private BibRecordType bibType;
  private String description;

  public BatchInfo toBatchInfo() {
    return BatchInfo.builder()
        .description(description)
        .customerCode(customer)
        .bibType(bibType)
        .build();
  }
}
