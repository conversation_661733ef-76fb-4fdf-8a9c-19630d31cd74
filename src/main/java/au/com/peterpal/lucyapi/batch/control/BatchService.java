package au.com.peterpal.lucyapi.batch.control;

import static au.com.peterpal.lucyapi.utils.Helper.getCustomerId;
import static au.com.peterpal.lucyapi.utils.Helper.notBlank;
import static au.com.peterpal.lucyapi.utils.Helper.notNull;
import static java.time.LocalTime.now;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.batch.model.BatchDeleteInfo;
import au.com.peterpal.lucyapi.batch.model.BatchInfo;
import au.com.peterpal.lucyapi.batch.dto.SearchParams;
import au.com.peterpal.lucyapi.batch.model.ExportInfo;
import au.com.peterpal.lucyapi.batch.model.RecordInfo;
import au.com.peterpal.lucyapi.core.service.BibTemplateService;
import au.com.peterpal.lucyapi.persistence.cataloguing.BibRecordBatchInfoRepository;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import javax.validation.Valid;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.beans.RemoteCatalogue;
import lucy.catalogue.codes.OrganisationIdentifierTypeCode;
import lucy.catalogue.codes.RecordSourceTypeCode;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.catalogue.entity.ProductSummary;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.codes.BibRecordBatchStatus;
import lucy.cataloguing.codes.BibRecordType;
import lucy.cataloguing.entity.BibRecord;
import lucy.cataloguing.entity.BibRecordBatch;
import lucy.cataloguing.entity.BibTemplate;
import lucy.cataloguing.util.TitleOrderMatch;
import lucy.common.NotFoundException;
import lucy.fulfillment.beans.RemoteFulfillment;
import lucy.fulfillment.codes.CataloguingAction;
import lucy.fulfillment.entity.OpenTitleOrder;
import lucy.fulfillment.entity.TransactionIdentifier;
import lucy.marc.MarcException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Log4j2
public class BatchService {

  private static final String MSG_BATCH_ID_NOT_NULL = "Batch id must not be null";
  private final RemoteCataloguing remoteCataloguing;
  private final RemoteFulfillment fulfillment;
  private final RemoteCatalogue catalogue;
  private final BibTemplateService bibTemplateService;
  private final FileService fileService;

  private final BibRecordBatchInfoRepository repo;

  public BatchService(RemoteCataloguing remoteCataloguing,
      BibTemplateService bibTemplateService,
      RemoteFulfillment fulfillment,
      RemoteCatalogue catalogue,
      FileService fileService,
      BibRecordBatchInfoRepository repo) {
    this.remoteCataloguing = remoteCataloguing;
    this.bibTemplateService = bibTemplateService;
    this.fulfillment = fulfillment;
    this.catalogue = catalogue;
    this.fileService = fileService;
    this.repo = repo;
  }

  /**
   * Create a batch.
   *
   * @param batchInfo batch information for the new batch.
   * @return
   */
  public BatchInfo createBatch(BatchInfo batchInfo, String user) {
    log.info(() -> String.format("Create batch: batchInfo %s, user %s", batchInfo, user));
    return BatchInfo.from(createBibRecordBatch(batchInfo, user));
  }

  public BibRecordBatch createBibRecordBatch(BatchInfo batchInfo, String user) {
    log.info(() -> String.format("Create batch: batchInfo %s, user %s", batchInfo, user));
    return Optional.ofNullable(batchInfo)
            .map(info -> {
              String userName = user == null || user.isEmpty() ? "unknown" : user;

              OrganisationIdentifier customerId = getCustomerId(batchInfo.getCustomerCode());

              BibRecordBatch batch = new BibRecordBatch(customerId, batchInfo.getBibType().toLucy());
              batch.setDescription(batchInfo.getDescription());
              batch.recordModification(userName, RecordSourceTypeCode.UNSPEC, "CREATED");
              try {
                return remoteCataloguing.addBibRecordBatch(batch);
              } catch(Exception ex) {
                String msg = String.format("Exception creating batch from %s", batchInfo);
                throw new BusinessException(msg, ex);
              }
            })
            .get();
  }

  /**
   * Get a batch given a batch id
   *
   * @param id id of the batch to be returned
   * @return information about the selected batch.
   */
  public BatchInfo getBatch(Integer id) {
    notNull(id, MSG_BATCH_ID_NOT_NULL);
    log.info(() -> String.format("Get batch with id %d", id));
    try {
      return BatchInfo.from(remoteCataloguing.getBibRecordBatch(id.intValue()));
    } catch(NotFoundException ex) {
      String msg = String.format("Batch with id %d not found", id);
      throw new BusinessException(msg, ex);
    }
  }

  /**
   * Search for batch records
   *
   * @param parameters search parameters
   * @return list of <code>BatchInfo</code> for each batch that was found.
   */
  @Transactional
  public List<BatchInfo> find(@Valid SearchParams parameters) {
    log.info(() -> String.format("Find batch for parameters %s", parameters));
    return Optional.ofNullable(parameters)
        .map(params -> {
          try {
            BibRecordType type = params.getBibRecordType() == null ? null : params.getBibRecordType().toLucy();
            BibRecordBatchStatus status = params.getStatus() == null ? null : params.getStatus().toLucy();

            return BatchInfo.from(repo.findBibRecordBatches(params.getCustomerCode(), "PPLS", type, status));
          } catch (Exception ex) {
            String msg = String.format("Exception searching for batch. Parameters: %s", parameters);
            throw new BusinessException(msg, ex);
          }
        })
        .orElseThrow(() -> new IllegalArgumentException("Search parameters must not be null"));
  }

  /**
   * Add bib records.
   *
   * @param batchId batch id of the batch to receive the bib records
   * @param customerCode customer code
   * @param records list of record information for new bib records
   * @return a <code>BatchInfo</code> containing the result of the operation
   */
  public BatchInfo addBibRecords(Integer batchId, String customerCode, List<RecordInfo> records) {
    notNull(batchId, MSG_BATCH_ID_NOT_NULL);
    notBlank(customerCode, "Customer code must not be null or empty");
    notNull(records, "Record information must not be null");
    log.info(() -> String.format(
        "Add bib records: batch id %d, customer code %s, record %s", batchId, customerCode, records));

    BibRecordBatch batch = getBatch(batchId.intValue());

    records.stream()
        .forEach(info -> addBibRecord(batch, info));

    return BatchInfo.from(getBatch(batchId.intValue()));
  }

  /**
   * Delete a list of batches.
   *
   * @param batchIdList list of batch ids to delete
   * @return a list of <class>BatchDeleteInfo</class> containing the result of the delete operation
   */
  public List<BatchDeleteInfo> deleteBatch(List<Integer> batchIdList) {
    log.info(() -> String.format("Delete batch list %s", batchIdList));

    return Optional.ofNullable(batchIdList)
        .map(l -> l.stream()
            .map(this::deleteBatch)
            .collect(Collectors.toList())
        )
        .orElse(new ArrayList<>());
  }

  public Set<BatchInfo> close(Set<Integer> batchIds) {
    log.info("CLose batch list {}", batchIds);
    return Optional.ofNullable(batchIds)
        .map(
            l ->
                l.stream()
                    .map(
                        batchId -> {
                          BatchInfo batchInfo = getBatch(batchId);
                          batchInfo.setStatus(BibRecordBatchStatus.CLOSED);
                          return update(batchInfo);
                        })
                    .collect(Collectors.toSet()))
        .orElse(new HashSet<>());
  }

  /**
   * Update a group of batches.
   *
   * @param updateList list of update information for each batch. Attributes in <class>BatchInfo</class>
   * that are not null will be updated.
   *
   * @return a list containing the result of the operation
   */
  public List<BatchInfo> update(List<BatchInfo> updateList) {
    log.info(() -> String.format("Update batch list %s", updateList));

    return Optional.ofNullable(updateList)
        .map(l -> l.stream()
            .map(this::update)
            .collect(Collectors.toList())
        )
        .orElse(new ArrayList<>());
  }

  /**
   *
   * @param batchIdList
   * @return
   */
  public List<ExportInfo> export(List<Integer> batchIdList) {
    log.info(() -> String.format("Export batch list %s", batchIdList));

    return Optional.ofNullable(batchIdList)
        .filter(l -> !l.isEmpty())
        .map(l -> l.stream()
            .map(this::export)
            .filter(Objects::nonNull)
            .collect(Collectors.toList()))
        .orElseThrow(() -> new IllegalArgumentException("Batch id list must not be null or empty"));
  }

  public Integer addToNewOrExistingBatch(String customerCode, BibRecord bibRecord, Set<Integer> batchIds)  {
    List<BibRecordBatch> batches = getBibRecordBatches(customerCode, bibRecord, batchIds);

    BatchInfo bibRecordBatch = BatchInfo.from(batches.stream().filter(b ->
        b.getEntryRecord().getModifiedBy().equals("lucy-api")
            && isTimeOnly(b.getDescription())).findFirst().orElseGet(
            () -> {
              // If can't find an existing batch, make a new one ...
              BatchInfo batchInfo = BatchInfo.builder()
                      .customerCode(customerCode)
                      .bibType(au.com.peterpal.lucyapi.model.BibRecordType.from(bibRecord.getBibRecordType()))
                      .description("" + now())
                      .build();
              return createBibRecordBatch(batchInfo, "lucy-api");
            }));

    log.debug(() -> String.format("Using BibRecordBatch: %s %s",bibRecordBatch.getId(),bibRecordBatch.getDescription()));

    // Add Full Bib Record to Batch
    try {
      remoteCataloguing.addBibRecordToBatch(bibRecordBatch.getId(), bibRecord.getPk());
    } catch (Exception e) {
      String msg = "Failed to add to batch. " + e.getMessage();
      log.error(msg, e);
      throw new BusinessException(msg);
    }
    return bibRecordBatch.getId();
  }

  private boolean isTimeOnly(String description) {
    if (description == null) {
      return false;
    }
    String regexPattern = "^\\d{2}:\\d{2}:\\d{2}\\.\\d{3}$";

    Pattern pattern = Pattern.compile(regexPattern);

    Matcher matcher = pattern.matcher(description);
    return matcher.matches();
  }

  private ExportInfo export(Integer batchId) {
    return Optional.ofNullable(batchId)
        .map(id -> {
          ExportInfo result;
          try {
            BibRecordBatch batch = remoteCataloguing.getBibRecordBatch(id);
            String fileName = fileService.export(batch);
            result =  ExportInfo.of(batch.getPk(), fileName);
          }
          catch(NotFoundException ex) {
            log.warn(() -> String.format("Could not find batch with id %d", (int)id), ex);
            result = null;
          }
          catch(Exception ex) {
            log.warn(() -> String.format("Exception exporting batch with id %d", (int)id), ex);
            result = null;
          }
          return result;
        })
        .orElseThrow(() -> new IllegalArgumentException(MSG_BATCH_ID_NOT_NULL));
  }

  private BatchInfo update(BatchInfo updateInfo) {
    notNull(updateInfo, "Update batch info must not be null");
    notNull(updateInfo.getId(), MSG_BATCH_ID_NOT_NULL);

    try {
      BibRecordBatch batch = remoteCataloguing.getBibRecordBatch(updateInfo.getId());
      update(batch, updateInfo);
      remoteCataloguing.updateBibRecordBatch(batch);
      return BatchInfo.from(remoteCataloguing.getBibRecordBatch(updateInfo.getId()));

    } catch (NotFoundException ex) {
      String msg = String.format("Could not find batch with id %d", updateInfo.getId());
      log.error(() -> msg, ex);
      throw new BusinessException(msg, ex);
    }
  }

  private void update(BibRecordBatch batch, BatchInfo updateInfo) {
    if (updateInfo.getStatus() != null) {
      batch.setStatus(updateInfo.getStatus());
    }
    if (updateInfo.getDescription() != null) {
      batch.setDescription(updateInfo.getDescription());
    }
  }

  private BatchDeleteInfo deleteBatch(Integer batchId) {
    notNull(batchId, "Batch id must not be null to delete a batch");
    try {
      BibRecordBatch batch = remoteCataloguing.getBibRecordBatch(batchId);
      boolean deleted = false;
      if (isBatchEmpty(batch.getBibRecords())) {
        remoteCataloguing.removeBibRecordBatch(batch.getPk());
        deleted = true;
      }

      String msg = String.format("Batch %d is not empty", batch.getPk());
      return BatchDeleteInfo.builder()
          .id(batch.getPk())
          .deleted(deleted)
          .message(!deleted ? msg : "")
          .build();
    } catch (NotFoundException ex) {
      String msg = String.format("Exception getting batch with id %d", batchId);
      log.error(() -> msg, ex);
      throw new BusinessException(msg, ex);
    }
  }

  private boolean isBatchEmpty(List<BibRecord> records) {
    return records == null || records.isEmpty();
  }

  private void addBibRecord(BibRecordBatch batch, RecordInfo record) {
    BibTemplate template = getBibTemplate(record.getTemplateId());
    Set<CataloguingAction> templateActions = bibTemplateService.getCataloguingActions(template);

    try {
      OpenTitleOrder to = fulfillment.getTitleOrder(new TransactionIdentifier(record.getTitleOrderId()));
      CataloguingAction action = to.getCataloguingAction() == null ? CataloguingAction.MATCH : to.getCataloguingAction();

      if (templateActions.contains(action)) {
        createBibRecord(to, template, record, batch);
      }
    } catch (NotFoundException ex) {
      log.warn(String.format("Bib record can not be created, because title order %s could not be found.", record.getTitleOrderId()), ex);
    }
  }

  private void createBibRecord(OpenTitleOrder to, BibTemplate template, RecordInfo record, BibRecordBatch batch) {
    try {
      remoteCataloguing.createOnOrderBibRecord(to, template, titleOrderMatch(record), batch, "");
    } catch (NotFoundException | MarcException ex) {
      log.warn(() -> String.format("Exception creating bib record for title order %s", to.getOrderId().getTransactionIdValue()), ex);
    }
  }

  private TitleOrderMatch titleOrderMatch(RecordInfo record) {
    TitleOrderMatch match = new TitleOrderMatch();
    match.setProductSummary(getSummary(record));
    match.setAuthorityBibRecord(null);
    match.setCustomerBibRecord(null);
    match.setIdMatchThreshhold(record.getMatchThreshold());
    match.setProductDescriptorBibRecord(null);
    return match;
  }

  private ProductSummary getSummary(RecordInfo record) {
    return Optional.ofNullable(record)
        .filter(rec -> record.getProductIdentifier() != null)
        .map(rec -> {
          ProductSummary summary = null;
          try {
            summary = catalogue.getProductSummary(record.getProductIdentifier());
          } catch (NotFoundException ex) {
            log.warn(() -> String.format("Exception retrieving summary for ", rec));
          }
          return summary;
        })
        .orElse(null);
  }

  private BibRecordBatch getBatch(int i) {
    BibRecordBatch bibRecord = null;
    try {
      bibRecord = remoteCataloguing.getBibRecordBatch(i);
    } catch (NotFoundException ex) {
      log.warn(() -> String.format("Batch with id %d not found", i));
    }
    return bibRecord;
  }

  private BibTemplate getBibTemplate(Integer id) {
    return Optional.ofNullable(id)
        .map(i -> {
          BibTemplate t = null;
          try {
            t = bibTemplateService.getBibTemplate(id.intValue());
          } catch (NotFoundException ex) {
            log.warn(() -> String.format("Could not find bib template with id %d", id));
          }
          return t;
        })
        .orElse(null);
  }

  public Integer addToSeparateBatch(
      String customerCode,
      BibRecord bibRecord,
      List<String> separateBatchCodes,
      Set<Integer> existingBatchIds) {
    List<BibRecordBatch> batches = getBibRecordBatches(customerCode, bibRecord, existingBatchIds);
    Set<Integer> batchIds = new HashSet<>();
    for (String separateBatchCode : separateBatchCodes) {
      BatchInfo bibRecordBatch = BatchInfo.from(batches.stream().filter(b -> b.getEntryRecord().getModifiedBy().equals("lucy-api")
          && isSeparateBatch(b.getDescription(), separateBatchCode)).findFirst().orElseGet(
          () -> {
            // If can't find an existing batch, make a new one ...
            BatchInfo batchInfo = BatchInfo.builder()
                .customerCode(customerCode)
                .bibType(au.com.peterpal.lucyapi.model.BibRecordType.from(bibRecord.getBibRecordType()))
                .description(String.format("%s %s", separateBatchCode, now()))
                .build();
            return createBibRecordBatch(batchInfo, "lucy-api");
          }));

      log.debug(() -> String.format("Using BibRecordBatch: %s %s", bibRecordBatch.getId(), bibRecordBatch.getDescription()));

      // Add Full Bib Record to Batch
      try {
        remoteCataloguing.addBibRecordToBatch(bibRecordBatch.getId(), bibRecord.getPk());
      } catch (Exception e) {
        String msg = "Failed to add to batch. " + e.getMessage();
        log.error(msg, e);
        throw new BusinessException(msg);
      }
      batchIds.add(bibRecordBatch.getId());
    }
    if (batchIds.size() != 1) {
      throw new BusinessException("A full bib record must added to one batch");
    }
    return batchIds.stream().findFirst().get();

  }

  private List<BibRecordBatch> getBibRecordBatches(
      String customerCode, BibRecord bibRecord, Set<Integer> existingBatchIds) {
    if (existingBatchIds.isEmpty()) {
      return remoteCataloguing.getBibRecordBatches(
          new OrganisationIdentifier(OrganisationIdentifierTypeCode.PPLS, customerCode),
          bibRecord.getBibRecordType(),
          BibRecordBatchStatus.OPEN);

    } else {
      return existingBatchIds.stream()
          .map(
              b -> {
                try {
                  return remoteCataloguing.getBibRecordBatch(b);
                } catch (NotFoundException e) {
                  log.warn(String.format("Batch with id %d not found", b));
                  return null;
                }
              })
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
    }
  }

  public static boolean isSeparateBatch(String description, String separateBatchCode) {
    if (StringUtils.isEmpty(description)) {
      return false;
    }
    String regexPattern = String.format("^%s \\d{2}:\\d{2}:\\d{2}\\.\\d{3}$", separateBatchCode);

    Pattern pattern = Pattern.compile(regexPattern);

    Matcher matcher = pattern.matcher(description);
    return matcher.matches();
  }

}
