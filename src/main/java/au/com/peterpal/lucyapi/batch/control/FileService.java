package au.com.peterpal.lucyapi.batch.control;

import static au.com.peterpal.lucyapi.utils.Helper.notNull;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import lucy.catalogue.entity.OrganisationIdentifier;
import lucy.cataloguing.beans.RemoteCataloguing;
import lucy.cataloguing.entity.BibRecord;
import lucy.cataloguing.entity.BibRecordBatch;
import lucy.cataloguing.entity.Client;
import lucy.common.NotFoundException;
import lucy.marc.MarcException;
import org.marc4j.MarcStreamWriter;
import org.marc4j.marc.Record;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class FileService {

  private static final char UNICODE_ENCODING_SCHEME = 'a';
  private static final char MARC8_ENCODING_SCHEME = ' ';
  private static final String UTF8_ENCODING = "UTF8";

  @Value("${file.export.dir:''}")
  private String fileExportDir;

  private final RemoteCataloguing cataloguing;

  private Path fileExportDirLocation;

  private DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
  private NumberFormat batchNumberFormat;

  public FileService(RemoteCataloguing cataloguing) {
    this.cataloguing = cataloguing;

  }

  @PostConstruct
  public void initService() {
    fileExportDirLocation = Paths.get(fileExportDir).toAbsolutePath().normalize();
    batchNumberFormat = NumberFormat.getIntegerInstance();
    batchNumberFormat.setMinimumIntegerDigits(5);
    batchNumberFormat.setGroupingUsed(false);

    try {
      Files.createDirectories(this.fileExportDirLocation);
    } catch (Exception ex) {
      log.error(ex);
      throw new ResourceNotFoundException(String.format("Could not create directory %s", fileExportDirLocation));
    }
  }

  public String export(BibRecordBatch batch) throws NotFoundException, FileNotFoundException, MarcException {
    notNull(batch, "Batch must not be null");

    String fileName = getFileName(batch);
    File exportFile = new File(fileExportDir, fileName);
    char encoding = getEncoding(batch.getCustomerId());

    try (
        OutputStream output = new FileOutputStream(exportFile);
    ) {
      MarcStreamWriter writer = getMarcWriter(output, encoding);
      for (BibRecord bibRecord : batch.getBibRecords()) {
        Record marcRecord = bibRecord.getMarcRecord();

        // Ensure that the record leader reflects the actual encoding scheme being used
        if (marcRecord.getLeader().getCharCodingScheme() != encoding) {
          marcRecord.getLeader().setCharCodingScheme(encoding);
        }
        writer.write(marcRecord);
      }
    } catch (IOException ex) {
      log.warn(() -> "Exception closing output stream", ex);
    }
    return fileName;
  }

  public Resource loadFileAsResource(String fileName) {
    try {
      Path filePath = this.fileExportDirLocation.resolve(fileName).normalize();
      Resource resource = new UrlResource(filePath.toUri());
      if(resource.exists()) {
        return resource;
      } else {
        throw new ResourceNotFoundException(String.format("File not found %s",fileName));
      }

    } catch (MalformedURLException ex) {
      throw new BusinessException(String.format("File not found %s",fileName), ex);
    }
  }

  private String getFileName(BibRecordBatch batch) {
    return String.format("PPLS_%s.mrc", batchNumberFormat.format(batch.getPk()));
  }

  private char getEncoding(OrganisationIdentifier customerId) throws NotFoundException {
    Client client = cataloguing.getClient(customerId);
    char encodingScheme = UNICODE_ENCODING_SCHEME;
    if (client.getMarcCharacterEncoding() != null && !client.getMarcCharacterEncoding().isEmpty()) {
      encodingScheme = client.getMarcCharacterEncoding().charAt(0);
    }
    return encodingScheme;
  }

  private MarcStreamWriter getMarcWriter(OutputStream output, char encoding) {
    if (encoding == MARC8_ENCODING_SCHEME) {
      return new MarcStreamWriter(output);
    } else {
      return new MarcStreamWriter(output, UTF8_ENCODING);
    }
  }
}
