package au.com.peterpal.lucyapi.batch.dto;

import au.com.peterpal.lucyapi.batch.model.BatchInfo;
import java.util.Objects;
import lombok.*;
import lucy.cataloguing.codes.BibRecordBatchStatus;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Builder
public class BatchUpdateRequest {

    @NonNull
    @NotNull
    private Integer id;

    private String description;
    private BibRecordBatchStatus status;

    public static List<BatchInfo> to(List<BatchUpdateRequest> updateList) {
    return Optional.ofNullable(updateList)
        .map(List::stream)
        .orElseGet(Stream::empty)
        .map(BatchUpdateRequest::to)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
    }

    public static BatchInfo to(BatchUpdateRequest request) {
        return Optional.ofNullable(request)
            .map(update -> BatchInfo.builder()
                .id(request.id)
                .status(request.status)
                .description(request.description)
                .build()
            )
            .orElse(null);
    }
}
