package au.com.peterpal.lucyapi.batch.model;

import au.com.peterpal.lucyapi.model.BibRecordType;
import au.com.peterpal.lucyapi.persistence.cataloguing.model.BiBRecordBatchInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.validation.constraints.NotNull;

import lombok.*;
import lucy.cataloguing.codes.BibRecordBatchStatus;
import lucy.cataloguing.entity.BibRecordBatch;

@Data
@Builder(toBuilder = true)
public class BatchInfo {
  private Integer id;

  private String customerCode;

  @NotNull
  private BibRecordType bibType;

  private String description;

  private List<Integer> bibRecordIdList;

  private BibRecordBatchStatus status;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate dateLastModified;

  private String modifiedBy;

  public static List<BatchInfo> from(List<BiBRecordBatchInfo> batchList) {
    return Optional.ofNullable(batchList)
        .map(List::stream)
        .orElseGet(Stream::empty)
        .map(BatchInfo::from)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  public static BatchInfo from(BiBRecordBatchInfo batch) {
    return Optional.ofNullable(batch)
        .map(b -> BatchInfo.builder()
            .id(batch.getId())
            .status(batch.getStatus())
            .customerCode(batch.getCustomerCode())
            .description(batch.getDescription())
            .bibType(BibRecordType.from(batch.getBibType()))
            .bibRecordIdList(
                Optional.ofNullable(b.getBibRecords())
                    .map(List::stream)
                    .orElseGet(Stream::empty)
                    .map(bb -> new Integer(bb.getId().getBibId()))
                    .collect(Collectors.toList())
            )
            .modifiedBy(b.getModifiedBy())
            .dateLastModified(b.getDateModified().toLocalDate())
            .build())
        .orElse(null);
  }

  public static BatchInfo from(BibRecordBatch batch) {
    return Optional.ofNullable(batch)
        .map(b -> BatchInfo.builder()
            .id(batch.getPk())
            .status(batch.getStatus())
            .customerCode(batch.getCustomerId().getOrganisationIdValue())
            .description(batch.getDescription())
            .bibType(BibRecordType.from(batch.getType()))
            .bibRecordIdList(
                Optional.ofNullable(b.getBibRecords())
                .map(List::stream)
                .orElseGet(Stream::empty)
                .map(br -> new Integer(br.getPk()))
                .collect(Collectors.toList())
            )
            .modifiedBy(b.getLastModifiedRecord().getModifiedBy())
            .dateLastModified(from(b.getLastModifiedRecord().getDateModified()))
            .build())
        .orElse(null);
  }

  private static LocalDate from(Date date) {
    return Optional.ofNullable(date)
        .map(d -> d.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
        .orElse(null);
  }

  public void setStatus(BibRecordBatchStatus status){
    this.status = status;
  }
}
