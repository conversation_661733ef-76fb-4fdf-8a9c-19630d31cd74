package au.com.peterpal.lucyapi.batch.dto;

import au.com.peterpal.lucyapi.batch.model.BatchStatus;
import au.com.peterpal.lucyapi.model.BibRecordType;
import javax.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;

@Value
@Builder
@ToString
public class SearchParams {

  @NotBlank(message = "must not be blank")
  private String customerCode;

  private BibRecordType bibRecordType;
  private BatchStatus status;
}
