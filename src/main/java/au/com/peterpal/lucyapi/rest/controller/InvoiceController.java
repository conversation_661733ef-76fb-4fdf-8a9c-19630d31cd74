package au.com.peterpal.lucyapi.rest.controller;

import static java.lang.String.format;

import au.com.peterpal.lucyapi.core.service.InvoiceService;
import au.com.peterpal.lucyapi.model.InvoiceCompareInfo;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Log4j2
@RestController
@RequestMapping("/api/invoice")
@SecurityRequirement(name = "BearerAuth")
public class InvoiceController {

  private final InvoiceService invoiceService;

  public InvoiceController(InvoiceService invoiceService) {
    this.invoiceService = invoiceService;
  }

  @GetMapping("/compare-info/{ids}")
  public List<InvoiceCompareInfo> getCompareInfo(@PathVariable("ids") String ids) {
    log.debug(() -> format("Requesting compare information for invoice ids: %s", ids));

    return invoiceService.getInvoiceCompareInfo(
        Stream.of(ids.split(","))
            .map(String::trim)
            .collect(Collectors.toList()));
  }

}
