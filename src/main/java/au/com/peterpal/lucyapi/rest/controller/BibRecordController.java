package au.com.peterpal.lucyapi.rest.controller;

import static java.lang.String.format;
import static org.springframework.util.StringUtils.hasText;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.core.service.BibRecordService;
import au.com.peterpal.lucyapi.model.BibRecord;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import lucy.common.NotFoundException;
import lucy.marc.MarcException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

@Log4j2
@RestController
@RequestMapping("/api/bibrecords")
@SecurityRequirement(name = "BearerAuth")
public class BibRecordController {

  private BibRecordService cataloguing;

  public BibRecordController(BibRecordService cataloguing) {
    this.cataloguing = cataloguing;
  }

  /** curl -X GET http://127.0.0.1:8080/lucy/api/bibrecords/498753 */
  @GetMapping("/{bib_record_id}")
  //@ApiOperation(value = "/{bib_record_id}", notes = "")
  public BibRecord getBibRecord(@PathVariable("bib_record_id")String bibRecordId) {
    log.debug(() -> format("Requesting bibRecordId: %s", bibRecordId));
    try {
      return cataloguing.getBibRecord(bibRecordId);
    } catch (NotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    }
  }

  /** curl -X GET http://127.0.0.1:8080/lucy/api/bibrecords/498753/marc */
  @GetMapping("/{bibRecordId}/marc")
  //@ApiOperation(value = "/{bibRecordId}/marc", notes = "")
  public String getBibRecordMarcInJson(@PathVariable("bibRecordId")String bibRecordId) {
    log.debug(() -> format("Requesting MARC record for bibRecordId: %s", bibRecordId));

    try {
      return cataloguing.getBibRecordMarcInJson(bibRecordId);
    } catch (NotFoundException ex) {
      String msg = String.format("Could not find bib record with id %s", bibRecordId);
      if (ex != null && hasText(ex.getMessage())) {
        msg = ex.getMessage();
      }
      throw new ResourceNotFoundException(msg);
    } catch (MarcException ex) {
      throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "MARC exception", ex);
    }
  }


}
