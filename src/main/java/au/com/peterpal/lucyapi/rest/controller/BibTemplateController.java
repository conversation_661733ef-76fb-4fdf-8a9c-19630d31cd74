package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.BibTemplateService;
import au.com.peterpal.lucyapi.model.BibCollectionType;
import au.com.peterpal.lucyapi.model.BibTemplateType;
import au.com.peterpal.lucyapi.rest.model.BibTemplateInfo;
import java.util.List;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;



@Log4j2
@RequestMapping("/api/bibtemplates")
@RestController
@SecurityRequirement(name = "BearerAuth")
public class BibTemplateController {

  private final BibTemplateService bibTemplateService;

  public BibTemplateController(BibTemplateService bibTemplateService) {
    this.bibTemplateService = bibTemplateService;
  }

  @GetMapping("/{bib_template_id}")
  public BibTemplateInfo getBibTemplate(@PathVariable("bib_template_id") Integer bibTemplateId ) {
    return null;
  }

  @GetMapping("/search")
  @Produces(MediaType.APPLICATION_JSON)
  public List<BibTemplateInfo> search(
      @RequestParam(required = false) String customerCode,
      @RequestParam(required = false)BibCollectionType collectionType,
      @RequestParam(required = false)BibTemplateType templateType ) {

    return BibTemplateInfo.from(bibTemplateService.getBibTemplates(customerCode, collectionType, templateType));
  }
}
