package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.SupplyErrorAdvicePrinterService;
import au.com.peterpal.lucyapi.model.SupplyErrorAdvicePrintMessage;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Log4j2
@RestController
@RequestMapping("/api/receiving-slip")
@RequiredArgsConstructor
@SecurityRequirement(name = "BearerAuth")
public class ReceivingSlipController {

  private final SupplyErrorAdvicePrinterService supplyErrorAdvicePrinterService;

  @PostMapping("supply-error-advice/print")
  public ResponseEntity<String> printSupplyErrorAdviceSlip(
      @RequestBody @Valid SupplyErrorAdvicePrintMessage request) {
    HttpStatus responseStatus = HttpStatus.OK;
    String message = null;
    try {
      supplyErrorAdvicePrinterService.handle(request);
    } catch (Exception e) {
      responseStatus = HttpStatus.BAD_REQUEST;
      message = e.getMessage();
    }

    return ResponseEntity.status(responseStatus).body(message);
  }
}
