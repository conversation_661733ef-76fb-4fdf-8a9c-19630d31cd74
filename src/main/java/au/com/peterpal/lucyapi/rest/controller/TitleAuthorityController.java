package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.TitleAuthorityService;
import java.util.List;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lucy.catalogue.codes.ProductIdentifierTypeCode;
import lucy.cataloguing.entity.TitleAuthority;
import lucy.common.NotFoundException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/titleauthorities")
@SecurityRequirement(name = "BearerAuth")
public class TitleAuthorityController {

  private final TitleAuthorityService service;

  public TitleAuthorityController(TitleAuthorityService service) {
    this.service = service;
  }

  /**
   * curl -X GET http://127.0.0.1:8080/lucy/api/titleauthorities?productIdType={productIdType}&productIdValue={productIdValue}&productIdSource={productIdSource}
   * ex. curl -X GET http://127.0.0.1:8080/lucy/api/titleauthorities?productIdType=EAN&productIdValue=9781593078638
   *
   * @param productIdType
   * @param productIdValue
   * @param productIdSource
   * @return
   */
  @GetMapping
  @Produces(MediaType.APPLICATION_JSON)
  public Response getTitleAuthorities(
      @RequestParam("productIdType") ProductIdentifierTypeCode productIdType,
      @QueryParam("productIdValue") String productIdValue,
      @QueryParam("productIdSource") String productIdSource) {
    Response response;
    try {
      List<TitleAuthority> matchingTitleAuthorities =
          service.find(productIdType, productIdValue, productIdSource);

      response = Response.ok().entity(matchingTitleAuthorities).build();

    } catch (NotFoundException ex) {
      response = Response.status(Response.Status.NOT_FOUND).build();
    }
    return response;
  }
}
