package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.core.service.message.IncomingMessageProcessor;
import au.com.peterpal.lucyapi.persistence.lucyapi.IncomingMessageRepository;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.IncomingMessage;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.IncomingMessageStatus;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/admin/incoming-message/")
@SecurityRequirement(name = "BearerAuth")
public class IncomingMessageAdminController {
  private final IncomingMessageRepository incomingMessageRepository;
  private final IncomingMessageProcessor incomingMessageProcessor;

  public IncomingMessageAdminController(
      IncomingMessageRepository incomingMessageRepository,
      IncomingMessageProcessor incomingMessageProcessor) {
    this.incomingMessageRepository = incomingMessageRepository;
    this.incomingMessageProcessor = incomingMessageProcessor;
  }

  @GetMapping
  public List<IncomingMessage> findByStatus(
      @RequestParam(required = false) IncomingMessageStatus status) {
    return incomingMessageRepository.findByStatus(status);
  }

  @GetMapping(path = "{id}")
  public IncomingMessage findById(@PathVariable String id) {
    return incomingMessageRepository
        .findById(id)
        .orElseThrow(() -> new ResourceNotFoundException(IncomingMessage.class, id));
  }

  @PostMapping
  @ResponseStatus(HttpStatus.OK)
  public String reprocessMessage(@RequestBody @Valid String id) {
    return incomingMessageProcessor.resendMessageToJMSQueue(id);
  }
}
