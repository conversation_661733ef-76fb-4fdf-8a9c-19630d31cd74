package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.lucyapi.core.service.orchestration.SpydusEdiOrchestratorService;
import au.com.peterpal.lucyapi.core.service.orchestration.control.CheckBarcodeService;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.PartiallyInvoicedWorkOrderMessage;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.ProcessingResult;
import au.com.peterpal.lucyapi.core.service.orchestration.dto.WorkflowStepStatus;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/spydus-edi-accessioning")
@Log4j2
@RequiredArgsConstructor
@SecurityRequirement(name = "BearerAuth")
public class SpydusEdiAccessioningController {

  private final SpydusEdiOrchestratorService orchestratorService;
  private final CheckBarcodeService checkBarcodeService;

  @Transactional
  @PutMapping("/{spydusOrchestrationId}/reprocess")
  @ResponseStatus(HttpStatus.OK)
  public WorkflowStepStatus reprocess(@PathVariable UUID spydusOrchestrationId) {
    log.debug(
        "Received request to reprocess spydus edi accessioning automation for spydusOrchestrationId: {}",
        spydusOrchestrationId);
    orchestratorService.initReprocess(spydusOrchestrationId);
    return orchestratorService.reprocess(spydusOrchestrationId);
  }

  @Transactional
  @PutMapping("/{spydusOrchestrationId}/stop")
  @ResponseStatus(HttpStatus.OK)
  public WorkflowStepStatus stop(@PathVariable UUID spydusOrchestrationId) {
    log.debug(
        "Received request to stop spydus edi accessioning automation for spydusOrchestrationId: {}",
        spydusOrchestrationId);
    return orchestratorService.stop(spydusOrchestrationId);
  }

  @Transactional
  @PutMapping("/{spydusOrchestrationId}/skip-step")
  @ResponseStatus(HttpStatus.OK)
  public WorkflowStepStatus skipStep(@PathVariable UUID spydusOrchestrationId) {
    log.debug(
        "Received request to skip step spydus edi accessioning automation for spydusOrchestrationId: {}",
        spydusOrchestrationId);
    orchestratorService.initSkipStepProcess(spydusOrchestrationId);
    return orchestratorService.skipStep(spydusOrchestrationId);
  }

  @Transactional
  @GetMapping("/check-barcodes")
  public ProcessingResult checkBarcodes(
      @RequestParam(required = false) UUID spydusOrchestrationId,
      @RequestParam(required = false) Set<String> invoiceNumbers,
      @RequestParam(required = false) Set<Integer> batchIds) {
    return checkBarcodeService.checkBarcodes(spydusOrchestrationId, invoiceNumbers, batchIds);
  }

  @Transactional
  @PutMapping("/manual-trigger-orchestration")
  public UUID triggerOrchestration(@RequestBody PartiallyInvoicedWorkOrderMessage message) {
    return orchestratorService
        .triggerSpydusEdiOrchestration(message)
        .orElseThrow(
            () -> new BusinessException("Cannot process spydus edi accessioning automation"));
  }

  @Transactional
  @PutMapping("/fix-barcodes-mismatch")
  public String fixBarcodesMismatch(@RequestParam UUID spydusOrchestrationId) {
    return checkBarcodeService.fixBarcodesMismatch(spydusOrchestrationId);
  }
}
