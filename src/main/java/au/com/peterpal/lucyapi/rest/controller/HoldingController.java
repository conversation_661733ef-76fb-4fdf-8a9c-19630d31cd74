package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.lucyapi.core.service.*;
import au.com.peterpal.lucyapi.model.AddCopiesResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;
import javax.ws.rs.core.Response;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.entity.Label;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.web.bind.annotation.*;

@Log4j2
@RestController
@RequestMapping("/api/holdings")
@SecurityRequirement(name = "BearerAuth")
public class HoldingController {

  private final TitleOrderService titleOrderService;
  private final HoldingService holdingService;
  private final ReceiptPrinterService receiptPrinterService;
  private final ClientService clientService;
  private final WorkOrderService workOrderService;
  private final CataloguingAcquisitionService cataloguingAcquisitionService;

  public HoldingController(
      TitleOrderService titleOrderService,
      HoldingService holdingService,
      ReceiptPrinterService receiptPrinterService,
      ClientService clientService,
      WorkOrderService workOrderService,
      CataloguingAcquisitionService cataloguingAcquisitionService) {
    this.titleOrderService = titleOrderService;
    this.holdingService = holdingService;
    this.receiptPrinterService = receiptPrinterService;
    this.clientService = clientService;
    this.workOrderService = workOrderService;
    this.cataloguingAcquisitionService = cataloguingAcquisitionService;
  }

  @PostMapping("/holding/{workOrderNumber}/completeAccessioningTask")
  public Response completeAccessioningTask(
      @RequestHeader(value = "current-user", defaultValue = "", required = false)
          String currentUser,
      @PathVariable String workOrderNumber,
      @RequestParam String printer)
      throws Exception {
    log.debug(
        () -> String.format("Completing Accessioning Task for work order: %s", workOrderNumber));

    workOrderService.completeAccessioningTask(workOrderNumber, currentUser, printer);
    return Response.ok().build();
  }

    @PostMapping("/holding/{workOrderNumber}/createFullBibRecord")
    public Response createFullBibRecord(@PathVariable String workOrderNumber) {
        log.debug(() -> String.format("Creating Full Bib Record for work order: %s", workOrderNumber));
        holdingService.createFullBibRecordWithStoreBarcodes(workOrderNumber, null, null, false);
        return Response.ok().build();
    }

  @PostMapping("/holding/{workOrderNumber}/printLabel")
  public Response printLabel(
      @PathVariable String workOrderNumber, @RequestParam(required = false) String labelName) {
    log.trace("print label for work order: {}", workOrderNumber);

    holdingService.printLabel(workOrderNumber, labelName);

    return Response.ok().build();
  }

  @PostMapping("/holding/{workOrderNumber}/addCopies")
  public Response addCopies(
      @PathVariable String workOrderNumber,
      @RequestParam(value = "barcodes") List<String> barcodes,
      @RequestParam String printer,
      @RequestParam(required = false) String labelName) {
    log.debug(() -> String.format("Adding Copies for work order: %s", workOrderNumber));

    validateAddCopies(workOrderNumber, barcodes);

    try {
      AddCopiesResponse addCopiesResponse =
          holdingService.addCopies(workOrderNumber, barcodes, labelName);
      receiptPrinterService.printBranchSlips(printer, workOrderNumber, barcodes);
      holdingService.printLabel(
          addCopiesResponse.getAllocationTemplatePk(), addCopiesResponse.getPrintableLabelMap());
    } catch (Exception e) {
      log.warn("Exception: {}", e.toString(), e);
      cataloguingAcquisitionService.removeAcquisitions(workOrderNumber);
      throw new BusinessException(String.format("Failed to add copies. %s", e.getMessage()));
    }


    return Response.ok().build();
  }

  private void validateAddCopies(String workOrderNumber, List<String> barcodes) {
    // Get Barcode Number Pattern
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    String barcodeNumberPattern = clientService.getClient(customerCode).getBarcodeNumberPattern();

    if (barcodeNumberPattern == null) {
      throw new BusinessException("Barcode Number Pattern is not set for customer " + customerCode);
    }
    // Check Barcodes against Pattern
    List<String> invalidBarcodes =
        barcodes.stream()
            .filter(b -> !b.matches(barcodeNumberPattern))
            .collect(Collectors.toList());
    if (!invalidBarcodes.isEmpty()) {
      String message =
          String.format(
              "Barcode %s invalid for pattern %s",
              String.join(", ", invalidBarcodes), barcodeNumberPattern);
      log.warn(message);
      throw new BusinessException(message);
    }
  }

  @PostMapping("/holding/{workOrderNumber}/printBranchSlips")
  public Response printBranchSlips(
      @PathVariable String workOrderNumber, @RequestParam String printer)
      throws IOException, URISyntaxException {
    receiptPrinterService.printBranchSlips(printer, workOrderNumber);

    return Response.ok().build();
  }

  @PostMapping("/holding/{workOrderNumber}")
  public Response addHolding(@PathVariable String workOrderNumber) {
    log.debug(() -> String.format("Adding Holding for: %s", workOrderNumber));
    Response response;
    try {
      holdingService.addHolding(workOrderNumber);
      response = Response.ok().build();
    } catch (BusinessException | ResourceNotFoundException businessException) {
      throw businessException;
    } catch (Exception ex) {
      log.warn(ExceptionUtils.getStackTrace(ex));
      response = Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
    return response;
  }

  @PostMapping("/holding/{workOrderNumber}/validate-accessioning-task")
  public Response validateWorkOrderAccessioningTask(@PathVariable String workOrderNumber) {
    try {
      workOrderService.extractAndValidateWorkOrderAccessioningTask(workOrderNumber);
    } catch (BusinessException | ResourceNotFoundException ex) {
      throw ex;
    } catch (Exception ex) {
      log.warn("Exception: " + ex.getMessage());
      log.warn(ExceptionUtils.getStackTrace(ex));
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(ex.getMessage()).build();
    }
    return Response.ok().build();
  }

  /**
   * This endpoint is used to accession a work order. It will first validate if the work order is
   * ready for accessioning. If it is, it will create a holding for the work order and add copies to
   * the holding. After that, it will print the branch slips. Finally, it will create a full bib
   * record for the holding and complete the accessioning task.
   *
   * @param currentUser the user who is performing the accessioning
   * @param workOrderNumber the work order number that is being accessioned
   * @param barcodes the barcodes of the copies that are being added to the holding
   * @param printer the printer name that is used to print the branch slips
   * @param labelName the label name that is used to print the branch slips
   * @return a response object which contains the status of the accessioning
   */
  @PostMapping("/holding/{workOrderNumber}/accession")
  public Response accession(
      @RequestHeader(value = "current-user", defaultValue = "", required = false)
          String currentUser,
      @PathVariable String workOrderNumber,
      @RequestParam(value = "barcodes") List<String> barcodes,
      @RequestParam String printer,
      @RequestParam(required = false) String labelName) {
    log.debug(() -> String.format("Accessioning work order %s", workOrderNumber));

    Response response;
    try {
      workOrderService.extractAndValidateWorkOrderAccessioningTask(workOrderNumber);
      validateAddCopies(workOrderNumber, barcodes);
      try {
        response = addHolding(workOrderNumber);
        if (response.getStatus() != Response.Status.OK.getStatusCode()) return response;
      } catch (BusinessException e) {
        throw new BusinessException(String.format("Failed to create holding. %s", e.getMessage()));
      }

      try {
        AddCopiesResponse addCopiesResponse =
            holdingService.addCopies(workOrderNumber, barcodes, labelName);
        receiptPrinterService.printBranchSlips(
            printer,
            workOrderNumber,
            barcodes,
            addCopiesResponse.getBarcodesAndDestinationsAdded());
        holdingService.printLabel(
            addCopiesResponse.getAllocationTemplatePk(), addCopiesResponse.getPrintableLabelMap());
      } catch (Exception e) {
        log.warn("Exception: {}", e.toString(), e);
        cataloguingAcquisitionService.removeAcquisitions(workOrderNumber);
        throw new BusinessException(String.format("Failed to add copies. %s", e.getMessage()));
      }

      if (holdingService.isSpydusEdiCustomer(workOrderNumber)) {
        log.info("Skip to create full bib record due to customer is spydus edi");
      } else {
        holdingService.createFullBibRecordWithStoreBarcodes(workOrderNumber, null, null, false);
      }

      response = completeAccessioningTask(currentUser, workOrderNumber, printer);
      if (response.getStatus() != Response.Status.OK.getStatusCode()) return response;

      response = Response.ok().build();
    } catch (BusinessException | ResourceNotFoundException ex) {
      throw ex;
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      if (ex.getRawStatusCode() == 405) {
        response = Response.status(Response.Status.NOT_FOUND).entity(workOrderNumber).build();
      } else {
        response =
            Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(ex.getMessage()).build();
      }
    } catch (Exception ex) {
      log.warn(ExceptionUtils.getStackTrace(ex));
      response =
          Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(ex.getMessage()).build();
    }

    return response;
  }

  @GetMapping("/holding/{workOrderNumber}/barcodePattern")
  @Operation(summary = "Get barcode pattern and convert it to support handling in Javascript")
  public String getBarcodePattern(@PathVariable String workOrderNumber) {
    String customerCode = titleOrderService.getCustomerCode(workOrderNumber);
    String barcodeNumberPattern = clientService.getClient(customerCode).getBarcodeNumberPattern();
    return StringUtils.replace(barcodeNumberPattern, "\\", "\\\\");
  }

  @GetMapping("/holding/{workOrderNumber}/labels")
  @Operation(summary = "Get all labels for the bib template")
  public List<Label> getLabels(@PathVariable String workOrderNumber) {
    return holdingService.getLabels(workOrderNumber);
  }
}
