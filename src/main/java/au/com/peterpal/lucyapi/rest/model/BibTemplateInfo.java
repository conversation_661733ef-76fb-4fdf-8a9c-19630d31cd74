package au.com.peterpal.lucyapi.rest.model;

import au.com.peterpal.lucyapi.model.BibCollectionType;
import au.com.peterpal.lucyapi.model.BibTemplateType;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.entity.BibTemplate;

@Builder
@Value
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@Log4j2
public class BibTemplateInfo {
  private Integer id;
  private String customerCode;
  private BibCollectionType collectionType;
  private BibTemplateType templateType;
  private String name;
  private Integer rank;
  private Integer bibRecordId;

  public static List<BibTemplateInfo> from(List<BibTemplate> bibTemplates) {
    return Optional.ofNullable(bibTemplates)
      .map(List::stream)
      .orElseGet(Stream::empty)
      .map(BibTemplateInfo::buildHelper)
      .collect(Collectors.toList());
  }

  private static BibTemplateInfo buildHelper(BibTemplate template) {
    return BibTemplateInfo.builder()
        .id(template.getPk())
        .customerCode(template.getCustomerId().getOrganisationIdValue())
        .collectionType(BibCollectionType.valueOf(template.getBibCollectionType().name()))
        .templateType(BibTemplateType.valueOf(template.getBibTemplateType().name()))
        .name(template.getName())
        .rank(template.getRank())
        .bibRecordId(template.getBibRecord().getPk())
        .build();
  }
}
