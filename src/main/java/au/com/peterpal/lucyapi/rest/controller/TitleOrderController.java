package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.TitleOrderService;
import au.com.peterpal.lucyapi.core.service.retrieve.RetrieveService;
import au.com.peterpal.lucyapi.model.*;
import au.com.peterpal.lucyapi.rest.model.MatchRequest;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import au.com.peterpal.lucyapi.rest.model.UpdateStateInfo;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import lucy.common.NotFoundException;
import lucy.fulfillment.codes.CataloguingAction;
import lucy.fulfillment.entity.OpenTitleOrder;
import lucy.fulfillment.entity.TitleOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Log4j2
@RestController
@RequestMapping("/api/orders")
@SecurityRequirement(name = "BearerAuth")
public class TitleOrderController {

  private final TitleOrderService titleOrderService;
  private final RetrieveService retrieveService;
  private final MessageChannel addToPurchaseOrderChannel;

  public TitleOrderController(TitleOrderService service, RetrieveService retrieveService, MessageChannel addToPurchaseOrderChannel) {
    this.titleOrderService = service;
    this.retrieveService = retrieveService;
    this.addToPurchaseOrderChannel = addToPurchaseOrderChannel;
  }

  @PutMapping
  @Produces(MediaType.APPLICATION_JSON)
  @ResponseBody
  public TitleOrderInfo addTitleOrder(@RequestBody TitleOrderInfo titleOrderInfo) {
    return titleOrderService.add(titleOrderInfo);
  }

  @GetMapping("/{orderNumber}")
  @Produces(MediaType.APPLICATION_JSON)
  public TitleOrderInfo getTitleOrder(@PathVariable("workOrderNumber") String workOrderNumber) {
    return titleOrderService.getTitleOrder(workOrderNumber, false);
  }

  @GetMapping
  @Produces(MediaType.APPLICATION_JSON)
  public List<TitleOrder> getTitleOrders(@RequestParam String orderNumbers) {
    return titleOrderService.getTitleOrders(orderNumbers);
  }

  @PostMapping(path = "/{orderNumber}")
  @Produces(MediaType.APPLICATION_JSON)
  public Response sendOrderToLms(@PathVariable("orderNumber") String orderNumber) {
    Response response;
    try {
      titleOrderService.sendTitleOrder(orderNumber);
      response = Response
              .status(Response.Status.OK)
              .build();
    } catch (Exception ex) {
      log.error("Error sending order to LMS", ex);
      response = Response
              .status(Response.Status.INTERNAL_SERVER_ERROR)
              .entity(ex.getMessage())
              .build();
    }
    return response;
  }

  @PutMapping(path = "/{orderNumber}/{bibRecordId}")
  public Response sendOrderToLms(
          @PathVariable("orderNumber") String orderNumber,
          @PathVariable("bibRecordId") Integer bibRecordId) {
    Response response;
    try {
      titleOrderService.updateBibRecordId(orderNumber, bibRecordId);
      titleOrderService.sendTitleOrder(orderNumber);
      response = Response
              .status(Response.Status.OK)
              .build();
    } catch (Exception ex) {
      log.error("Error updating and sending order to LMS", ex);
      response = Response
              .status(Response.Status.INTERNAL_SERVER_ERROR)
              .entity(ex.getMessage())
              .build();
    }
    return response;
  }

  @GetMapping("/search")
  public Page<TitleOrderInfo> search(
          @RequestParam String customer,
          @RequestParam(required = false) CataloguingActionValue cataloguingAction,
          @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate dateFrom,
          @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate dateTo,
          @PageableDefault(size = 20) Pageable pageRequest) {

    List<OpenTitleOrder> otoList = titleOrderService
            .getTitleOrders(customer, fromValue(cataloguingAction), dateFrom, dateTo);

    int start = (int) pageRequest.getOffset();
    int end = ((start + pageRequest.getPageSize()) > otoList.size()
            ? otoList.size()
            : (start + pageRequest.getPageSize()));

    return new PageImpl<>(TitleOrderInfo.from(otoList.subList(start, end)), pageRequest, otoList.size());
  }

  @GetMapping("/barchart")
  public List<BarchartInfo> getBarchartInfo(
          @RequestParam String customer,
          @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate dateFrom,
          @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate dateTo) {
    return BarchartInfo.from(titleOrderService.getTitleOrderActionInfo(customer, dateFrom, dateTo));
  }

  @PostMapping("/match")
  @Produces(MediaType.APPLICATION_JSON)
  @ResponseBody
  public List<MatchInfo> match(@RequestBody MatchRequest request) throws NotFoundException {
    return titleOrderService.match(request.getTitleOrderNumbers(), request.getBibTemplateId());
  }

  @PostMapping("/retrieve/{customerCode}")
  @ResponseStatus(HttpStatus.OK)
  public void retrieve(
          @RequestBody List<String> titleOrderNumbers,
          @PathVariable("customerCode") String customerCode) throws NotFoundException {

    retrieveService.retrieve(customerCode, titleOrderNumbers);
  }

  @GetMapping("/searchByStatus")
  public Page<TitleOrderInfo> searchByStatus(
          @RequestParam String customer,
          @RequestParam(required = false) List<String> cataloguingStates,
          @RequestParam(required = false) String source,
          @RequestParam(required = false) String fundCode,
          @RequestParam(required = false) String media,
          @RequestParam(required = false) String category2,
          @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate dateFrom,
          @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate dateTo,
          @PageableDefault(size = 20) Pageable pageRequest) {

    SearchCriteria serarchCriteria = SearchCriteria.builder()
            .cataloguingStates(cataloguingStates)
            .source(source)
            .fundCode(fundCode)
            .media(media)
            .category2(category2)
            .build();

    List<TitleOrderInfo> titleOrders =
            titleOrderService.getTitleOrdersByState(customer, serarchCriteria, dateFrom, dateTo);

    int start = (int) pageRequest.getOffset();
    int end = (Math.min((start + pageRequest.getPageSize()), titleOrders.size()));
    return new PageImpl<>(titleOrders.subList(start, end), pageRequest, titleOrders.size());
  }

  @GetMapping("/search-by-status-all")
  public List<TitleOrderInfo> searchByStatusAll(
          @RequestParam String customer,
          @RequestParam(required = false) List<String> cataloguingStates,
          @RequestParam(required = false) String source,
          @RequestParam(required = false) String fundCode,
          @RequestParam(required = false) String media,
          @RequestParam(required = false) String category2,
          @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate dateFrom,
          @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate dateTo ) {

    SearchCriteria serarchCriteria = SearchCriteria.builder()
            .cataloguingStates(cataloguingStates)
            .source(source)
            .fundCode(fundCode)
            .media(media)
            .category2(category2)
            .build();

    return titleOrderService.getTitleOrdersByState(customer, serarchCriteria, dateFrom, dateTo);
  }

  @PatchMapping("/{state}")
  @Produces(MediaType.APPLICATION_JSON)
  public List<UpdateStateInfo> updateState(@PathVariable("state") CataloguingState state, @RequestBody List<String> titleOrderNumbers) {
    log.debug(() -> String.format("Received update state to %s for title orders %s", state, titleOrderNumbers));

    return UpdateStateInfo.from(titleOrderService.updateState(state, titleOrderNumbers));
  }

  @PostMapping("/match-all")
  @ResponseStatus(HttpStatus.OK)
  public List<MatchInfo> searchAndMatchAll(
          @RequestParam @NotBlank String customerCode,
          @RequestParam @NotNull Integer bibTemplateId) throws NotFoundException {
    return titleOrderService.match(customerCode, bibTemplateId);
  }

  @PostMapping("/retrieve-all")
  @ResponseStatus(HttpStatus.OK)
  public void retrieveAll(@RequestParam @NotBlank String customerCode) {
    retrieveService.retrieve(customerCode);
  }

  @PostMapping(path = "/add-to-purchase-order")
  @ResponseStatus(HttpStatus.OK)
  @Operation(summary = "Process a list of title orders. Add each one to an existing purchase order or create a new one.")
  public void processTitleOrders(@RequestBody @NonNull List<String> titleOrders) {
    addToPurchaseOrderChannel.send(MessageBuilder.withPayload(titleOrders).build());
  }

  private CataloguingAction fromValue(CataloguingActionValue value) {
    CataloguingAction result = null;
    if (value != null) {
      result = CataloguingAction.values()[value.ordinal()];
    }
    return result;
  }
}
