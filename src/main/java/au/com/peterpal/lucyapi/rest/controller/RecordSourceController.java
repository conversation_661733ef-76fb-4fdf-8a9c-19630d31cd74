package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.RecordSourceService;
import java.util.List;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lucy.cataloguing.entity.RecordSource;
import lucy.common.NotFoundException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/recordsources")
@SecurityRequirement(name = "BearerAuth")
public class RecordSourceController {

  private final RecordSourceService service;

  public RecordSourceController(RecordSourceService service) {
    this.service = service;
  }

  /**
   * curl -X GET http://127.0.0.1:8080/lucy/api/recordsources
   *
   * @return all record sources
   */
  @GetMapping
  @Produces(MediaType.APPLICATION_JSON)
  public Response getRecordSources() {
    Response response;
    try {
      List<RecordSource> recordSources = service.getRecordSources();
      response = Response.ok().entity(recordSources).build();
    } catch (Exception ex) {
      response = Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
    return response;
  }

  /**
   * curl -X GET http://127.0.0.1:8080/lucy/api/recordsources/{recordSourceId}
   *
   * @param recordSourceId
   * @return
   */
  @GetMapping(path = "/{recordSourceId}")
  @Produces(MediaType.APPLICATION_JSON)
  public Response getRecordSource(@PathVariable("recordSourceId") int recordSourceId) {
    Response response;
    try {
      RecordSource recordSource = service.getRecordSource(recordSourceId);
      response = Response.ok().entity(recordSource).build();
    } catch (NotFoundException ex) {
      response = Response.status(Response.Status.NOT_FOUND).build();
    }
    return response;
  }

  /**
   * curl -X GET http://127.0.0.1:8080/lucy/api/recordsources/{customerCode}/internal
   *
   * @param customerCode
   * @return
   */
  @GetMapping(path = "/{customerCode}/internal")
  @Produces(MediaType.APPLICATION_JSON)
  public Response getClientInternalRecordSources(@PathVariable("customerCode") String customerCode) {
    Response response;
    try {
      RecordSource recordSource = service.getClientInternalRecordSources(customerCode);
      response = Response.ok().entity(recordSource).build();
    } catch (NotFoundException ex) {
      response = Response.status(Response.Status.NOT_FOUND).build();
    }
    return response;
  }


  @GetMapping(path = "/{customerCode}/external")
  @Produces(MediaType.APPLICATION_JSON)
  public Response getClientExternalRecordSources(@PathVariable("customerCode") String customerCode) {
    Response response;
    try {
      List<RecordSource> recordSources = service.getClientExternalRecordSources(customerCode);
      response = Response.ok().entity(recordSources).build();
    } catch (Exception ex) {
      response = Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
    return response;
  }
}
