package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.BarcodeService;
import au.com.peterpal.lucyapi.core.service.WorkOrderService;
import au.com.peterpal.lucyapi.model.BarcodeInfo;
import java.util.List;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import lucy.common.NotFoundException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Log4j2
@RestController
@RequestMapping("/api/admin/")
@SecurityRequirement(name = "BearerAuth")
public class CataloguingAdminController {

  private final BarcodeService barcodeService;

  private final WorkOrderService workOrderService;

  public CataloguingAdminController(
      BarcodeService barcodeService, WorkOrderService workOrderService) {
    this.barcodeService = barcodeService;
    this.workOrderService = workOrderService;
  }

  @GetMapping(path = "barcodes/find-by-work-order-number/{workOrderNumber}")
  public List<BarcodeInfo> findByWorkOrderNumber(@PathVariable String workOrderNumber)
      throws NotFoundException {
    return barcodeService.getBarcodeInfo(workOrderNumber);
  }

  @GetMapping(path = "work-order-number")
  public List<String> getWorkorderNumbersByBarcode(
      @RequestParam String barcode, @RequestParam String customerId) {
    return workOrderService.getWorkOrderNumberForBarcode(barcode, customerId);
  }
}
