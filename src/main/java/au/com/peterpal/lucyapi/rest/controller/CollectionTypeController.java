package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.CollectionTypeService;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.CollectionTypeByFund;
import au.com.peterpal.lucyapi.rest.model.CollectionTypeResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.Optional;

@Log4j2
@RestController
@RequestMapping("/api/collectiontype")
@SecurityRequirement(name = "BearerAuth")
public class CollectionTypeController {

    private final CollectionTypeService collectionTypeService;

    public CollectionTypeController(CollectionTypeService collectionTypeService) {
        this.collectionTypeService = collectionTypeService;
    }

    @GetMapping("{workOrderNumber}")
    @Produces(MediaType.APPLICATION_JSON)
    public Optional<CollectionTypeResponse> getCollectionType(@PathVariable String workOrderNumber) {

        log.info("Getting Collection for workOrderNumber: {}", workOrderNumber);
        Optional<CollectionTypeByFund> collectionType = collectionTypeService.getCollectionType(workOrderNumber);
        return CollectionTypeResponse.from(collectionType);
    }
}
