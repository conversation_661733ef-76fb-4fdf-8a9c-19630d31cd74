package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.PrinterService;
import au.com.peterpal.lucyapi.model.CreatePrinterRequest;
import au.com.peterpal.lucyapi.model.PrinterResponse;
import au.com.peterpal.lucyapi.model.UpdatePrinterRequest;
import au.com.peterpal.lucyapi.persistence.lucyapi.model.Printer;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/printer")
@Log4j2
@SecurityRequirement(name = "BearerAuth")
public class PrinterController {

  private static final String RECEIVED_REQUEST_MSG = "Received request: %s";

  private final PrinterService printerService;

  public PrinterController(PrinterService printerService) {
    this.printerService = printerService;
  }

  @Transactional
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public PrinterResponse createPrinter(@RequestBody @Valid CreatePrinterRequest request) {
    log.debug(String.format(RECEIVED_REQUEST_MSG, request));

    Printer printerSaved = printerService.create(request.toPrinter());
    return PrinterResponse.from(printerSaved);
  }

  @Transactional
  @PutMapping
  @ResponseStatus(HttpStatus.OK)
  public PrinterResponse updatePrinter(@RequestBody @Valid UpdatePrinterRequest request) {
    log.debug(String.format(RECEIVED_REQUEST_MSG, request));

    Printer printer = printerService.findById(request.getId());
    Printer printerUpdated = printerService.update(request.toPrinter(printer));

    return PrinterResponse.from(printerUpdated);
  }

  @Transactional
  @DeleteMapping("{printerId}")
  @ResponseStatus(HttpStatus.OK)
  public void deletePrinter(@PathVariable String printerId) {
    log.debug(String.format(RECEIVED_REQUEST_MSG, printerId));

    Printer printer = printerService.findById(UUID.fromString(printerId));
    printerService.delete(printer);
  }

  @GetMapping("{printerId}")
  public PrinterResponse findById(@PathVariable String printerId) {
    Printer printer = printerService.findById(UUID.fromString(printerId));

    return PrinterResponse.from(printer);
  }

  @GetMapping("find-all")
  public List<PrinterResponse> findAll() {
    List<Printer> printers = printerService.findAll();

    return printers.stream()
        .map(PrinterResponse::from)
        .collect(Collectors.toList());
  }
}
