package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.ClientService;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import lucy.cataloguing.entity.Client;
import lucy.common.NotFoundException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



@Log4j2
@RestController
@RequestMapping("/api/clients")
@SecurityRequirement(name = "BearerAuth")
public class ClientController {
  private final ClientService service;

  public ClientController(ClientService service) {
    this.service = service;
  }

  /**
   * curl -X GET http://127.0.0.1:8080/lucy/api/clients/ERIKM
   *
   * @param customerCode
   * @return
   */
  @GetMapping(path = "/{customer_code}")
  @Produces(MediaType.APPLICATION_JSON)
  public Response getBranch(@PathVariable("customer_code") String customerCode) {
    Response response;
    Client client = service.getClient(customerCode);
    response = Response.ok().entity(client).build();
    return response;
  }

  /**
   * curl -X POST \
   * http://127.0.0.1:8080/lucy/api/clients \
   * -H 'content-type: application/json' \
   * -d '{
   * "version": 0,
   * "customerId": {
   * "organisationIdType": "PPLS",
   * "organisationIdValue": "ERIKM"
   * },
   * "name": "Just for Test",
   * "description": null,
   * "marcCharacterEncoding": null,
   * "enforceUniqueBarcodeNumbers": true
   * }'
   */
  @PostMapping
  @Produces(MediaType.APPLICATION_JSON)
  public Response addBranch(Client client) {
    Response response;
    try {
      Client savedClient = service.addClient(client);
      response = Response.ok(savedClient).build();
    } catch (Exception ex) {
      response = Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
    return response;

  }
}
