package au.com.peterpal.lucyapi.rest.controller;

import au.com.peterpal.lucyapi.core.service.LMSService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.Set;
import javax.ws.rs.core.Response;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.web.bind.annotation.*;

@Log4j2
@RestController
@RequestMapping("/api/lms")
@SecurityRequirement(name = "BearerAuth")
public class LMSController {
    private final LMSService lmsService;

    public LMSController(LMSService lmsService) {
        this.lmsService = lmsService;
    }

    @DeleteMapping("/on-order-record/{workOrderNumber}")
    public Response deleteOnOrderRecord(@PathVariable String workOrderNumber){
        Response response;
        try {
            lmsService.deleteOnOrderRecord(workOrderNumber);
            response = Response.ok().build();
        }catch (Exception ex){
            log.error(ExceptionUtils.getStackTrace(ex));
            response = Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(ex).build();
        }

        return response;
    }

  @PutMapping("/{customerCode}/uploadBibRecordsToSpydus")
  public Response uploadBibRecordsToSpydus(
      @PathVariable String customerCode, @RequestParam(required = false) Set<Integer> batchIdList) {

    lmsService.uploadBibRecordsToSpydus(customerCode, batchIdList);
    return Response.ok().build();
  }

  @PutMapping("/{customerCode}/loginToSpydus")
  public void loginToSpydus(@PathVariable String customerCode) {

    lmsService.loadLms(customerCode);
  }

  @PutMapping("/{customerCode}/uploadBibRecordsToSierra")
  public Response uploadBibRecordsToSierra(
      @PathVariable String customerCode,
      @RequestParam(required = false) List<Integer> batchIdList) {
    lmsService.uploadBibRecordsToSierra(customerCode, batchIdList);
    return Response.ok().build();
  }

  @PutMapping("/{customerCode}/uploadBibRecordsForOCLC")
  public Response uploadBibRecordsToOCLC(
      @PathVariable String customerCode,
      @RequestParam(required = false) List<Integer> batchIdList) {

    lmsService.uploadBibRecordsForOCLC(customerCode, batchIdList);
    return Response.ok().build();
  }

  @PutMapping("/{customerCode}/sendBibRecordsToEmail")
  public Response sendBibRecordsToEmail(
      @PathVariable String customerCode,
      @RequestParam(required = false) List<Integer> batchIdList) {

    lmsService.sendBibRecordsToEmail(customerCode, batchIdList);
    return Response.ok().build();
  }
}
