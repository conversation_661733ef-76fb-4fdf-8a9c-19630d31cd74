package au.com.peterpal.lucyapi.rest.model;

import au.com.peterpal.lucyapi.model.TitleOrderInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
@Value
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@Log4j2
public class UpdateStateInfo {
    private String titleOrderNumber;

    public static List<UpdateStateInfo> from(List<TitleOrderInfo> titleOrders) {
        return Optional.ofNullable(titleOrders)
            .map(List::stream)
            .orElseGet(Stream::empty)
            .map(UpdateStateInfo::from)
            .collect(Collectors.toList());
    }

    public static UpdateStateInfo from(TitleOrderInfo titleOrderInfo) {
        return UpdateStateInfo.builder()
            .titleOrderNumber(titleOrderInfo.getOrderNumber())
            .build();
    }
}
