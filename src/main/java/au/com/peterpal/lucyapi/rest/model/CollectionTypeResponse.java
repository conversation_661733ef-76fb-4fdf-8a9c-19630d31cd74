package au.com.peterpal.lucyapi.rest.model;

import au.com.peterpal.lucyapi.persistence.lucyapi.model.CollectionTypeByFund;
import lombok.Builder;
import lombok.Value;

import java.util.Optional;

@Value
@Builder
public class CollectionTypeResponse {
    private String customerCode;
    private String fund;
    private boolean reduceQuantityToOne;

    public static Optional<CollectionTypeResponse> from(Optional<CollectionTypeByFund> collectionTypeByFund) {
        return collectionTypeByFund.map(
            c -> CollectionTypeResponse.builder().customerCode(c.getCustomerCode())
                .fund(c.getFund())
                .reduceQuantityToOne(c.isReduceQuantityToOne()).build()
        );
    }
}
