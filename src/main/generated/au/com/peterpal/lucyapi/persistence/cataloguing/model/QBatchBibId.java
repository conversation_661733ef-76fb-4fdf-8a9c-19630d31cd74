package au.com.peterpal.lucyapi.persistence.cataloguing.model;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBatchBibId is a Querydsl query type for BatchBibId
 */
@Generated("com.querydsl.codegen.EmbeddableSerializer")
public class QBatchBibId extends BeanPath<BatchBibId> {

    private static final long serialVersionUID = -1636582269L;

    public static final QBatchBibId batchBibId = new QBatchBibId("batchBibId");

    public final NumberPath<Integer> batchId = createNumber("batchId", Integer.class);

    public final NumberPath<Integer> bibId = createNumber("bibId", Integer.class);

    public QBatchBibId(String variable) {
        super(BatchBibId.class, forVariable(variable));
    }

    public QBatchBibId(Path<? extends BatchBibId> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBatchBibId(PathMetadata metadata) {
        super(BatchBibId.class, metadata);
    }

}

