package au.com.peterpal.lucyapi.persistence.cataloguing.model;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QBiBRecordBatchInfo is a Querydsl query type for BiBRecordBatchInfo
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBiBRecordBatchInfo extends EntityPathBase<BiBRecordBatchInfo> {

    private static final long serialVersionUID = -954937213L;

    public static final QBiBRecordBatchInfo biBRecordBatchInfo = new QBiBRecordBatchInfo("biBRecordBatchInfo");

    public final ListPath<BatchBib, QBatchBib> bibRecords = this.<BatchBib, QBatchBib>createList("bibRecords", BatchBib.class, QBatchBib.class, PathInits.DIRECT2);

    public final EnumPath<lucy.cataloguing.codes.BibRecordType> bibType = createEnum("bibType", lucy.cataloguing.codes.BibRecordType.class);

    public final StringPath customerCode = createString("customerCode");

    public final StringPath customerType = createString("customerType");

    public final DateTimePath<java.time.LocalDateTime> dateModified = createDateTime("dateModified", java.time.LocalDateTime.class);

    public final StringPath description = createString("description");

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final StringPath modifiedBy = createString("modifiedBy");

    public final EnumPath<lucy.cataloguing.codes.BibRecordBatchStatus> status = createEnum("status", lucy.cataloguing.codes.BibRecordBatchStatus.class);

    public QBiBRecordBatchInfo(String variable) {
        super(BiBRecordBatchInfo.class, forVariable(variable));
    }

    public QBiBRecordBatchInfo(Path<? extends BiBRecordBatchInfo> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBiBRecordBatchInfo(PathMetadata metadata) {
        super(BiBRecordBatchInfo.class, metadata);
    }

}

