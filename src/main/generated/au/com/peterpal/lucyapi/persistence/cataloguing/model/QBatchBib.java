package au.com.peterpal.lucyapi.persistence.cataloguing.model;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QBatchBib is a Querydsl query type for BatchBib
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBatchBib extends EntityPathBase<BatchBib> {

    private static final long serialVersionUID = 1960305992L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QBatchBib batchBib = new QBatchBib("batchBib");

    public final QBatchBibId id;

    public QBatchBib(String variable) {
        this(BatchBib.class, forVariable(variable), INITS);
    }

    public QBatchBib(Path<? extends BatchBib> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QBatchBib(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QBatchBib(PathMetadata metadata, PathInits inits) {
        this(BatchBib.class, metadata, inits);
    }

    public QBatchBib(Class<? extends BatchBib> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.id = inits.isInitialized("id") ? new QBatchBibId(forProperty("id")) : null;
    }

}

