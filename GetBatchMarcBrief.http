# curl 'https://sunshinecoast.spydus.com/spydus/api/Marc/GetBatchMarcsBrief'
#  -H 'Accept: application/json, text/javascript, */*; q=0.01'
#  -H 'Accept-Language: en-US,en;q=0.9'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/json; charset=UTF-8'
#  -H 'Cookie: .AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8BI4EyEFPQJGmz-lwm9Wk5jhkFjRS4eymUyqw7LNDrPCQI4BQOc5HpSEp2UGdSY_c5QLZtpRk6SUQQocx0H-rCdlMNjASM-hP-QjhOB4bnpvFVHjmrQyzZrte8DrgkGkcelPZ4BIbZFXnetP7XUbXg8; LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; SPYDUS_SESSIONID_443=7739E541216E19BF2F058012781B152900BD80271B3F35E8C73EE71435BCFA9FF24F09B7BB8FF23658A28343CE97D9A6; LASTLOGINDATE_443=22%2F11%2F2023; LASTLOGINTIME_443=11%3A31%20AM; ASPLOGININFO_443=98D59583C2AD25CE16C055D1A09A2D2727473FA192D8C0AE7A8CB40BF99003E097D61F44C3013CDD57640943CAA79154DCBA1D1B9037941E5A443F66F196A1B056F4F8B49732B02619E8D77BC375515FC4A5498CEDC57D3C87E3FDCAD59C9FFF4E64616F7D30D648154D0BBC7B137343087D2790017A62034348A8B400519CAF05529E11BD71434E6D08625685F538F5; XSRF-TOKEN=CfDJ8BI4EyEFPQJGmz-lwm9Wk5hGqV9seIIZDRXFCabQ7_wPpW8TtU_2pUU5r4HowV-NpTvG0QWzGK6ayQP5_GTXHqnv0Q8jt7cWygica7JbjiT7RWoukXh3gyCAzoZe10QMxmW3QLW5k756NS6-rZtXUH8; .AspNetCore.Session=CfDJ8BI4EyEFPQJGmz%2Blwm9Wk5iCk5oF6pAEgNGH5jsi0tDaDwjb2PcvGbTrZg%2B8FCZ1m436UZW40TpjAyrvGxkUhkdpmZkKRCf7JtXWcVhJqSEAmEVa8cLFLDKa3GBQbHMkuRw5iE5NqtDf7ho9KS9ahwZegjUT7Fk2Hli0jJlofp66'
#  -H 'Origin: https://sunshinecoast.spydus.com'
#  -H 'Referer: https://sunshinecoast.spydus.com/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'X-XSRF-TOKEN: CfDJ8BI4EyEFPQJGmz-lwm9Wk5hGqV9seIIZDRXFCabQ7_wPpW8TtU_2pUU5r4HowV-NpTvG0QWzGK6ayQP5_GTXHqnv0Q8jt7cWygica7JbjiT7RWoukXh3gyCAzoZe10QMxmW3QLW5k756NS6-rZtXUH8'
#  -H 'sec-ch-ua: "Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Windows"'
#  --data-raw '{"SelectedLoadMarcs":"L1","Start":1,"AllTheRest":true,"NumberOfRecords":1,"Filename":"PPLS_219742_SUNSHINECOAST_20231122.mrc","PageSize":0,"SessionId":"946defaf-9af6-449d-ac58-2d9b90cb5bc9"}'
#  --compressed
POST https://sunshinecoast.spydus.com/spydus/api/Marc/GetBatchMarcsBrief
Accept: application/json, text/javascript, */*; q=0.01
Accept-Language: en-US,en;q=0.9
Connection: keep-alive
Cookie: .AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8BI4EyEFPQJGmz-lwm9Wk5jhkFjRS4eymUyqw7LNDrPCQI4BQOc5HpSEp2UGdSY_c5QLZtpRk6SUQQocx0H-rCdlMNjASM-hP-QjhOB4bnpvFVHjmrQyzZrte8DrgkGkcelPZ4BIbZFXnetP7XUbXg8; LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; SPYDUS_SESSIONID_443=7739E541216E19BF2F058012781B152900BD80271B3F35E8C73EE71435BCFA9FF24F09B7BB8FF23658A28343CE97D9A6; LASTLOGINDATE_443=22%2F11%2F2023; LASTLOGINTIME_443=11%3A31%20AM; ASPLOGININFO_443=98D59583C2AD25CE16C055D1A09A2D2727473FA192D8C0AE7A8CB40BF99003E097D61F44C3013CDD57640943CAA79154DCBA1D1B9037941E5A443F66F196A1B056F4F8B49732B02619E8D77BC375515FC4A5498CEDC57D3C87E3FDCAD59C9FFF4E64616F7D30D648154D0BBC7B137343087D2790017A62034348A8B400519CAF05529E11BD71434E6D08625685F538F5; XSRF-TOKEN=CfDJ8BI4EyEFPQJGmz-lwm9Wk5hGqV9seIIZDRXFCabQ7_wPpW8TtU_2pUU5r4HowV-NpTvG0QWzGK6ayQP5_GTXHqnv0Q8jt7cWygica7JbjiT7RWoukXh3gyCAzoZe10QMxmW3QLW5k756NS6-rZtXUH8; .AspNetCore.Session=CfDJ8BI4EyEFPQJGmz%2Blwm9Wk5iCk5oF6pAEgNGH5jsi0tDaDwjb2PcvGbTrZg%2B8FCZ1m436UZW40TpjAyrvGxkUhkdpmZkKRCf7JtXWcVhJqSEAmEVa8cLFLDKa3GBQbHMkuRw5iE5NqtDf7ho9KS9ahwZegjUT7Fk2Hli0jJlofp66
Origin: https://sunshinecoast.spydus.com
Referer: https://sunshinecoast.spydus.com/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
X-Requested-With: XMLHttpRequest
X-XSRF-TOKEN: CfDJ8BI4EyEFPQJGmz-lwm9Wk5hGqV9seIIZDRXFCabQ7_wPpW8TtU_2pUU5r4HowV-NpTvG0QWzGK6ayQP5_GTXHqnv0Q8jt7cWygica7JbjiT7RWoukXh3gyCAzoZe10QMxmW3QLW5k756NS6-rZtXUH8
sec-ch-ua: "Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Content-Type: application/json; charset=UTF-8

{
  "SelectedLoadMarcs": "L1",
  "Start": 1,
  "AllTheRest": true,
  "NumberOfRecords": 1,
  "Filename": "PPLS_219742_SUNSHINECOAST_20231122.mrc",
  "PageSize": 0,
  "SessionId": "946defaf-9af6-449d-ac58-2d9b90cb5bc9"
}

###

