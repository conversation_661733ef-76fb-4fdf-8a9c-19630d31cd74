###

# curl 'https://yprl.spydus.com/spydus/api/Marc/GetBatchMarcsBrief' 
#  -H 'Accept: application/json, text/javascript, */*; q=0.01' 
#  -H 'Accept-Language: en,vi;q=0.9' 
#  -H 'Connection: keep-alive' 
#  -H 'Content-Type: application/json; charset=UTF-8' 
#  -H 'Cookie: .AspNetCore.Antiforgery.PxIq_3VpCkA=CfDJ8Kk7U8lZV5hHllgnmhE_U0jh4k6lSURPk89ZEQND47ifBDVt3puKtfK0W_qbedRX-Mu1PJlxLCaR-BmcO9V_ExcfyUf94GvBMal6fA5zmK3Xq6kjzJXfaNPdpvuKT46npYhp7dtEn9GQ3Aet2usyoHI; _pk_id.124.5b36=20546b3df6c46d9a.1718081943.; SPYDUS_SESSIONID_443=7A3AC8D5FB3B9E4ACF6B16FB8AE092DF3A203FEABDE8E152025AFF9A7DBBC660474343243176A07653300B989B98A958; LOC_443=25346; LOCHDG_443=; LASTLOGINDATE_443=5%2F07%2F2024; LASTLOGINTIME_443=1%3A26%20PM; ASPLOGININFO_443=AF97EB2A232D25D331F1685EA93391A85A81323F17EB1AA1C8BBC19349CC658D8E267A6851923E4D2AB6F0EAF2CE5AC967BAB55FA7EF2057F4F2FD33E4992F1D1421A9DBAE0328A21997593080FFBD69A5B671C33258A71A05DE63F7EEBE2CF363C8833976D7F0F8636DEAF935A661550CE59B8C1576FD7121D9EA93553BCCBA; XSRF-TOKEN=CfDJ8Kk7U8lZV5hHllgnmhE_U0jDz1OcjCnoOiDJIbC-w8-Krkdxws1u86UkfQM-209oLAwoUt48j8IL5h83V_JQjiTfFZbfeD5ldbgjJwQ5eNK_L0QmtOnBdYs3SHOXz9EELLKwtlyNfnZwWIKrC-_GQbo; .AspNetCore.Session=CfDJ8Kk7U8lZV5hHllgnmhE%2FU0ieJNZ7DOFZCVpF9k2TxBXTlkOgRRzRO7qY0%2BLlCRifpL9kWZV7UHP%2F47O02uj3gyRJtBN16iSKjIH5LNeDxZCuq4O%2FxqsFEAKkHvBD57rtmez%2FQ0%2BEuflHFgva5z2R8jg5wCVwSKDDk%2FD1DQKJNxyd' 
#  -H 'Origin: https://yprl.spydus.com' 
#  -H 'Referer: https://yprl.spydus.com/spydus' 
#  -H 'Sec-Fetch-Dest: empty' 
#  -H 'Sec-Fetch-Mode: cors' 
#  -H 'Sec-Fetch-Site: same-origin' 
#  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36' 
#  -H 'X-Requested-With: XMLHttpRequest' 
#  -H 'X-XSRF-TOKEN: CfDJ8Kk7U8lZV5hHllgnmhE_U0jDz1OcjCnoOiDJIbC-w8-Krkdxws1u86UkfQM-209oLAwoUt48j8IL5h83V_JQjiTfFZbfeD5ldbgjJwQ5eNK_L0QmtOnBdYs3SHOXz9EELLKwtlyNfnZwWIKrC-_GQbo' 
#  -H 'sec-ch-ua: "Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"' 
#  -H 'sec-ch-ua-mobile: ?0' 
#  -H 'sec-ch-ua-platform: "macOS"' 
#  --data-raw '{"SelectedLoadMarcs":"L1","Start":1,"AllTheRest":true,"NumberOfRecords":1,"Filename":"PPLS_139562.mrc","PageSize":0,"SessionId":"8c44a0ad-1420-4f64-838c-89e43a648f0b"}'
POST https://yprl.spydus.com/spydus/api/Marc/GetBatchMarcsBrief
Accept: application/json, text/javascript, */*; q=0.01
Accept-Language: en,vi;q=0.9
Connection: keep-alive
Cookie: .AspNetCore.Antiforgery.PxIq_3VpCkA=CfDJ8Kk7U8lZV5hHllgnmhE_U0jh4k6lSURPk89ZEQND47ifBDVt3puKtfK0W_qbedRX-Mu1PJlxLCaR-BmcO9V_ExcfyUf94GvBMal6fA5zmK3Xq6kjzJXfaNPdpvuKT46npYhp7dtEn9GQ3Aet2usyoHI; _pk_id.124.5b36=20546b3df6c46d9a.1718081943.; SPYDUS_SESSIONID_443=7A3AC8D5FB3B9E4ACF6B16FB8AE092DF3A203FEABDE8E152025AFF9A7DBBC660474343243176A07653300B989B98A958; LOC_443=25346; LOCHDG_443=; LASTLOGINDATE_443=5%2F07%2F2024; LASTLOGINTIME_443=1%3A26%20PM; ASPLOGININFO_443=AF97EB2A232D25D331F1685EA93391A85A81323F17EB1AA1C8BBC19349CC658D8E267A6851923E4D2AB6F0EAF2CE5AC967BAB55FA7EF2057F4F2FD33E4992F1D1421A9DBAE0328A21997593080FFBD69A5B671C33258A71A05DE63F7EEBE2CF363C8833976D7F0F8636DEAF935A661550CE59B8C1576FD7121D9EA93553BCCBA; XSRF-TOKEN=CfDJ8Kk7U8lZV5hHllgnmhE_U0jDz1OcjCnoOiDJIbC-w8-Krkdxws1u86UkfQM-209oLAwoUt48j8IL5h83V_JQjiTfFZbfeD5ldbgjJwQ5eNK_L0QmtOnBdYs3SHOXz9EELLKwtlyNfnZwWIKrC-_GQbo; .AspNetCore.Session=CfDJ8Kk7U8lZV5hHllgnmhE%2FU0ieJNZ7DOFZCVpF9k2TxBXTlkOgRRzRO7qY0%2BLlCRifpL9kWZV7UHP%2F47O02uj3gyRJtBN16iSKjIH5LNeDxZCuq4O%2FxqsFEAKkHvBD57rtmez%2FQ0%2BEuflHFgva5z2R8jg5wCVwSKDDk%2FD1DQKJNxyd
Origin: https://yprl.spydus.com
Referer: https://yprl.spydus.com/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36
X-Requested-With: XMLHttpRequest
X-XSRF-TOKEN: CfDJ8Kk7U8lZV5hHllgnmhE_U0jDz1OcjCnoOiDJIbC-w8-Krkdxws1u86UkfQM-209oLAwoUt48j8IL5h83V_JQjiTfFZbfeD5ldbgjJwQ5eNK_L0QmtOnBdYs3SHOXz9EELLKwtlyNfnZwWIKrC-_GQbo
sec-ch-ua: "Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
Content-Type: application/json; charset=UTF-8

{
  "SelectedLoadMarcs": "L1",
  "Start": 1,
  "AllTheRest": true,
  "NumberOfRecords": 1,
  "Filename": "PPLS_139562.mrc",
  "PageSize": 0,
  "SessionId": "8c44a0ad-1420-4f64-838c-89e43a648f0b"
}

###

