# step 1
###

# curl 'https://sunshinecoast.spydus.com/cgi-bin/spydus.exe/ENQ/CAT/BIBENQ'
#  -H 'Accept: */*'
#  -H 'Accept-Language: en-GB,en;q=0.9'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8'
#  -H 'Cookie: LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; SPYDUS_SESSIONID_443=981D3A71EFCDEC36AE441BD207C33869CC8D34E40A188091EDB52F85B32F770E9E3E73E10006A163F8A14BC3439A2731; LASTLOGINDATE_443=17%2F11%2F2023; LASTLOGINTIME_443=1%3A57%20PM; ASPLOGININFO_443=98D59583C2AD25CE16C055D1A09A2D2727473FA192D8C0AE7A8CB40BF99003E0BC29B04A5F4E9496F8254BD957896D1ED48C4A340B543FB23297FB88E3288E5C2E2F446C2DF2A2EE73C98A20BEC5A1BEF6995F62053E89EB59BAEE5962DEF85899C2AA914FEDDA7684A1DB3C115D5C9D8C0EBBCB15436362781650C574EAF82CCCBBCE60E4F631D3F288C1DF05DAAEC2; XSRF-TOKEN=CfDJ8BI4EyEFPQJGmz-lwm9Wk5iZPJYrSxP1B510oipEciWaa6EEMOU4WhYvCFngiv6DHsnGB2Pay-cGVqfmFkI58LYbN8GOjUuN1-Uk4JCga7vfpbTW6IAnCdk70Fl3V2xaZBa3syFu80Yc-a381XGJlh0; .AspNetCore.Session=CfDJ8BI4EyEFPQJGmz%2Blwm9Wk5igEY%2FXHz2PsN1%2FlaxZaMmBa2BeJvnAc1DxxH2HmvXEpg1t3lWgsqDC8g%2BCO76%2BRfoBz%2BnKC%2F%2FRsn%2FjpcSb%2Btwg5uOP2Cpp42IlECejMecamkH7b3LdHhZshqUiYYpDplkB98%2FoPKGSoS7KqFpSgzWr'
#  -H 'Origin: https://sunshinecoast.spydus.com'
#  -H 'Referer: https://sunshinecoast.spydus.com/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'Spydus-Sender: WBC'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --data-raw 'ACN=&BRN=&SBN=9781922858313&SSN=&ENTRY1_NAME=TI&ENTRY1=&ENTRY1_TYPE=K&ENTRY2_NAME=AU&ENTRY2=&ENTRY2_TYPE=K&ENTRY3_NAME=SU&ENTRY3=&ENTRY3_TYPE=K&ENTRY5_NAME=DDC&ENTRY5=&PD=&BIBCD=&BIBCUSR=&BIBUD=&BIBUUSR=&NRECS=30&SORTS=HBT.SOVR&SEARCH_FORM=%2Fcgi-bin%2Fspydus.exe%2FMSGTRN%2FCAT%2FBIB%2FAPP&_SPQ=1&FORM_DESC=Cataloguing+-+Bibliographic+Search&QRY=FORMAT%3A+BIB&MODE=CAT&CF=BIB'
#  --compressed
POST https://sunshinecoast.spydus.com/cgi-bin/spydus.exe/ENQ/CAT/BIBENQ
Accept: */*
Accept-Language: en-GB,en;q=0.9
Connection: keep-alive
Cookie: LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; SPYDUS_SESSIONID_443=981D3A71EFCDEC36AE441BD207C33869CC8D34E40A188091EDB52F85B32F770E9E3E73E10006A163F8A14BC3439A2731; LASTLOGINDATE_443=17%2F11%2F2023; LASTLOGINTIME_443=1%3A57%20PM; ASPLOGININFO_443=98D59583C2AD25CE16C055D1A09A2D2727473FA192D8C0AE7A8CB40BF99003E0BC29B04A5F4E9496F8254BD957896D1ED48C4A340B543FB23297FB88E3288E5C2E2F446C2DF2A2EE73C98A20BEC5A1BEF6995F62053E89EB59BAEE5962DEF85899C2AA914FEDDA7684A1DB3C115D5C9D8C0EBBCB15436362781650C574EAF82CCCBBCE60E4F631D3F288C1DF05DAAEC2; XSRF-TOKEN=CfDJ8BI4EyEFPQJGmz-lwm9Wk5iZPJYrSxP1B510oipEciWaa6EEMOU4WhYvCFngiv6DHsnGB2Pay-cGVqfmFkI58LYbN8GOjUuN1-Uk4JCga7vfpbTW6IAnCdk70Fl3V2xaZBa3syFu80Yc-a381XGJlh0; .AspNetCore.Session=CfDJ8BI4EyEFPQJGmz%2Blwm9Wk5igEY%2FXHz2PsN1%2FlaxZaMmBa2BeJvnAc1DxxH2HmvXEpg1t3lWgsqDC8g%2BCO76%2BRfoBz%2BnKC%2F%2FRsn%2FjpcSb%2Btwg5uOP2Cpp42IlECejMecamkH7b3LdHhZshqUiYYpDplkB98%2FoPKGSoS7KqFpSgzWr
Origin: https://sunshinecoast.spydus.com
Referer: https://sunshinecoast.spydus.com/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
Spydus-Sender: WBC
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

ACN=&BRN=&SBN=9781922858313&SSN=&ENTRY1_NAME=TI&ENTRY1=&ENTRY1_TYPE=K&ENTRY2_NAME=AU&ENTRY2=&ENTRY2_TYPE=K&ENTRY3_NAME=SU&ENTRY3=&ENTRY3_TYPE=K&ENTRY5_NAME=DDC&ENTRY5=&PD=&BIBCD=&BIBCUSR=&BIBUD=&BIBUUSR=&NRECS=30&SORTS=HBT.SOVR&SEARCH_FORM=%2Fcgi-bin%2Fspydus.exe%2FMSGTRN%2FCAT%2FBIB%2FAPP&_SPQ=1&FORM_DESC=Cataloguing+-+Bibliographic+Search&QRY=FORMAT%3A+BIB&MODE=CAT&CF=BIB

###
#Step 2
###

# curl 'https://sunshinecoast.spydus.com/cgi-bin/spydus.exe/ENQ/CAT/BIBENQ?MODE=CAT&IRN=85994531&_=1700193749975'
#  -H 'Accept: text/html, */*; q=0.01'
#  -H 'Accept-Language: en-GB,en;q=0.9'
#  -H 'Connection: keep-alive'
#  -H 'Cookie: LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; SPYDUS_SESSIONID_443=981D3A71EFCDEC36AE441BD207C33869CC8D34E40A188091EDB52F85B32F770E9E3E73E10006A163F8A14BC3439A2731; LASTLOGINDATE_443=17%2F11%2F2023; LASTLOGINTIME_443=1%3A57%20PM; ASPLOGININFO_443=98D59583C2AD25CE16C055D1A09A2D2727473FA192D8C0AE7A8CB40BF99003E0BC29B04A5F4E9496F8254BD957896D1ED48C4A340B543FB23297FB88E3288E5C2E2F446C2DF2A2EE73C98A20BEC5A1BEF6995F62053E89EB59BAEE5962DEF85899C2AA914FEDDA7684A1DB3C115D5C9D8C0EBBCB15436362781650C574EAF82CCCBBCE60E4F631D3F288C1DF05DAAEC2; XSRF-TOKEN=CfDJ8BI4EyEFPQJGmz-lwm9Wk5iZPJYrSxP1B510oipEciWaa6EEMOU4WhYvCFngiv6DHsnGB2Pay-cGVqfmFkI58LYbN8GOjUuN1-Uk4JCga7vfpbTW6IAnCdk70Fl3V2xaZBa3syFu80Yc-a381XGJlh0; .AspNetCore.Session=CfDJ8BI4EyEFPQJGmz%2Blwm9Wk5igEY%2FXHz2PsN1%2FlaxZaMmBa2BeJvnAc1DxxH2HmvXEpg1t3lWgsqDC8g%2BCO76%2BRfoBz%2BnKC%2F%2FRsn%2FjpcSb%2Btwg5uOP2Cpp42IlECejMecamkH7b3LdHhZshqUiYYpDplkB98%2FoPKGSoS7KqFpSgzWr'
#  -H 'Referer: https://sunshinecoast.spydus.com/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'X-XSRF-TOKEN: CfDJ8BI4EyEFPQJGmz-lwm9Wk5iZPJYrSxP1B510oipEciWaa6EEMOU4WhYvCFngiv6DHsnGB2Pay-cGVqfmFkI58LYbN8GOjUuN1-Uk4JCga7vfpbTW6IAnCdk70Fl3V2xaZBa3syFu80Yc-a381XGJlh0'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --compressed
GET https://sunshinecoast.spydus.com/cgi-bin/spydus.exe/ENQ/CAT/BIBENQ?MODE=CAT&IRN=85994531&_=1700193749975
Accept: text/html, */*; q=0.01
Accept-Language: en-GB,en;q=0.9
Connection: keep-alive
Cookie: LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; SPYDUS_SESSIONID_443=981D3A71EFCDEC36AE441BD207C33869CC8D34E40A188091EDB52F85B32F770E9E3E73E10006A163F8A14BC3439A2731; LASTLOGINDATE_443=17%2F11%2F2023; LASTLOGINTIME_443=1%3A57%20PM; ASPLOGININFO_443=98D59583C2AD25CE16C055D1A09A2D2727473FA192D8C0AE7A8CB40BF99003E0BC29B04A5F4E9496F8254BD957896D1ED48C4A340B543FB23297FB88E3288E5C2E2F446C2DF2A2EE73C98A20BEC5A1BEF6995F62053E89EB59BAEE5962DEF85899C2AA914FEDDA7684A1DB3C115D5C9D8C0EBBCB15436362781650C574EAF82CCCBBCE60E4F631D3F288C1DF05DAAEC2; XSRF-TOKEN=CfDJ8BI4EyEFPQJGmz-lwm9Wk5iZPJYrSxP1B510oipEciWaa6EEMOU4WhYvCFngiv6DHsnGB2Pay-cGVqfmFkI58LYbN8GOjUuN1-Uk4JCga7vfpbTW6IAnCdk70Fl3V2xaZBa3syFu80Yc-a381XGJlh0; .AspNetCore.Session=CfDJ8BI4EyEFPQJGmz%2Blwm9Wk5igEY%2FXHz2PsN1%2FlaxZaMmBa2BeJvnAc1DxxH2HmvXEpg1t3lWgsqDC8g%2BCO76%2BRfoBz%2BnKC%2F%2FRsn%2FjpcSb%2Btwg5uOP2Cpp42IlECejMecamkH7b3LdHhZshqUiYYpDplkB98%2FoPKGSoS7KqFpSgzWr
Referer: https://sunshinecoast.spydus.com/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
X-XSRF-TOKEN: CfDJ8BI4EyEFPQJGmz-lwm9Wk5iZPJYrSxP1B510oipEciWaa6EEMOU4WhYvCFngiv6DHsnGB2Pay-cGVqfmFkI58LYbN8GOjUuN1-Uk4JCga7vfpbTW6IAnCdk70Fl3V2xaZBa3syFu80Yc-a381XGJlh0
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"

###





# curl 'https://sunshinecoast.spydus.com/spydus/api/itemMaintenance/DeleteItems'
#  -H 'Accept: application/json, text/javascript, */*; q=0.01'
#  -H 'Accept-Language: en-GB,en;q=0.9'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/json'
#  -H 'Cookie: .AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8BI4EyEFPQJGmz-lwm9Wk5j2AhDNlQ61FIBs4Sp3fuJncANqxle9ZcHGuqH_Wf-P3KACtjIa88qZFXx4DQIiT2xkQUkxtUS0ySH-mTpLAa3cWvWzPo0bnYWMy0rv32IzYVwZOQmZEKqPOeehYT64wsk; LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; SPYDUS_SESSIONID_443=981D3A71EFCDEC36AE441BD207C33869CC8D34E40A188091EDB52F85B32F770E9E3E73E10006A163F8A14BC3439A2731; LASTLOGINDATE_443=17%2F11%2F2023; LASTLOGINTIME_443=1%3A57%20PM; ASPLOGININFO_443=98D59583C2AD25CE16C055D1A09A2D2727473FA192D8C0AE7A8CB40BF99003E0BC29B04A5F4E9496F8254BD957896D1ED48C4A340B543FB23297FB88E3288E5C2E2F446C2DF2A2EE73C98A20BEC5A1BEF6995F62053E89EB59BAEE5962DEF85899C2AA914FEDDA7684A1DB3C115D5C9D8C0EBBCB15436362781650C574EAF82CCCBBCE60E4F631D3F288C1DF05DAAEC2; XSRF-TOKEN=CfDJ8BI4EyEFPQJGmz-lwm9Wk5iZPJYrSxP1B510oipEciWaa6EEMOU4WhYvCFngiv6DHsnGB2Pay-cGVqfmFkI58LYbN8GOjUuN1-Uk4JCga7vfpbTW6IAnCdk70Fl3V2xaZBa3syFu80Yc-a381XGJlh0; .AspNetCore.Session=CfDJ8BI4EyEFPQJGmz%2Blwm9Wk5igEY%2FXHz2PsN1%2FlaxZaMmBa2BeJvnAc1DxxH2HmvXEpg1t3lWgsqDC8g%2BCO76%2BRfoBz%2BnKC%2F%2FRsn%2FjpcSb%2Btwg5uOP2Cpp42IlECejMecamkH7b3LdHhZshqUiYYpDplkB98%2FoPKGSoS7KqFpSgzWr'
#  -H 'Origin: https://sunshinecoast.spydus.com'
#  -H 'Referer: https://sunshinecoast.spydus.com/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'X-XSRF-TOKEN: CfDJ8BI4EyEFPQJGmz-lwm9Wk5iZPJYrSxP1B510oipEciWaa6EEMOU4WhYvCFngiv6DHsnGB2Pay-cGVqfmFkI58LYbN8GOjUuN1-Uk4JCga7vfpbTW6IAnCdk70Fl3V2xaZBa3syFu80Yc-a381XGJlh0'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --data-raw '{"irns":["86480041"]}'
#  --compressed
POST https://sunshinecoast.spydus.com/spydus/api/itemMaintenance/DeleteItems
Accept: application/json, text/javascript, */*; q=0.01
Cookie: .AspNetCore.Antiforgery.hBNcHWhXbn8=.AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8BI4EyEFPQJGmz-lwm9Wk5j2AhDNlQ61FIBs4Sp3fuJncANqxle9ZcHGuqH_Wf-P3KACtjIa88qZFXx4DQIiT2xkQUkxtUS0ySH-mTpLAa3cWvWzPo0bnYWMy0rv32IzYVwZOQmZEKqPOeehYT64wsk; SPYDUS_SESSIONID_443=BEF7EA30A263E0C04EA27CD695E3277AA7250603B3B1A6CA64BB3A6960305A1ACE1B17777BF71B97877E56CDB8C93969; ASPLOGININFO_443=98D59583C2AD25CE16C055D1A09A2D2727473FA192D8C0AE7A8CB40BF99003E0D0D40D04023AE6F3AF7357975967DB2193346AA69D7B884495403F39BAE88D6075025D7E8E1CB435FD9033F1A723FC8FAB4475793AE7C4640FF38C7BE9F85C4368ED6DCAF7B126EBB48BA3DA16FED2B81C0DFB3897812D45340DCACCDDB57824B179EC851CFDD6E701E9E1B1137F6455; XSRF-TOKEN=CfDJ8BI4EyEFPQJGmz-lwm9Wk5ih5fALK7Lqp0z3bIxAroxufepl-yoBSK-PnPudLaOFI5moWqd698F95cpDPgb9xiNsm5zS2q7VxXUNlXXLITqfng1BDZDplt3jIoYa-EntrzQYLmb3tgAH-6RTUkIi_vM;
Origin: https://sunshinecoast.spydus.com
Referer: https://sunshinecoast.spydus.com/spydus
X-XSRF-TOKEN: CfDJ8BI4EyEFPQJGmz-lwm9Wk5ih5fALK7Lqp0z3bIxAroxufepl-yoBSK-PnPudLaOFI5moWqd698F95cpDPgb9xiNsm5zS2q7VxXUNlXXLITqfng1BDZDplt3jIoYa-EntrzQYLmb3tgAH-6RTUkIi_vM
Content-Type: application/json

{
  "irns": [
    "86480041"
  ]
}

###

