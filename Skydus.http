###

# curl 'https://hutttest.spydus.co.nz/spydus/api/Marc/GetNumRecords?Filename=3c2cef61-18b7-419b-ac71-48d742337e16.mrx&_=1696565767197'
#
#  -H 'Accept: */*'
#
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8'
#
#  -H 'CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704'
#
#  -H 'Connection: keep-alive'
#
#  -H 'Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D'
#
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#
#  -H 'Sec-Fetch-Dest: empty'
#
#  -H 'Sec-Fetch-Mode: cors'
#
#  -H 'Sec-Fetch-Site: same-origin'
#
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#
#  -H 'X-Requested-With: XMLHttpRequest'
#
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#
#  -H 'sec-ch-ua-mobile: ?0'
#
#  -H 'sec-ch-ua-platform: "Linux"'
#
#  --compressed



GET https://hutttest.spydus.co.nz/spydus/api/Marc/GetNumRecords?Filename=d97b529e-b898-4a3f-820c-a13140913f55.mrx&_=1696565767197
Accept: */*
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704
Connection: keep-alive
Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"

###

# curl 'https://hutttest.spydus.co.nz/spydus/api/Marc/GetNumRecords?Filename=3c2cef61-18b7-419b-ac71-48d742337e16.mrx&_=1696565767197'
#  -H 'Accept: */*'
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8'
#  -H 'CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704'
#  -H 'Connection: keep-alive'
#  -H 'Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D'
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --compressed
GET https://hutttest.spydus.co.nz/spydus/api/Marc/GetNumRecords?Filename=d97b529e-b898-4a3f-820c-a13140913f55.mrx&_=1696565767197
Accept: */*
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704
Connection: keep-alive
Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"

###

# curl 'https://hutttest.spydus.co.nz/spydus/api/Marc/GetRecordsCount?sessionId=3c2cef61-18b7-419b-ac71-48d742337e16&_=1696565767198'
#  -H 'Accept: */*'
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8'
#  -H 'CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704'
#  -H 'Connection: keep-alive'
#  -H 'Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D'
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --compressed
GET https://hutttest.spydus.co.nz/spydus/api/Marc/GetRecordsCount?sessionId=d97b529e-b898-4a3f-820c-a13140913f55&_=1696565767198
Accept: */*
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704
Connection: keep-alive
Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"

###
# curl 'https://hutttest.spydus.co.nz/spydus/api/Marc/GetBatchMarcsBrief'
#  -H 'Accept: application/json, text/javascript, */*; q=0.01'
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8'
#  -H 'CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/json; charset=UTF-8'
#  -H 'Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D'
#  -H 'Origin: https://hutttest.spydus.co.nz'
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --data-raw '{"SelectedLoadMarcs":"L1","Start":1,"AllTheRest":true,"NumberOfRecords":1,"Filename":"PPLS_216423_HUTT_20231006.mrc","PageSize":0,"SessionId":"3c2cef61-18b7-419b-ac71-48d742337e16"}'
#  --compressed
POST https://hutttest.spydus.co.nz/spydus/api/Marc/GetBatchMarcsBrief
Accept: application/json, text/javascript, */*; q=0.01
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704
Connection: keep-alive
Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D
Origin: https://hutttest.spydus.co.nz
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"
Content-Type: application/json; charset=UTF-8

{
  "SelectedLoadMarcs": "L1",
  "Start": 1,
  "AllTheRest": true,
  "NumberOfRecords": 1,
  "Filename": "PPLS_216423_HUTT_20231006.mrc",
  "PageSize": 0,
  "SessionId": "8b84b04d-3789-4d4f-9ff6-cb7971fd9aa1"
}

###

# curl 'https://hutttest.spydus.co.nz/spydus/api/Marc/GetRecordsCount?sessionId=3c2cef61-18b7-419b-ac71-48d742337e16&_=1696565767199'
#  -H 'Accept: */*'
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8'
#  -H 'CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704'
#  -H 'Connection: keep-alive'
#  -H 'Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D'
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --compressed
GET https://hutttest.spydus.co.nz/spydus/api/Marc/GetRecordsCount?sessionId=d97b529e-b898-4a3f-820c-a13140913f55&_=1696565767199
Accept: */*
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704
Connection: keep-alive
Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"

###


# curl 'https://hutttest.spydus.co.nz/cgi-bin/spydus.exe/ENQ/CAT/BIBENQ'
#  -H 'Accept: */*'
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8'
#  -H 'Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; LOC_443=1294982; LOCHDG_443=; SPYDUS_SESSIONID_443=BBB91ACCC259EF5FBC491861DE3EA7336DF87BFA822C9F8DD22EBC0AA286FB07F659466918E8B6A1DBD8B89CA79B1F7A; LASTLOGINDATE_443=11/10/2023; LASTLOGINTIME_443=4:56 PM; .ASPXAUTH=E6D073385B0A5A73EEF9A3FAAE8902D99FFB0690A898424259E0AF0AACB7EA0F8379DCDC98C1A41C2873353DE830B74DC6DD1D8D915128747FE3E4D61FC997A2F88F42E12864792E2587F3A21E821D52292BD16951FCC7EEFD980DE42F5A3CAC; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A04B988B695536232F170A7BADA53E5CAAE9A0E55254240AF156505D1B8F4AFAF610458ADEC4B7898E647AC353E4E1C2FC721950B4F426BB72A8CE0FA49234E19C76254657505CB09E4A7054CAC9BFE1F58ECF532F0717E79D29D3DAEC535183C07B101E66B7BC72829AB6C51F10B0FA5D; ASP.NET_SessionId=0gdkxawk34qsx4jcjrrx2c5z'
#  -H 'Origin: https://hutttest.spydus.co.nz'
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'Spydus-Sender: WBC'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --data-raw 'ACN=&BRN=&SBN=+9780740773655&SSN=&ENTRY1_NAME=TI&ENTRY1=&ENTRY1_TYPE=K&ENTRY2_NAME=AU&ENTRY2=&ENTRY2_TYPE=K&ENTRY3_NAME=SU&ENTRY3=&ENTRY3_TYPE=K&ENTRY5_NAME=DDC&ENTRY5=&PD=&BIBCD=&BIBCUSR=&BIBUD=&BIBUUSR=&NRECS=20&SORTS=HBT.SOVR&SEARCH_FORM=%2Fcgi-bin%2Fspydus.exe%2FMSGTRN%2FCAT%2FBIB%2FAPP&_SPQ=1&FORM_DESC=Cataloguing+-+Bibliographic+Search&QRY=FORMAT%3A+BIB&ISGLB=1&MODE=CAT&CF=BIB'
#  --compressed
POST https://hutttest.spydus.co.nz/cgi-bin/spydus.exe/ENQ/CAT/BIBENQ
Accept: */*
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
Connection: keep-alive
Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; LOC_443=1294982; LOCHDG_443=; SPYDUS_SESSIONID_443=BBB91ACCC259EF5FBC491861DE3EA7336DF87BFA822C9F8DD22EBC0AA286FB07F659466918E8B6A1DBD8B89CA79B1F7A; LASTLOGINDATE_443=11/10/2023; LASTLOGINTIME_443=4:56 PM; .ASPXAUTH=E6D073385B0A5A73EEF9A3FAAE8902D99FFB0690A898424259E0AF0AACB7EA0F8379DCDC98C1A41C2873353DE830B74DC6DD1D8D915128747FE3E4D61FC997A2F88F42E12864792E2587F3A21E821D52292BD16951FCC7EEFD980DE42F5A3CAC; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A04B988B695536232F170A7BADA53E5CAAE9A0E55254240AF156505D1B8F4AFAF610458ADEC4B7898E647AC353E4E1C2FC721950B4F426BB72A8CE0FA49234E19C76254657505CB09E4A7054CAC9BFE1F58ECF532F0717E79D29D3DAEC535183C07B101E66B7BC72829AB6C51F10B0FA5D; ASP.NET_SessionId=0gdkxawk34qsx4jcjrrx2c5z
Origin: https://hutttest.spydus.co.nz
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
Spydus-Sender: WBC
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

ACN=&BRN=&SBN=+9780740773655&SSN=&ENTRY1_NAME=TI&ENTRY1=&ENTRY1_TYPE=K&ENTRY2_NAME=AU&ENTRY2=&ENTRY2_TYPE=K&ENTRY3_NAME=SU&ENTRY3=&ENTRY3_TYPE=K&ENTRY5_NAME=DDC&ENTRY5=&PD=&BIBCD=&BIBCUSR=&BIBUD=&BIBUUSR=&NRECS=20&SORTS=HBT.SOVR&SEARCH_FORM=%2Fcgi-bin%2Fspydus.exe%2FMSGTRN%2FCAT%2FBIB%2FAPP&_SPQ=1&FORM_DESC=Cataloguing+-+Bibliographic+Search&QRY=FORMAT%3A+BIB&ISGLB=1&MODE=CAT&CF=BIB

###



# curl 'https://hutttest.spydus.co.nz/spydus/maintenance/LoadItemsByParentIrnViaScan?parentIrn=57938580&issIrn=0&_=1698197958541'
#  -H 'Accept: application/json, text/javascript, */*; q=0.01'
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8'
#  -H 'CSRFtoken: E6847683811402A0DFD12765B1B9BCCE34D103B99E7CAC82C819BB8DB32583FC'
#  -H 'Connection: keep-alive'
#  -H 'Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; LOC_443=1294982; LOCHDG_443=; SPYDUS_SESSIONID_443=BBB91ACCC259EF5FBC491861DE3EA7336DF87BFA822C9F8DD22EBC0AA286FB07F659466918E8B6A1DBD8B89CA79B1F7A; LASTLOGINDATE_443=11/10/2023; LASTLOGINTIME_443=4:56 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A04B988B695536232F170A7BADA53E5CAAE9A0E55254240AF156505D1B8F4AFAF610458ADEC4B7898E647AC353E4E1C2FC721950B4F426BB72A8CE0FA49234E19C76254657505CB09E4A7054CAC9BFE1F58ECF532F0717E79D29D3DAEC535183C07B101E66B7BC72829AB6C51F10B0FA5D; ASP.NET_SessionId=0gdkxawk34qsx4jcjrrx2c5z; .ASPXAUTH=28F798C190B79066E216D6C1D98690B4D5446CC5CED7EFFAA13E54E1F6E42C77F78AFD6E47F7D8B7FCC9BAA2E8A04CFC7F6D839873A4D5007745275C863BCAE8D82510F51F9C5F482FA9BA5EBEA3E1581A0990189DE4E39B3DED52979C086CB3'
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --compressed
GET https://hutttest.spydus.co.nz/spydus/maintenance/LoadItemsByParentIrnViaScan?parentIrn=84658709&issIrn=0&_=1698197958541
Accept: application/json, text/javascript, */*; q=0.01
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
CSRFtoken: E6847683811402A0DFD12765B1B9BCCE34D103B99E7CAC82C819BB8DB32583FC
Connection: keep-alive
Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; LOC_443=1294982; LOCHDG_443=; SPYDUS_SESSIONID_443=BBB91ACCC259EF5FBC491861DE3EA7336DF87BFA822C9F8DD22EBC0AA286FB07F659466918E8B6A1DBD8B89CA79B1F7A; LASTLOGINDATE_443=11/10/2023; LASTLOGINTIME_443=4:56 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A04B988B695536232F170A7BADA53E5CAAE9A0E55254240AF156505D1B8F4AFAF610458ADEC4B7898E647AC353E4E1C2FC721950B4F426BB72A8CE0FA49234E19C76254657505CB09E4A7054CAC9BFE1F58ECF532F0717E79D29D3DAEC535183C07B101E66B7BC72829AB6C51F10B0FA5D; ASP.NET_SessionId=0gdkxawk34qsx4jcjrrx2c5z; .ASPXAUTH=28F798C190B79066E216D6C1D98690B4D5446CC5CED7EFFAA13E54E1F6E42C77F78AFD6E47F7D8B7FCC9BAA2E8A04CFC7F6D839873A4D5007745275C863BCAE8D82510F51F9C5F482FA9BA5EBEA3E1581A0990189DE4E39B3DED52979C086CB3
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"

###

