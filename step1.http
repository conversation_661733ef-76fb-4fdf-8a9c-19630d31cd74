###

# curl 'https://hutttest.spydus.co.nz/spydus/Account/Login' 
#  -H 'Accept: */*' 
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' 
#  -H 'CSRFtoken: undefined' 
#  -H 'Connection: keep-alive' 
#  -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' 
#  -H 'Cookie: SPYDUSCULTURE_443=en-AU; _pk_id.75.9d71=54db55ce5dd36a46.**********.; _pk_ses.75.9d71=1; PQID_443=*********; _gid=GA1.3.*********.**********; LOC_443=1294982; LOCHDG_443=; ASP.NET_SessionId=wmcgskpjkgnmzfxqhfpxb4f0; _gat_gtag_UA_11112591_5=1; _ga=GA1.1.**********.**********; _ga_RG8EKV2V07=GS1.1.**********.1.1.**********.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.**********.1.1.**********.0.0.0' 
#  -H 'Origin: https://hutttest.spydus.co.nz' 
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus' 
#  -H 'Sec-Fetch-Dest: empty' 
#  -H 'Sec-Fetch-Mode: cors' 
#  -H 'Sec-Fetch-Site: same-origin' 
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36' 
#  -H 'X-Requested-With: XMLHttpRequest' 
#  -H 'sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"' 
#  -H 'sec-ch-ua-mobile: ?0' 
#  -H 'sec-ch-ua-platform: "Linux"' 
#  --data-raw 'UserName=PPALSG&Password=JA8aSiSXjBzWw9PBqweRTLbJxcJZrp0WnGCn%2FAYOhGdtLGep0tkWBlTJrVDOpsjHMfC1fd3eszGjYTLHcseUmCbHcHE1p85%2BHT95P1vH2UmA6h72OObukZbTqexPdH8tYb3if9DKe0o3ms8G5R1pm1xFCuk45kL9LQANKKsaiz8%3D&NewPassword=&ConfPassword=&SpydusSSO=&SpydusSSOUTC=&isForced=false&AuthKey=&Location=1294982&SubLocation=0' 
#  --compressed
POST https://hutttest.spydus.co.nz/spydus/Account/Login
Accept: */*
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
CSRFtoken: undefined
Connection: keep-alive
Cookie: SPYDUSCULTURE_443=en-AU; _pk_id.75.9d71=54db55ce5dd36a46.**********.; _pk_ses.75.9d71=1; PQID_443=*********; _gid=GA1.3.*********.**********; LOC_443=1294982; LOCHDG_443=; ASP.NET_SessionId=wmcgskpjkgnmzfxqhfpxb4f0; _gat_gtag_UA_11112591_5=1; _ga=GA1.1.**********.**********; _ga_RG8EKV2V07=GS1.1.**********.1.1.**********.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.**********.1.1.**********.0.0.0
Origin: https://hutttest.spydus.co.nz
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

UserName=PPALSG&Password=lgVLIcpqIwMJ6FWEiHvEcMOKuT7iS20ohvGsa94DgaqDkRJob0G7GAGpcdB0omHrEoEhOLjnd96eibxWcVIQLo3fqlXyawgFAeEJSHqCSBFkpTCiCner6WFzmauN2aMUKBV%2F4KwsZc%2BIEqE5M5JEoxNYJB3%2FSnnbU3AKO1yyaBQ%3D&NewPassword=&ConfPassword=&SpydusSSO=&SpydusSSOUTC=&isForced=false&AuthKey=&Location=1294982&SubLocation=0

###

