IF OBJECT_ID('sp_ProcessProfileTitleOrder', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_ProcessProfileTitleOrder;
GO

print 'creating      - sp_ProcessProfileTitleOrder ...'
GO

CREATE PROCEDURE sp_ProcessProfileTitleOrder
    @titleOrderNum varchar(20),
    @poMessageCode varchar(20) = NULL,
    @errorCode smallint OUTPUT,
    @poPriorityCode varchar(20) = NULL
AS
    /* Declare variable for error handling */
    DECLARE
        @objectName varchar(30),
        @_error smallint,
        @_rowcount int,
        @errorStr varchar(255),
        @tranFlag smallint

    SET @errorCode = 0

    /* Declare variables for storing title order details */
    DECLARE
        @supplierCode varchar(20)
        , @priorityCode varchar(20)
        , @isbn varchar(20)
        , @qtyOrdered int
        , @poNum varchar(20)
        , @poLineNum int
        , @statusCode varchar(20)
        , @poMsg varchar(20)
        , @poNotes varchar(30)

    SELECT
        @supplierCode = supplierCode,
        @priorityCode = ISNULL(@poPriorityCode, priorityCode),
        @isbn = isbn,
        @qtyOrdered = qtyOrdered
    FROM
        TitleOrd
    WHERE
        titleOrderNum = @titleOrderNum

    SET @_rowcount = @@rowcount

    IF @_rowcount <> 1
    BEGIN

        SET @errorCode = 13201
        RETURN

    END

    IF @supplierCode IS NULL
    BEGIN

        SET @errorCode = 13202
        RETURN

    END

    /*
     * Start a transaction if one is not already running
     */
    IF @@TRANCOUNT <= 0
    BEGIN

        BEGIN TRANSACTION
        SET @tranFlag = 1

    END


    -- Look for an existing, pending purchase order for this supplier/priority code
    -- Only consider existing POs that have any other messages other than 'INV/BO'
    -- If there are multiple, use the most recently created one

    SET @statusCode = 'PENDING'

    SELECT
        @poNum = POHdr.poNum, @poMsg = POHdrMessages.messageCode
    FROM
        POHdr LEFT JOIN
        POHdrMessages ON POHdr.poNum = POHdrMessages.poNum
    WHERE POHdr.suppCode = @supplierCode
        AND POHdr.priorityCode = @priorityCode
        AND POHdr.statusCode = @statusCode
        AND poHdrMessages.messageCode = @poMessageCode
        AND POHdr.notes = 'Profile Orders'
    ORDER BY
        POHdr.poNum ASC

    -- If we found a purchase order, look for an existing line item with this isbn
    -- Depending on what we found, add the title order to a new or existing po/line
    IF @poNum IS NULL
        BEGIN

            SELECT 'PO Num is null ...'
            SELECT @titleOrderNum "Title Order No", @poPriorityCode "Priority Code"

            EXEC sp_CreatePOFromTitleOrder @titleOrderNum = @titleOrderNum, @poPriority = @priorityCode, @poNotes = 'Profile Orders', @poNum = @poNum OUTPUT, @errorCode = @errorCode OUTPUT

            SELECT @titleOrderNum "T/O Num", @poNum "poNum", @errorcode "ErrorCode", @poMessageCode "POMsgCode", @errorCode "Error Code"

            IF @errorCode = 0 AND @poMessageCode IN ( SELECT msgCode FROM POMsgs )
            EXEC sp_AddMessageToPOHdr @titleOrderNum, @poMessageCode, @errorCode OUTPUT
        END
    ELSE
        BEGIN

            IF @poMsg IS NULL AND @poMessageCode IN ( SELECT msgCode FROM POMsgs )
                BEGIN

                    EXEC sp_AddMessageToPOHdr @titleOrderNum, @poMessageCode, @errorCode OUTPUT

                    SET @_error = @@error
                    IF @_error <> 0
                        SET @errorCode = 13203

                END

            IF @errorCode = 0
                BEGIN

                    SELECT @poLineNum = poLineNum
                    FROM POLine
                    WHERE POLine.poNum = @poNum
                        AND POLine.isbn = @isbn

                    IF @poLineNum IS NULL
                        BEGIN

                            EXEC sp_InsertPOLineFromTitleOrder @titleOrderNum, @poNum, @poLineNum OUTPUT, @errorCode OUTPUT

                            SET @_error = @@error
                            IF @_error <> 0
                                SET @errorCode = 13203

                        END
                    ELSE
                        BEGIN

                            EXEC sp_IncrementPOLine @titleOrderNum, @poNum, @poLineNum, @qtyOrdered, @errorCode OUTPUT

                            SET @_error = @@error
                            IF @_error <> 0
                                SET @errorCode = 13203

                        END

                    END
                END

    IF @errorCode = 0
    BEGIN

        UPDATE TitleOrd
        SET dateProcessed = getdate(),
            processedBy = CURRENT_USER
        WHERE
            titleOrderNum = @titleOrderNum

        SET @_error = @@error
        IF @_error <> 0
            SET @errorCode = 13204

    END

    /* Commit/Rollback the transaction if one was started */
    IF @tranFlag = 1
    BEGIN
        IF @errorCode = 0
            COMMIT TRANSACTION
        ELSE
            BEGIN

                ROLLBACK TRANSACTION
                /* TODO: Log the error */
            END
    END
RETURN

go

grant all on dbo.sp_ProcessProfileTitleOrder to public
GO
