###

# curl 'https://sunshinecoast.spydus.com/spydus/Account/Login'
#  -H 'Accept: application/json, text/javascript, */*; q=0.01'
#  -H 'Accept-Language: en-US,en;q=0.9'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/json; charset=UTF-8'
#  -H 'Cookie: .AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8BI4EyEFPQJGmz-lwm9Wk5hCrAYeNyJwBwbRWFq0dTVqEWod0aaxK-owpC8eWbyjTe85VKO1Sw4VfaRXHbgQD3QemavW_xSOCGhrBcKPb8PPw41MXtYZYEEEnqtIq4BTVVugEyjfgSkrqjv9YMqQD94'
#  -H 'Origin: https://sunshinecoast.spydus.com'
#  -H 'Referer: https://sunshinecoast.spydus.com/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'X-XSRF-TOKEN: undefined'
#  -H 'sec-ch-ua: "Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Windows"'
#  --data-raw '{"UserName":"PAL","Password":"opoDL5bDVoni9VVLAHqjCyqUt0lXST1X3RO6CKc/CFbdXwfGFsFdPryNXBs1mW/kIUyKXyvwp6y86yZu78XTWKbD3Nr1cFOPt1lrhdL42DVLkWgzS5X+NXzXl3f7viY5THqxUqOrCZRqDOBBcMweTuIQi82Wc34RKNKm1O/anVs=","NewPassword":"","ConfPassword":"","SpydusSSO":"","SpydusSSOUTC":"","isForced":false,"AuthKey":"","Location":"7947981","SubLocation":"7949212"}'
#  --compressed
POST https://sunshinecoast.spydus.com/spydus/Account/Login
Accept: application/json, text/javascript, */*; q=0.01
Accept-Language: en-US,en;q=0.9
Connection: keep-alive
Cookie: .AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8BI4EyEFPQJGmz-lwm9Wk5j2AhDNlQ61FIBs4Sp3fuJncANqxle9ZcHGuqH_Wf-P3KACtjIa88qZFXx4DQIiT2xkQUkxtUS0ySH-mTpLAa3cWvWzPo0bnYWMy0rv32IzYVwZOQmZEKqPOeehYT64wsk
Origin: https://sunshinecoast.spydus.com
Referer: https://sunshinecoast.spydus.com/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
X-XSRF-TOKEN: undefined
sec-ch-ua: "Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Content-Type: application/json; charset=UTF-8

{
  "UserName": "PAL",
  "Password": "a3KU7N1T63W1J7KrMCpS6DynV+iIDpycv6NDylvoyMb4aS6ZYvGgauRCfhObkq5iy7AmeU7iht+ZWK2dyQGq5eNIltFmjyThAPYrmNwizR4SReYuyWKXHLuT9G5rh8hccbUUoGMyQJlgZuycc54JXobypAseYCk9/HPktedzdDY=",
  "NewPassword": "",
  "ConfPassword": "",
  "SpydusSSO": "",
  "SpydusSSOUTC": "",
  "isForced": false,
  "AuthKey": "",
  "Location": "7947981",
  "SubLocation": "7949212"
}

###

