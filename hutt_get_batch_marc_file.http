###

# curl 'https://hutttest.spydus.co.nz/spydus/api/Marc/GetBatchMarcsBrief'
#  -H 'Accept: application/json, text/javascript, */*; q=0.01'
#  -H 'Accept-Language: en,vi;q=0.9'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/json; charset=UTF-8'
#  -H 'Cookie: .AspNetCore.Antiforgery.uDYLwMZzUFg=CfDJ8N59zV_hNdhLu0F-BygXGxJe36erKqVIRc9MVTzflLEF4VKn38W3BM0m65XIQHbLoulw82EpWc0QJ13QGlmEbxD2FsVOA4mUkS2PfZqvZQtXFD_sekMGcaqvrnRkB2ro4IBxNrMuQIwwskjU9skWuV4; SPYDUSCULTURE_443=en-AU; SPYDUS_SESSIONID_443=0F3264697A361637C0E5C9027C7108DD905CF8F42277FEE61685F4F9086B659CC2F521876BDFD1232EA7FC1993196258; LOC_443=1294983; LOCHDG_443=; LASTLOGINDATE_443=8%2F08%2F2024; LASTLOGINTIME_443=2%3A49%20PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD3D47E76BD0BE659792D9D09F69A88D116F5BEA4AB36ADD16A5FE33D3C7499DEFA294A4A26E37F04E542BB1722DF2F9409708CC1CFBE4600127380B3CEA5D8366B6994AF3A9204378C5E9CDBFE709AA06667234E90E440FDCC7048AF4AC1FA75FB9F6856D9F8A62CAEE3AB22D1FAB16DBBBC5826EF89D95935FD6A01B630338C69; XSRF-TOKEN=CfDJ8N59zV_hNdhLu0F-BygXGxIRSKCd4-D8s0JiYlFupcuS2IYnjqlFjywvlEdeMTdgLBUB3A3R5S9OLoNCmqI5nmgzJnfZvGDUJ6BGrsE4hjiyOFtP3IbUOCe1vVFKrNy6fZt8AGxDxOG_z6l20zfyxmc; .AspNetCore.Session=CfDJ8N59zV%2FhNdhLu0F%2BBygXGxLNvoxJfSfwa9vl%2F4%2FEJ%2B5R9%2FtwOV%2B3KmLspFvXvx1Qh4ipeznEaTsty%2FNyLNhPT9fuUjC6z8nFlmW6E2QrBYSuhCt6wt%2FIzDFfAo9n63U%2FIp13pmi%2FwkGpU6UrleWMgvUq5cjuGClfF4LP0vXsOaEw'
#  -H 'Origin: https://hutttest.spydus.co.nz'
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'X-XSRF-TOKEN: CfDJ8N59zV_hNdhLu0F-BygXGxIRSKCd4-D8s0JiYlFupcuS2IYnjqlFjywvlEdeMTdgLBUB3A3R5S9OLoNCmqI5nmgzJnfZvGDUJ6BGrsE4hjiyOFtP3IbUOCe1vVFKrNy6fZt8AGxDxOG_z6l20zfyxmc'
#  -H 'sec-ch-ua: "Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "macOS"'
#  --data-raw '{"SelectedLoadMarcs":"L1","Start":1,"AllTheRest":true,"NumberOfRecords":1,"Filename":"PPLS_139562.mrc","PageSize":0,"SessionId":"0aea8f5a-962b-4733-a9fc-a7f75d4fca3e"}'
POST https://hutttest.spydus.co.nz/spydus/api/Marc/GetBatchMarcsBrief
Accept: application/json, text/javascript, */*; q=0.01
Accept-Language: en,vi;q=0.9
Connection: keep-alive
Cookie: .AspNetCore.Antiforgery.uDYLwMZzUFg=CfDJ8N59zV_hNdhLu0F-BygXGxJe36erKqVIRc9MVTzflLEF4VKn38W3BM0m65XIQHbLoulw82EpWc0QJ13QGlmEbxD2FsVOA4mUkS2PfZqvZQtXFD_sekMGcaqvrnRkB2ro4IBxNrMuQIwwskjU9skWuV4; SPYDUSCULTURE_443=en-AU; SPYDUS_SESSIONID_443=0F3264697A361637C0E5C9027C7108DD905CF8F42277FEE61685F4F9086B659CC2F521876BDFD1232EA7FC1993196258; LOC_443=1294983; LOCHDG_443=; LASTLOGINDATE_443=8%2F08%2F2024; LASTLOGINTIME_443=2%3A49%20PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD3D47E76BD0BE659792D9D09F69A88D116F5BEA4AB36ADD16A5FE33D3C7499DEFA294A4A26E37F04E542BB1722DF2F9409708CC1CFBE4600127380B3CEA5D8366B6994AF3A9204378C5E9CDBFE709AA06667234E90E440FDCC7048AF4AC1FA75FB9F6856D9F8A62CAEE3AB22D1FAB16DBBBC5826EF89D95935FD6A01B630338C69; XSRF-TOKEN=CfDJ8N59zV_hNdhLu0F-BygXGxIRSKCd4-D8s0JiYlFupcuS2IYnjqlFjywvlEdeMTdgLBUB3A3R5S9OLoNCmqI5nmgzJnfZvGDUJ6BGrsE4hjiyOFtP3IbUOCe1vVFKrNy6fZt8AGxDxOG_z6l20zfyxmc; .AspNetCore.Session=CfDJ8N59zV%2FhNdhLu0F%2BBygXGxLNvoxJfSfwa9vl%2F4%2FEJ%2B5R9%2FtwOV%2B3KmLspFvXvx1Qh4ipeznEaTsty%2FNyLNhPT9fuUjC6z8nFlmW6E2QrBYSuhCt6wt%2FIzDFfAo9n63U%2FIp13pmi%2FwkGpU6UrleWMgvUq5cjuGClfF4LP0vXsOaEw
Origin: https://hutttest.spydus.co.nz
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
X-XSRF-TOKEN: CfDJ8N59zV_hNdhLu0F-BygXGxIRSKCd4-D8s0JiYlFupcuS2IYnjqlFjywvlEdeMTdgLBUB3A3R5S9OLoNCmqI5nmgzJnfZvGDUJ6BGrsE4hjiyOFtP3IbUOCe1vVFKrNy6fZt8AGxDxOG_z6l20zfyxmc
sec-ch-ua: "Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
Content-Type: application/json; charset=UTF-8

{
  "SelectedLoadMarcs": "L1",
  "Start": 1,
  "AllTheRest": true,
  "NumberOfRecords": 1,
  "Filename": "PPLS_139562.mrc",
  "PageSize": 0,
  "SessionId": "0aea8f5a-962b-4733-a9fc-a7f75d4fca3e"
}

###

