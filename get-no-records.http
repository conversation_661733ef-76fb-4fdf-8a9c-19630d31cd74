###

# curl 'https://hutttest.spydus.co.nz/spydus/api/Marc/GetNumRecords?Filename=feeb5885-facd-4bd7-a6c6-55efc07cf82a.mrx&_=1696565767180'
#  -H 'Accept: */*'
#  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8'
#  -H 'CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704'
#  -H 'Connection: keep-alive'
#  -H 'Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; _pk_ses.75.9d71=1; PQID_443=143530562; _gid=GA1.3.1474353255.1696565754; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; .ASPXAUTH=8C0CBC3D17A9ECF83B02E3523BFF6F25374C6BC35D2C5136F3DA015388660FFDB5E43D81385CB21AE0E8507D2C8E4F3C2593E6C5A39E01AD78B888684971E82B6C66A3429B0E57CCF7ADB523BCD85151462FB224731C103E6D0E0B491BCFD7E9; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0'
#  -H 'Referer: https://hutttest.spydus.co.nz/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Linux"'
#  --compressed
GET https://hutttest.spydus.co.nz/spydus/api/Marc/GetNumRecords?Filename=c3d737b5-a0e2-4801-9da6-dc3e5145f285.mrx&_=1696565767180
Accept: */*
Accept-Language: en-GB,en-US;q=0.9,en;q=0.8
CSRFtoken: 01AE6049AF8E5D9CFAC70C48805EEF38BE7F01DA7A2178626951747B6995C704
Connection: keep-alive
Cookie: _pk_id.75.9d71=a210b307d66fa4b3.1696565753.; PQID_443=143530562; _ga=GA1.1.1348636136.1696565753; SPYDUSCULTURE_443=en-AU; _ga_RG8EKV2V07=GS1.1.1696565753.1.0.1696565766.0.0.0; _ga_9FEGRSQJ3Z=GS1.1.1696565754.1.0.1696565766.0.0.0; SPYDUS_SESSIONID_443=C6CF577AD73E231DC72BB6DCEA01C3545525E04FD239E3975034A4D1E40513845AAC09ACF262CBF6C1A296E3B12AC169; LOC_443=1294982; LOCHDG_443=; LASTLOGINDATE_443=6/10/2023; LASTLOGINTIME_443=4:11 PM; ASPLOGININFO_443=58E50FF1C79FB0E38D6271DACCFDBAD319CD57EC7BB71E472EBAACB5D32D29A085F06D9A460879F2D912A459AFF5BA66249193E05913616537DB9C24B5858D4927B4894F7C48871FCA2D7DA9BE72CBC7416F1400F7EB0681CE37E9F71524A99FDB2A80B8E86512B380D514CFF191E20E409614F267EF615CBC80AEB20C540951170E8D7AEF2C82F2D19FC2D3AF5BE899; ASP.NET_SessionId=2cnmxzwdarr5drgv2qvqnle0; .ASPXAUTH=AAC3C3A37AC9311E242B5D187F48ABCDE7252DEE7CA90799399DAE4754B828E649276E353732ACBFAB6FCCD1780AC3D19ACF409ED662586C31A5BAAA304CCFD67BD33AE8EA72CE911F57DE841ACEED9F581A45343B55C4EA373C98106C89101D
Referer: https://hutttest.spydus.co.nz/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"

###

