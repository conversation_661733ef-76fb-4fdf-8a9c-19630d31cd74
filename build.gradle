plugins {
    id 'org.springframework.boot' version '2.1.4.RELEASE'
    id 'java'
    id 'org.sonarqube' version '2.6.2'
    id 'com.palantir.docker' version '0.19.2'
    id 'com.gorylenko.gradle-git-properties' version '1.5.1'
}

apply plugin: 'io.spring.dependency-management'
apply plugin: 'com.palantir.docker'
apply plugin: 'org.springframework.boot'

bootJar {
    baseName = "lucy-api"
}

group = 'au.com.peterpal'
version = '1.0-SNAPSHOT'
sourceCompatibility = '1.8'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
    jaxb
    querydslapt
}

repositories {
    mavenCentral()
    maven {
        url 'https://dl.bintray.com/palantir/releases' // docker-compose-rule is published on bintray
    }
    maven {
        url 'https://repo.spring.io/libs-snapshot'
    }
}

ext {
    springFoxSwaggerVersion = "2.8.0"
    log4jVersion = "2.10.0"
    lombokVersion = "1.16.20"
    guavaVersion = "15.0"
    wildflyEjbClientBomVersion = "10.1.0.Final"
    lucyJavaEEVersion = "8.07.25031203"
    commonRestVersion = "0.5.2019090501"
    hamcrestVersion = "1.3"
    mockitoVersion = "2.15.0"
    querydslVersion = "4.2.1"
    jzkitVersion = "2.2.4"
    a2jVersion = "2.0.4"
    liberoVersion = "1.0.20161006"
    liberoApiVersion = "2.0.20200502"
    lucyMarcVersion = "8.04.2019121301"
    h2Version = "1.4.197"
    testToolsVersion = "0.1"

    commonSecurityVersion = "1.0.2020091403"
    springDocVersion = "1.4.1"
    keycloakVersion = "10.0.2"
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-openfeign:2.1.4.RELEASE"
        mavenBom 'org.springframework.cloud:spring-cloud-sleuth:2.1.4.RELEASE'
        mavenBom "org.keycloak.bom:keycloak-adapter-bom:${keycloakVersion}"
    }
}
dependencies {
    // Peter Pal libraries
    implementation "au.com.peterpal:common-rest:$commonRestVersion"
    implementation "au.com.peterpal:lucy-marc:$lucyMarcVersion"

    // Exclusions to be applied in multiple places
    def withExclusions = {
        // Spring Boot Starter comes with Logback by default but we want Log4j2
        exclude group: "ch.qos.logback", module: "logback-classic"
        // Need to exclude SLF4J bindings or we get an infinite loop when
        exclude group: "org.apache.logging.log4j", module: "log4j-to-slf4j"
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    implementation 'org.springframework.ws:spring-ws-core'
    jaxb "com.sun.xml.bind:jaxb-xjc:2.1.7"

    // Basic Spring Boot microservice boilerplate, common to all services
    implementation 'org.springframework.boot:spring-boot-starter-web', withExclusions
    implementation 'org.springframework.boot:spring-boot-starter-webflux', withExclusions
    implementation 'org.springframework.boot:spring-boot-starter-actuator', withExclusions
    implementation 'org.springframework.boot:spring-boot-starter-log4j2', withExclusions
    // Spring Retry
    implementation 'org.springframework.retry:spring-retry:1.2.5.RELEASE', withExclusions
    implementation 'org.springframework:spring-aspects', withExclusions
    // Online REST API documentation
    implementation("io.springfox:springfox-swagger2:$springFoxSwaggerVersion")
    implementation("io.springfox:springfox-bean-validators:$springFoxSwaggerVersion")
    runtimeOnly("io.springfox:springfox-swagger-ui:$springFoxSwaggerVersion")
    implementation("org.springdoc:springdoc-openapi-ui:$springDocVersion")

    // JPA and data access
    implementation("org.springframework.boot:spring-boot-starter-data-jpa", withExclusions)
    implementation group: 'net.sourceforge.jtds', name: 'jtds', version: '1.3.1'
    implementation("com.querydsl:querydsl-root:$querydslVersion")
    implementation("com.querydsl:querydsl-jpa:$querydslVersion")
    compileOnly("com.querydsl:querydsl-apt:$querydslVersion:jpa")
    implementation(group: 'org.postgresql', name: 'postgresql', version: '42.2.2')
    implementation(group: 'org.flywaydb', name: 'flyway-core', version: '5.2.4')
    runtime("com.h2database:h2:$h2Version")

    // Spring Integration
    compile("org.springframework.boot:spring-boot-starter-integration", withExclusions)
    compile("org.springframework.boot:spring-boot-starter-artemis", withExclusions)
    compile("org.springframework.integration:spring-integration-jms", withExclusions)
    compile("org.springframework.integration:spring-integration-http", withExclusions)
    compile("org.springframework.integration:spring-integration-jpa", withExclusions)

    // Security
    implementation('org.springframework.boot:spring-boot-starter-security', withExclusions)
    implementation('org.keycloak:keycloak-spring-boot-starter', withExclusions)
    compile("au.com.peterpal:common-security-java-8:$commonSecurityVersion", withExclusions)
    // Sleuth
    compile("org.springframework.cloud:spring-cloud-starter-sleuth", withExclusions)

    // JSON processing
    implementation("com.fasterxml.jackson.core:jackson-databind:2.9.8")

    // Marc processing
    implementation("org.marc4j:marc4j:2.8.3")

    // EJB integration
    implementation("org.wildfly:wildfly-ejb-client-bom:$wildflyEjbClientBomVersion")
    implementation("au.com.peterpal:lucy-cataloguing-wildfly-service:$lucyJavaEEVersion")
    implementation("au.com.peterpal:lucy-cataloguing-core-api:$lucyJavaEEVersion")
    implementation("au.com.peterpal:lucy-catalogue-core-model:$lucyJavaEEVersion")

    implementation("au.com.peterpal:lucy-fulfilment-wildfly-service:$lucyJavaEEVersion")
    implementation("au.com.peterpal:lucy-fulfilment-core-api:$lucyJavaEEVersion")
    implementation("au.com.peterpal:lucy-fulfilment-core-model:$lucyJavaEEVersion")

    // Axis2 libraries
    implementation group: 'org.apache.axis2', name: 'axis2-codegen', version: '1.7.9'
    implementation group: 'org.apache.axis2', name: 'axis2-xmlbeans', version: '1.7.9'
    implementation group: 'org.apache.axis2', name: 'axis2-transport-local', version: '1.7.9'
    implementation group: 'org.apache.axis2', name: 'axis2-transport-http', version: '1.7.9'
    implementation group: 'org.apache.axis2', name: 'axis2-jaxws', version: '1.7.9'
    implementation group: 'org.apache.xmlbeans', name: 'xmlbeans', version: '2.6.0'

    // Z3950 libraries
    implementation("org.jzkit:jzkit2_core:$jzkitVersion")
    implementation("org.jzkit:jzkit2_service:$jzkitVersion")
    implementation("org.jzkit:jzkit2_z3950_plugin:$jzkitVersion")
    implementation("org.jzkit:a2j:$a2jVersion")

    // Libero libraries
    //implementation("au.com.peterpal.libero:liberoapi:$liberoApiVersion")
    implementation("au.com.peterpal.libero:lucy-libero:$liberoVersion")
    //implementation("au.com.peterpal.libero:libero-ws:$liberoVersion")
    implementation("au.com.peterpal.libero:libero-ws:2.0.2020100202")

    // Utility libraries
    compileOnly("org.projectlombok:lombok:$lombokVersion")
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor("com.querydsl:querydsl-apt:$querydslVersion:jpa")
    annotationProcessor 'jakarta.persistence:jakarta.persistence-api:2.2.3'
    implementation("org.apache.logging.log4j:log4j-web:$log4jVersion")
    runtimeOnly("log4j:log4j:1.2.17")
    implementation("org.hibernate:hibernate-core:5.4.0.Final")
    implementation("com.google.guava:guava:$guavaVersion:cdi1.0")
    implementation("javax.persistence:javax.persistence-api:2.2")
    implementation("javax.xml:jaxrpc-api:1.1")
    implementation("axis:axis:1.4")
    implementation("wsdl4j:wsdl4j:1.5.1")
    implementation("javax.activation:activation:1.1.1")
    implementation("javax.mail:mail:1.4")
    implementation('org.apache.commons:commons-collections4:4.1')
    implementation('org.apache.commons:commons-text:1.10.0')
    implementation 'com.eatthepath:java-otp:0.4.0'
    implementation 'commons-codec:commons-codec:1.16.0'

    testCompileOnly "org.projectlombok:lombok:$lombokVersion"
    testAnnotationProcessor "org.projectlombok:lombok:$lombokVersion"
    testImplementation 'org.springframework.boot:spring-boot-starter-test', withExclusions
    testImplementation("org.hamcrest:hamcrest-core:$hamcrestVersion")
    testImplementation("org.mockito:mockito-core:$mockitoVersion")
    testImplementation("au.com.peterpal:test-tools:$testToolsVersion")
    testCompile("org.springframework.security:spring-security-test")
    testImplementation("org.xmlunit:xmlunit-core:2.9.0")
    testImplementation("org.xmlunit:xmlunit-assertj3:2.9.0")
    testImplementation("org.assertj:assertj-core:3.24.2")
}

processResources {
    def profile = (project.hasProperty('profile') ? project.profile : null)
    include "application*.properties"
    include "*.xml"
    if (profile) {
        profile = profile.toLowerCase()
        include "jboss-ejb-client-${profile}.properties"
        rename("jboss-ejb-client-${profile}.properties", 'jboss-ejb-client.properties')
    }
    include "a2j.properties"
    include "InternalAttrTypes.properties"
    include "InternalToType1Rules.properties"
    from('src/main/resources') {
        include '**/*.*'
    }
}

docker {
    dependsOn(build)
    def dockerRegistry = project.hasProperty("dockerRegistry") ? "${project.dockerRegistry}" : 'docker.peterpal.com.au'

    name("${dockerRegistry}/${bootJar.baseName}")
    files(bootJar.archivePath)
    buildArgs(["JAR_FILE": "${bootJar.archiveName}"])

    tags project.hasProperty("releaseNumber") ? project.releaseNumber : project.version
}
