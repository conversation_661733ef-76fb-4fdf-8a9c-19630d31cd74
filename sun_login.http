# curl 'https://sunshinecoast.spydus.com/spydus/Account/Login'
#  -H 'Accept: application/json, text/javascript, */*; q=0.01'
#  -H 'Accept-Language: en-US,en;q=0.9,en-AU;q=0.8'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/json; charset=UTF-8'
#  -H 'Cookie: .AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8OeUeirWOaNDuqbzxnjDY16K490uSyd_9XOdHt8kIOO5NShoYyn06CWoayZCfyv2t45R0kLcVo_XQUNeluOLuWghKkCqVfWJJcWMUoehnWDBfyMPAu3dWh75U6Dx2lo5CP-CJClEa041cqHQDuO-I2I; LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; _ga=GA1.1.**********.**********; _ga_F6LV82ECSM=GS1.1.**********.1.0.**********.0.0.0; XSRF-TOKEN=CfDJ8OeUeirWOaNDuqbzxnjDY16BeBe1cBp2glt1mA2b_2raxAoEv_kjZPb5CiueQz9I9ZUkz4teVi-TDHvVOjuCksvLEe-1lSCzkmfWHdz7XIe1_bXCldzA16rkdbN75CsfeivGx1KqweaGY-qW6dj3i9g; SPYDUS_SESSIONID_443=; LASTLOGINDATE_443=; LASTLOGINTIME_443=; ASPLOGININFO_443='
#  -H 'Origin: https://sunshinecoast.spydus.com'
#  -H 'Referer: https://sunshinecoast.spydus.com/spydus'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'X-XSRF-TOKEN: CfDJ8OeUeirWOaNDuqbzxnjDY16BeBe1cBp2glt1mA2b_2raxAoEv_kjZPb5CiueQz9I9ZUkz4teVi-TDHvVOjuCksvLEe-1lSCzkmfWHdz7XIe1_bXCldzA16rkdbN75CsfeivGx1KqweaGY-qW6dj3i9g'
#  -H 'sec-ch-ua: "Microsoft Edge";v="129", "Not=A?Brand";v="8", "Chromium";v="129"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Windows"'
#  --data-raw '{"UserName":"PAL","Password":"sE1Ci3Es8Y/GJ4SthjHjO4uDoSpcmAH3z/ld7PF+SY0cwEDWrwsOS2Nn+uBAOutHPSL/ibwLIEnOwxdcON4AQehNVJ2gl5bzB4hJgVQJnlU7d/lU+7SVqMPqlWZl54iZu9cXnOTKycMVDfeDwAHtoJ4whRH/nOygKe7dOgMZVhE=","NewPassword":"","ConfPassword":"","SpydusSSO":"","SpydusSSOUTC":"","isForced":false,"AuthKey":"","Location":7947981,"SubLocation":7949212}'
POST https://sunshinecoast.spydus.com/spydus/Account/Login
Accept: application/json, text/javascript, */*; q=0.01
Accept-Language: en-US,en;q=0.9,en-AU;q=0.8
Connection: keep-alive
Cookie: .AspNetCore.Antiforgery.hBNcHWhXbn8=CfDJ8OeUeirWOaNDuqbzxnjDY16K490uSyd_9XOdHt8kIOO5NShoYyn06CWoayZCfyv2t45R0kLcVo_XQUNeluOLuWghKkCqVfWJJcWMUoehnWDBfyMPAu3dWh75U6Dx2lo5CP-CJClEa041cqHQDuO-I2I; LOC_443=7947981; LOCHDG_443=; LASTSUBLOC_443=7949212; SUBLOC_443=7949212; SUBLOCHDG_443=; _ga=GA1.1.**********.**********; _ga_F6LV82ECSM=GS1.1.**********.1.0.**********.0.0.0; XSRF-TOKEN=CfDJ8OeUeirWOaNDuqbzxnjDY16BeBe1cBp2glt1mA2b_2raxAoEv_kjZPb5CiueQz9I9ZUkz4teVi-TDHvVOjuCksvLEe-1lSCzkmfWHdz7XIe1_bXCldzA16rkdbN75CsfeivGx1KqweaGY-qW6dj3i9g; SPYDUS_SESSIONID_443=; LASTLOGINDATE_443=; LASTLOGINTIME_443=; ASPLOGININFO_443=
Origin: https://sunshinecoast.spydus.com
Referer: https://sunshinecoast.spydus.com/spydus
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
X-Requested-With: XMLHttpRequest
X-XSRF-TOKEN: CfDJ8OeUeirWOaNDuqbzxnjDY16BeBe1cBp2glt1mA2b_2raxAoEv_kjZPb5CiueQz9I9ZUkz4teVi-TDHvVOjuCksvLEe-1lSCzkmfWHdz7XIe1_bXCldzA16rkdbN75CsfeivGx1KqweaGY-qW6dj3i9g
sec-ch-ua: "Microsoft Edge";v="129", "Not=A?Brand";v="8", "Chromium";v="129"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Content-Type: application/json; charset=UTF-8

{
  "UserName": "PAL",
  "Password": "sE1Ci3Es8Y/GJ4SthjHjO4uDoSpcmAH3z/ld7PF+SY0cwEDWrwsOS2Nn+uBAOutHPSL/ibwLIEnOwxdcON4AQehNVJ2gl5bzB4hJgVQJnlU7d/lU+7SVqMPqlWZl54iZu9cXnOTKycMVDfeDwAHtoJ4whRH/nOygKe7dOgMZVhE=",
  "NewPassword": "",
  "ConfPassword": "",
  "SpydusSSO": "",
  "SpydusSSOUTC": "",
  "isForced": false,
  "AuthKey": "",
  "Location": 7947981,
  "SubLocation": 7949212
}

###

