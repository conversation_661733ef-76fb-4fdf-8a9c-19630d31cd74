@startuml

class JzkitZ3950Client {
  void init(RecordSource recordSource)
  void query(String query)
}

class Z3950ServiceFactory {
}

class Z3950Origin {
}

class ZEndpoint {
}

abstract class CustomerRecordsClient {
  List<Record> retrieveMatchingRecords(OpenTitleOrder titleOrder)
  {abstract} List<Record> titleAndAuthorSearch(OpenTitleOrder titleOrder)
  {abstract} List<Record> productIdSearch(OpenTitleOrder titleOrder)
}

class Z3950ClientManager {
  protected List<Record> productIdSearch(OpenTitleOrder titleOrder)
  List<Record> titleAndAuthorSearch(OpenTitleOrder titleOrder)
  List<Record> localNumberSearch(OpenTitleOrder titleOrder)
}

class LiberoClient {
}

class Thread {
}

interface Searchable

CustomerRecordsClient <|== Z3950ClientManager
CustomerRecordsClient <|== LiberoClient
JzkitZ3950Client o-right-- Searchable
JzkitZ3950Client =left= Z3950ServiceFactory
Searchable <.. Z3950Origin
Z3950Origin o-- ZEndpoint
Thread <|== ZEndpoint
Z3950ClientManager o-- JzkitZ3950Client : client
@enduml
