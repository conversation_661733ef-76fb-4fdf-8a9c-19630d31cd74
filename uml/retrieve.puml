@startuml

class RetrieveService {
  -context: ApplicationContext
  -clients: Map<String, CustomerRecordsClient>
  +void init()
  +void retrieve(customerCode: String, titleOrders: List<String>)
}

abstract class CustomerRecordsClient {
}

class Z3950ClientManager {
}

class LiberoClient {
}

RetrieveService - CustomerRecordsClient : clients
CustomerRecordsClient <|-- Z3950ClientManager
CustomerRecordsClient <|-- LiberoClient

@enduml
