@startuml
'https://plantuml.com/sequence-diagram

!pragma teoz true
box "Lucy_client"
    participant AddCustomerHoldingWizardModel
    box "AddCustomerHoldingWizardPage"
        participant ProductsReceiptsPage
        participant AcquisitionDetailsPage
        participant ReservationsPage
        participant AllocationsPage
        participant CopyDetailsPage
    end box
end box

group createAcquisition\n(((AItemReceiptAssignment) selectionIterator.next())\n.getItemReceiptAssignment())
ProductsReceiptsPage -> AddCustomerHoldingWizardModel: createAcquisition(ItemReceiptAssignment itemReceiptAssignment)
alt holding==null
AddCustomerHoldingWizardModel -> AddCustomerHoldingWizardModel: buildHolding(itemReceiptAssignment)
end
AddCustomerHoldingWizardModel -> AddCustomerHoldingWizardModel: buildAcquisition(ItemReceiptAssignment itemReceiptAssignment)
AddCustomerHoldingWizardModel -> AddCustomerHoldingWizardModel: addReceiptToAcquisition\n(ItemReceiptAssignment itemReceiptAssignment, Acqusition acquisition)
end

ProductsReceiptsPage -> AcquisitionDetailsPage:

AcquisitionDetailsPage -> ReservationsPage

ReservationsPage -> AddCustomerHoldingWizardModel: addReservationAllocations(reservationAllocations)

ReservationsPage -> AllocationsPage

AllocationsPage -> AddCustomerHoldingWizardModel: createCopies()
AllocationsPage -> AddCustomerHoldingWizardModel: autoAllocateAcquisition\n(final Acquisition acquisition, BibTemplate allocationsTemplate)
AddCustomerHoldingWizardModel -> Lucy_javaee7: get AutomaticAllocations(acquisition, allocationsTemplate)

AllocationsPage -> CopyDetailsPage

CopyDetailsPage -> Lucy_javaee7: addHolding(customerId, bibCollectionType, holding)

@enduml