@startuml
actor Client
boundary BatchController

Client->Bat<PERSON><PERSON>ontroller : **POST** /api/batches - crerateBatch
Client<--BatchController : ""**201** CREATED""
Client->BatchController : **POST** /api/batches/find - find
Client->BatchController : **GET**  /api/batches/{batchId} - getBatch
Client->BatchController : **POST**  /api/batches/{batchId}/{customerCode} - addBibRecords
Client->BatchController : **DELETE**   /api/batches - deleteBatch
Client->BatchController : **PUT** /api/batches - updateBatch
Client->BatchController : **POST** /api/batches/export - export
Client->BatchController : **GET** /api/batches/downloadFile/{fileName}

@enduml
