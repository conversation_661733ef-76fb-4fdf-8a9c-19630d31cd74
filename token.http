# curl 'https://yprl.spydus.com/spydus/antiforgery/token'
#  -H 'sec-ch-ua: "Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"'
#  -H 'Referer: https://yprl.spydus.com/spydus'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'Authorization: undefined'
#  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36'
#  -H 'sec-ch-ua-platform: "Windows"'
GET https://yprl.spydus.com/spydus/antiforgery/token
sec-ch-ua: "Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"
Referer: https://yprl.spydus.com/spydus
sec-ch-ua-mobile: ?0
Authorization: undefined
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36
sec-ch-ua-platform: "Windows"

###

